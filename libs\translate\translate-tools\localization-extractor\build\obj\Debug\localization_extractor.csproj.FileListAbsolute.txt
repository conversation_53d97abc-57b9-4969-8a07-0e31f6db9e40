D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-extractor\build\obj\Debug\localization_extractor.csproj.AssemblyReference.cache
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-extractor\build\obj\Debug\localization_extractor.GeneratedMSBuildEditorConfig.editorconfig
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-extractor\build\obj\Debug\localization_extractor.AssemblyInfoInputs.cache
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-extractor\build\obj\Debug\localization_extractor.AssemblyInfo.cs
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-extractor\build\obj\Debug\localization_extractor.csproj.CoreCompileInputs.cache
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-extractor\build\obj\Debug\localization_extractor.sourcelink.json
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-extractor\build\Debug\localization_extractor.exe
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-extractor\build\Debug\localization_extractor.deps.json
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-extractor\build\Debug\localization_extractor.runtimeconfig.json
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-extractor\build\Debug\localization_extractor.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-extractor\build\Debug\localization_extractor.pdb
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-extractor\build\Debug\CommandLine.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-extractor\build\Debug\CommunityToolkit.Mvvm.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-extractor\build\Debug\Microsoft.CodeAnalysis.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-extractor\build\Debug\Microsoft.CodeAnalysis.CSharp.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-extractor\build\Debug\Microsoft.CodeAnalysis.CSharp.Scripting.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-extractor\build\Debug\Microsoft.CodeAnalysis.Scripting.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-extractor\build\Debug\Newtonsoft.Json.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-extractor\build\Debug\cs\Microsoft.CodeAnalysis.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-extractor\build\Debug\de\Microsoft.CodeAnalysis.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-extractor\build\Debug\es\Microsoft.CodeAnalysis.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-extractor\build\Debug\fr\Microsoft.CodeAnalysis.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-extractor\build\Debug\it\Microsoft.CodeAnalysis.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-extractor\build\Debug\ja\Microsoft.CodeAnalysis.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-extractor\build\Debug\ko\Microsoft.CodeAnalysis.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-extractor\build\Debug\pl\Microsoft.CodeAnalysis.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-extractor\build\Debug\pt-BR\Microsoft.CodeAnalysis.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-extractor\build\Debug\ru\Microsoft.CodeAnalysis.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-extractor\build\Debug\tr\Microsoft.CodeAnalysis.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-extractor\build\Debug\zh-Hans\Microsoft.CodeAnalysis.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-extractor\build\Debug\zh-Hant\Microsoft.CodeAnalysis.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-extractor\build\Debug\cs\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-extractor\build\Debug\de\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-extractor\build\Debug\es\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-extractor\build\Debug\fr\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-extractor\build\Debug\it\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-extractor\build\Debug\ja\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-extractor\build\Debug\ko\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-extractor\build\Debug\pl\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-extractor\build\Debug\pt-BR\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-extractor\build\Debug\ru\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-extractor\build\Debug\tr\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-extractor\build\Debug\zh-Hans\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-extractor\build\Debug\zh-Hant\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-extractor\build\Debug\cs\Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-extractor\build\Debug\de\Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-extractor\build\Debug\es\Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-extractor\build\Debug\fr\Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-extractor\build\Debug\it\Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-extractor\build\Debug\ja\Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-extractor\build\Debug\ko\Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-extractor\build\Debug\pl\Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-extractor\build\Debug\pt-BR\Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-extractor\build\Debug\ru\Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-extractor\build\Debug\tr\Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-extractor\build\Debug\zh-Hans\Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-extractor\build\Debug\zh-Hant\Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-extractor\build\Debug\cs\Microsoft.CodeAnalysis.Scripting.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-extractor\build\Debug\de\Microsoft.CodeAnalysis.Scripting.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-extractor\build\Debug\es\Microsoft.CodeAnalysis.Scripting.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-extractor\build\Debug\fr\Microsoft.CodeAnalysis.Scripting.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-extractor\build\Debug\it\Microsoft.CodeAnalysis.Scripting.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-extractor\build\Debug\ja\Microsoft.CodeAnalysis.Scripting.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-extractor\build\Debug\ko\Microsoft.CodeAnalysis.Scripting.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-extractor\build\Debug\pl\Microsoft.CodeAnalysis.Scripting.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-extractor\build\Debug\pt-BR\Microsoft.CodeAnalysis.Scripting.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-extractor\build\Debug\ru\Microsoft.CodeAnalysis.Scripting.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-extractor\build\Debug\tr\Microsoft.CodeAnalysis.Scripting.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-extractor\build\Debug\zh-Hans\Microsoft.CodeAnalysis.Scripting.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-extractor\build\Debug\zh-Hant\Microsoft.CodeAnalysis.Scripting.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-extractor\build\Debug\localizationlib.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-extractor\build\Debug\localizationlib.pdb
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-extractor\build\obj\Debug\localiza.CB9E91E9.Up2Date
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-extractor\build\obj\Debug\localization_extractor.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-extractor\build\obj\Debug\refint\localization_extractor.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-extractor\build\obj\Debug\localization_extractor.pdb
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-extractor\build\obj\Debug\localization_extractor.genruntimeconfig.cache
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-extractor\build\obj\Debug\ref\localization_extractor.dll
