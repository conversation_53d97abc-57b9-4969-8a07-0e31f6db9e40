D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\localizationlib\Debug\localizationlib.deps.json
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\localizationlib\Debug\localizationlib.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\localizationlib\Debug\localizationlib.pdb
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\localizationlib\obj\Debug\localizationlib.csproj.AssemblyReference.cache
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\localizationlib\obj\Debug\localizationlib.GeneratedMSBuildEditorConfig.editorconfig
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\localizationlib\obj\Debug\localizationlib.AssemblyInfoInputs.cache
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\localizationlib\obj\Debug\localizationlib.AssemblyInfo.cs
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\localizationlib\obj\Debug\localizationlib.csproj.CoreCompileInputs.cache
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\localizationlib\obj\Debug\localizationlib.sourcelink.json
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\localizationlib\obj\Debug\localizationlib.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\localizationlib\obj\Debug\refint\localizationlib.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\localizationlib\obj\Debug\localizationlib.pdb
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\localizationlib\obj\Debug\ref\localizationlib.dll
