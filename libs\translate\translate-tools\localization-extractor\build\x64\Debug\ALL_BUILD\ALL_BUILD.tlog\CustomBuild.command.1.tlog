^D:\KIBISOFT\WORKAREA\GIT\INTEROP\LIBS\TRANSLATE\TRANSLATE-TOOLS\LOCALIZATION-EXTRACTOR\CMAKELISTS.TXT
setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SD:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-extractor -BD:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-extractor/build --check-stamp-file D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-extractor/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
