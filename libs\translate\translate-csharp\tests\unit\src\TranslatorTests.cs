﻿﻿﻿﻿using NUnit.Framework;
using System.Collections.Generic;
using System.IO;
using System.Linq;

namespace Alcon.Interop.Translate.Tests
{
    [TestFixture]
    public class TranslatorXmlTests
    {
        private Translator _translator;
        private string _spanishFilePath;
        private string _romanianFilePath;
        private string _sameTextFilePath;
        private string _duplicateIdsFilePath;

        [SetUp]
        public void SetUp()
        {
            // Create a fresh Translator before each test
            _translator = new Translator();

            // Point to the XML file in a "TestData" folder within your test project output
            var testDataDir = Path.Combine(TestContext.CurrentContext.TestDirectory, "..", "..", "..", "etc");
            testDataDir = Path.GetFullPath(testDataDir); // Normalize the path
            _spanishFilePath = Path.Combine(testDataDir, "PlanWidget_es.xml");
            _romanianFilePath = Path.Combine(testDataDir, "PlanWidget_ro.xml");
            _translator = Translator.Instance;
        }

        [Test]
        public void Initialize_ShouldLoadTranslations_AndSetAvailableLanguages()
        {
            // Arrange
            var translationFiles = new List<string> { _spanishFilePath, _romanianFilePath };

            // Act
            _translator.Initialize(translationFiles, "Spanish");

            // Assert
            Assert.AreEqual("Spanish", _translator.CurrentUserLanguage, "Current user language mismatch.");
            Assert.IsTrue(_translator.AvailableLanguages.Count >= 2, "AvailableLanguages should include at least Spanish and Romanian.");
            Assert.IsTrue(_translator.AvailableLanguages.Any(a=> a.Name == "Spanish"), "Spanish language is not loaded.");
            Assert.IsTrue(_translator.AvailableLanguages.Any(a => a.Name == "Romanian"), "Romanian language is not loaded.");


        }

        [Test]
        public void Translate_ShouldReturnTranslatedText_WhenTextIdExists()
        {
            // Arrange
            _translator.Initialize(new List<string> { _spanishFilePath }, "Spanish");
            const string textId = "-1286148484"; // Temporal_es
            const string englishText = "Temporal";

            // Act
            var result = _translator.Translate(textId, englishText);

            // Assert
            Assert.AreEqual("Temporal_es", result, "Translation mismatch for 'Temporal' in Spanish.");
        }


        [Test]
        public void Translate_ShouldReturnTranslatedDecodedText()
        {
            // Arrange
            _translator.Initialize(new List<string> { _spanishFilePath }, "Spanish");
            const string textId = "1363271264"; // Temporal_es
            const string englishText = "decoded text";

            // Act
            var result = _translator.Translate(textId, englishText);

            // Assert
            Assert.AreEqual("Show & Edit Details", result, "Translation mismatch text not decoded");
        }

        [Test]
        public void Translate_ShouldFallbackToEnglish_WhenLanguageNotLoaded()
        {
            // Arrange
            _translator.Initialize(new List<string> { _spanishFilePath }, "French"); // French is not loaded
            const string textId = "-1286148484"; // Temporal
            const string englishText = "Temporal";

            // Act
            var result = _translator.Translate(textId, englishText);

            // Assert
            Assert.AreEqual("Temporal", result, "Fallback to English failed for 'Temporal'.");
        }

        [Test]
        public void Translate_ShouldFallbackToEnglish_WhenIDNotExist()
        {
            // Arrange
            _translator.Initialize(new List<string> { _spanishFilePath }, "Spanish"); 
            const string textId = "-1286148111"; // Not Exist
            const string englishText = "Not Exist";

            // Act
            var result = _translator.Translate(textId, englishText);

            // Assert
            Assert.AreEqual("Not Exist", result, "Fallback to English failed for 'Temporal'.");
        }

        [Test]
        public void UpdateUI_ShouldTriggerPropertyChanged()
        {
            // Arrange
            bool propertyChangedCalled = false;
            _translator.PropertyChanged += (sender, args) =>
            {
                if (args.PropertyName == nameof(_translator.CurrentUserLanguage))
                {
                    propertyChangedCalled = true;
                }
            };

            // Act
            _translator.UpdateUI();

            // Assert
            Assert.IsTrue(propertyChangedCalled, "PropertyChanged event for CurrentUserLanguage was not triggered.");
        }

        [Test]
        public void TranslateM_ShouldReturnMetadata_WhenIdDoesNotExist()
        {
            // Arrange
            _translator.Initialize(new List<string> { _spanishFilePath }, "Spanish");
            const string metadata = "NonExistentId";

            // Act
            var result = _translator.TranslateM(metadata);

            // Assert
            Assert.AreEqual(metadata, result, "TranslateM should return metadata when the ID does not exist.");
        }

        [Test]
        public void Initialize_WithInvalidFile_ShouldHandleErrorGracefully()
        {
            // Arrange
            var invalidFilePath = Path.Combine(TestContext.CurrentContext.TestDirectory, "InvalidFile.xml");

            // Act
            TestDelegate action = () => _translator.Initialize(new List<string> { invalidFilePath });

            // Assert
            Assert.DoesNotThrow(action, "Initialize should not throw an exception for invalid files.");
        }

        [Test]
        public void Translate_ShouldHandleMultipleLanguagesSimultaneously()
        {
            // Arrange
            _translator.Initialize(new List<string> { _spanishFilePath, _romanianFilePath });

            const string spanishTextId = "-1286148484"; // Temporal_es
            const string romanianTextId = "-2133836346"; // Eroare: Inciziile se suprapun

            // Act
            _translator.CurrentUserLanguage = "Spanish";
            var spanishResult = _translator.Translate(spanishTextId, "Temporal");

            _translator.CurrentUserLanguage = "Romanian";
            var romanianResult = _translator.Translate(romanianTextId, "Error: Incisions Overlap");

            // Assert
            Assert.AreEqual("Temporal_es", spanishResult, "Spanish translation mismatch for 'Temporal'.");
            Assert.AreEqual("Eroare: Inciziile se suprapun", romanianResult, "Romanian translation mismatch for 'Error: Incisions Overlap'.");
        }

        [Test]
        public void TranslateM_WithValidMetadata_ShouldReturnTranslatedText()
        {
            // Arrange
            _translator.Initialize(new List<string> { _spanishFilePath }, "Spanish");
            const string metadata = "id:-1286148484|en:Temporal|f:font14|w:170|h:25|cf:false|wr:false";

            // Act
            var result = _translator.TranslateM(metadata);

            // Assert
            Assert.AreEqual("Temporal_es", result, "TranslateM failed to return the correct translation for metadata.");
        }

        [Test]
        public void TranslateM_WithInvalidMetadata_ShouldReturnText()
        {
            // Arrange
            _translator.Initialize(new List<string> { _spanishFilePath }, "Spanish");
            const string metadata = "id:NonExistent|en:NonExistent";

            // Act
            var result = _translator.TranslateM(metadata);

            // Assert
            Assert.AreEqual("NonExistent", result, "TranslateM should return the metadata when the translation is not found.");
        }

        [Test]
        public void TryGetValue_ShouldExtractValueFromMetadata()
        {
            // Arrange
            const string metadata = "id:-1286148484|en:Temporal|f:font14|w:170|h:25|cf:false|wr:false";

            // Act
            bool idExists = TranslateMetadata.TryGetValue(metadata, TranslateMetadata.IdPrefix, out string idValue);
            bool fontExists = TranslateMetadata.TryGetValue(metadata, TranslateMetadata.FontPrefix, out string fontValue);
            bool invalidExists = TranslateMetadata.TryGetValue(metadata, "invalid:", out string invalidValue);

            // Assert
            Assert.IsTrue(idExists, "Id prefix was not found in metadata.");
            Assert.AreEqual("-1286148484", idValue, "Id value mismatch.");
            Assert.IsTrue(fontExists, "Font prefix was not found in metadata.");
            Assert.AreEqual("font14", fontValue, "Font value mismatch.");
            Assert.IsFalse(invalidExists, "Invalid prefix should not exist in metadata.");
            Assert.IsNull(invalidValue, "Invalid prefix should return null for the value.");
        }

        [Test]
        public void TranslateM_WithPlaceholders_ShouldFormatTranslationCorrectly_TextNotExits()
        {
            // Arrange
            _translator.Initialize(new List<string> { _spanishFilePath }, "Spanish");
            const string metadata = "id:PlaceholderTest|en:Hi %[String], you have %[Number] messages.|f:font14";
            var placeholders = new[] { "Maria", "5" };

            // Act
            var result = _translator.TranslateM(metadata, placeholders);

            // Assert
            Assert.AreEqual("Hi Maria, you have 5 messages.", result, "TranslateM failed to format placeholders correctly.");
        }

        [Test]
        public void TranslateM_WithPlaceholders_ShouldFormatTranslationCorrectly_TextExits_Spanish()
        {
            // Arrange
            _translator.Initialize(new List<string> { _spanishFilePath }, "Spanish");
            const string metadata = "id:1757935796|en:Hi %[String], you have %[Number] messages.|f:font14";
            var placeholders = new[] { "Maria", "5" };

            // Act
            var result = _translator.TranslateM(metadata, placeholders);

            // Assert
            Assert.AreEqual("Hola Maria, tienes 5 mensajes.", result, "TranslateM failed to format placeholders correctly.");
        }

        [Test]
        public void TranslateM_WithPlaceholders_ShouldFormatTranslationCorrectly_TextExits_Romanian()
        {
            // Arrange
            _translator.Initialize(new List<string> { _romanianFilePath }, "Romanian");
            const string metadata = "id:1757935796|en:Hi %[String], you have %[Number] messages.|f:font14";
            var placeholders = new[] { "Maria", "5" };

            // Act
            var result = _translator.TranslateM(metadata, placeholders);

            // Assert
            Assert.AreEqual("Bună Maria, aveți 5 mesaje.", result, "TranslateM failed to format placeholders correctly.");
        }
        [Test]
        public void TranslateM_WithPlaceholders_ShouldFormatTranslationCorrectly_TextExits_WithoutId()
        {
            // Arrange
            _translator.Initialize(new List<string> { _spanishFilePath }, "Spanish");
            const string metadata = "en:Hi %[String], you have %[Number] messages.|f:font14";
            var placeholders = new[] { "Maria", "5" };

            // Act
            var result = _translator.TranslateM(metadata, placeholders);

            // Assert
            Assert.AreEqual("Hola Maria, tienes 5 mensajes.", result, "TranslateM failed to format placeholders correctly.");
        }

        [Test]
        public void TranslateM_WithPlaceholders_ShouldFormatTranslationCorrectly_WithText()
        {
            // Arrange
            _translator.Initialize(new List<string> { _spanishFilePath }, "Spanish");
            const string metadata = "Hi %[String], you have %[Number] messages.";
            var placeholders = new[] { "Maria", "5" };

            // Act
            var result = _translator.TranslateM(metadata, placeholders);

            // Assert
            Assert.AreEqual("Hi Maria, you have 5 messages.", result, "TranslateM failed to format placeholders correctly.");
        }

        [Test]
        public void Translate_WithMetadata_ShouldFallbackToEnglishWhenLanguageNotLoaded()
        {
            // Arrange
            _translator.Initialize(new List<string> { _spanishFilePath }, "French"); // French not loaded
            const string metadata = "id:-1286148484|en:Temporal|f:font14";

            // Act
            var result = _translator.TranslateM(metadata);

            // Assert
            Assert.AreEqual("Temporal", result, "Fallback to English failed for TranslateM with metadata.");
        }

        [Test]
        public void Initialize_WithDuplicateIds_ShouldUseLastLoadedTranslation()
        {
            // Arrange
            _duplicateIdsFilePath = Path.Combine(TestContext.CurrentContext.TestDirectory, "..", "..", "..", "etc", "PlanWidget_duplicate_ids.xml");
            _duplicateIdsFilePath = Path.GetFullPath(_duplicateIdsFilePath);
            _translator.Initialize(new List<string> { _duplicateIdsFilePath }, "French");

            // Act
            // Test the duplicate ID (-1286148484) which appears in both Group1 and Group2
            var result = _translator.Translate("-1286148484", "Temporal");

            // Assert
            // Should use the last loaded translation (from Group2)
            Assert.AreEqual("Temporal_Group2", result, "Translation should use the last loaded value for duplicate IDs");
        }

        [Test]
        public void Initialize_WithDuplicateIds_ShouldPreserveUniqueIds()
        {
            // Arrange
            _duplicateIdsFilePath = Path.Combine(TestContext.CurrentContext.TestDirectory, "..", "..", "..", "etc", "PlanWidget_duplicate_ids.xml");
            _duplicateIdsFilePath = Path.GetFullPath(_duplicateIdsFilePath);
            _translator.Initialize(new List<string> { _duplicateIdsFilePath }, "French");

            // Act & Assert
            // Test unique IDs from both groups
            var result1 = _translator.Translate("1885090865", "Test Text 1");
            var result2 = _translator.Translate("1885090866", "Test Text 2");

            Assert.AreEqual("Test_Text_1", result1, "Translation for unique ID in Group1 should be preserved");
            Assert.AreEqual("Test_Text_2", result2, "Translation for unique ID in Group2 should be preserved");
        }

        [Test]
        public void TranslateM_WithDuplicateIds_ShouldUseLastLoadedProperties()
        {
            // Arrange
            _duplicateIdsFilePath = Path.Combine(TestContext.CurrentContext.TestDirectory, "..", "..", "..", "etc", "PlanWidget_duplicate_ids.xml");
            _duplicateIdsFilePath = Path.GetFullPath(_duplicateIdsFilePath);
            _translator.Initialize(new List<string> { _duplicateIdsFilePath }, "French");

            // Create metadata with the duplicate ID and different properties
            const string metadata = "id:-1286148484|en:Temporal|f:font14Bold|w:200|h:30|cf:true|wr:true";

            // Act
            var result = _translator.TranslateM(metadata);

            // Assert
            // Should return the translation from Group2 which has the matching properties
            Assert.AreEqual("Temporal_Group2", result, "TranslateM should use the last loaded translation and properties for duplicate IDs");
        }

        [Test]
        public void Translate_WithSameTextDifferentProperties_ShouldUseCorrectId()
        {
            // Arrange
            _sameTextFilePath = Path.Combine(TestContext.CurrentContext.TestDirectory, "..", "..", "..", "etc", "PlanWidget_same_text.xml");
            _sameTextFilePath = Path.GetFullPath(_sameTextFilePath);
            _translator.Initialize(new List<string> { _sameTextFilePath }, "German");

            // Act
            // Test Button_Text with different properties
            var buttonResult1 = _translator.Translate("1001", "Click Here");
            var buttonResult2 = _translator.Translate("2001", "Click Here");

            // Assert
            Assert.AreEqual("Button_Text", buttonResult1, "Translation for button with font14 properties should be returned");
            Assert.AreEqual("Button_Text", buttonResult2, "Translation for button with font18 properties should be returned");
            Assert.AreNotSame(buttonResult1, buttonResult2, "Although text is same, references should be different");
        }

        [Test]
        public void TranslateM_WithSameTextDifferentProperties_ShouldRespectMetadataProperties()
        {
            // Arrange
            _sameTextFilePath = Path.Combine(TestContext.CurrentContext.TestDirectory, "..", "..", "..", "etc", "PlanWidget_same_text.xml");
            _sameTextFilePath = Path.GetFullPath(_sameTextFilePath);
            _translator.Initialize(new List<string> { _sameTextFilePath }, "German");

            // Create metadata with different properties
            const string headerMetadata1 = "id:1002|en:Welcome|f:font14|w:170|h:25|cf:false|wr:false";
            const string headerMetadata2 = "id:2002|en:Welcome|f:font14Bold|w:300|h:50|cf:true|wr:true";

            // Act
            var headerResult1 = _translator.TranslateM(headerMetadata1);
            var headerResult2 = _translator.TranslateM(headerMetadata2);

            // Assert
            Assert.AreEqual("Header_Text", headerResult1, "Translation for header with standard properties should be returned");
            Assert.AreEqual("Header_Text", headerResult2, "Translation for header with bold properties should be returned");
            Assert.AreNotSame(headerResult1, headerResult2, "Although text is same, references should be different");
        }

        [Test]
        public void TranslateM_WithSameTextDifferentProperties_ShouldFallbackToEnglishWithCorrectProperties()
        {
            // Arrange
            _sameTextFilePath = Path.Combine(TestContext.CurrentContext.TestDirectory, "..", "..", "..", "etc", "PlanWidget_same_text.xml");
            _sameTextFilePath = Path.GetFullPath(_sameTextFilePath);
            _translator.Initialize(new List<string> { _sameTextFilePath }, "French"); // French not loaded

            // Create metadata with different properties
            const string metadata1 = "id:1001|en:Click Here|f:font14|w:100|h:25|cf:false|wr:false";
            const string metadata2 = "id:2001|en:Click Here|f:font18|w:200|h:40|cf:true|wr:true";

            // Act
            var result1 = _translator.TranslateM(metadata1);
            var result2 = _translator.TranslateM(metadata2);

            // Assert
            Assert.AreEqual("Click Here", result1, "Should fallback to English text while preserving font14 properties");
            Assert.AreEqual("Click Here", result2, "Should fallback to English text while preserving font18 properties");
            Assert.AreNotSame(result1, result2, "Although text is same, references should be different due to different properties");
        }

    }
}
