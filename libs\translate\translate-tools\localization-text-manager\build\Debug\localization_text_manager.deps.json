{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {"localization_text_manager/1.0.0": {"dependencies": {"CommunityToolkit.Mvvm": "8.2.2", "translate_csharp": "1.0.0", "ui_extractor_tool": "1.0.0"}, "runtime": {"localization_text_manager.dll": {}}}, "CommunityToolkit.Mvvm/8.2.2": {"runtime": {"lib/net6.0/CommunityToolkit.Mvvm.dll": {"assemblyVersion": "8.2.0.0", "fileVersion": "8.2.2.1"}}}, "Newtonsoft.Json/13.0.3": {"runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"assemblyVersion": "13.0.0.0", "fileVersion": "13.0.3.27908"}}}, "translate_csharp/1.0.0": {"runtime": {"translate_csharp.dll": {"assemblyVersion": "1.0.0", "fileVersion": "*******"}}}, "ui_extractor_tool/1.0.0": {"dependencies": {"CommunityToolkit.Mvvm": "8.2.2", "Newtonsoft.Json": "13.0.3"}, "runtime": {"ui_extractor_tool.dll": {"assemblyVersion": "1.0.0", "fileVersion": "*******"}}}}}, "libraries": {"localization_text_manager/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "CommunityToolkit.Mvvm/8.2.2": {"type": "package", "serviceable": true, "sha512": "sha512-r0g0k9tGYdrnz8R7T3x5UiokDffeevzK/2P/9SBL6fqLgN8B157MIi/bVUWI1KAz6ZorZrK9AdABCWUeXZZsvA==", "path": "communitytoolkit.mvvm/8.2.2", "hashPath": "communitytoolkit.mvvm.8.2.2.nupkg.sha512"}, "Newtonsoft.Json/13.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "path": "newtonsoft.json/13.0.3", "hashPath": "newtonsoft.json.13.0.3.nupkg.sha512"}, "translate_csharp/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "ui_extractor_tool/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}}}