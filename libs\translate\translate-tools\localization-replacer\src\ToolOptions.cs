/*
 * Copyright 2021-present Alcon. All rights reserved.
 * Alcon proprietary/confidential.
 * Use is subject to license terms.
 */

using CommandLine;

namespace LocalizationReplacer;

public class ToolOptions
{
    [Option('p', "projectPath", Required = true,
        HelpText = "Path to the project folder containing Cs/XAML files.")]
    public string ProjectPath { get; set; }

    [Option('t', "fileTypes", Required = false, Default = "both",
    HelpText = "Which file types to extract. Valid values: 'xaml', 'cs', 'both'")]
    public string FileTypes { get; set; }
}
