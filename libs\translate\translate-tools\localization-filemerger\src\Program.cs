/*
 * Copyright 2021-present Alcon. All rights reserved.
 * Alcon proprietary/confidential.
 * Use is subject to license terms.
 */

using CommandLine;
using LocalizationLib;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;

namespace LocalizationFileMerger;

class Program
{
    public class Options
    {
        [Option('i', "input", Required = true, Min = 2, HelpText = "Paths to the input files (base file and the updated file).")]
        public IEnumerable<string> InputFiles { get; set; } = [];

        [Option('o', "output", Required = false, HelpText = "Path to the output merged file.")]
        public string OutputFile { get; set; } = string.Empty;

        [Option('f', "forceMerge", Required = false, Default = false, HelpText = "If set to true forces to merge the differences from the updated file into the base file.")]
        public bool ForceMerge { get; set; }
    }


    static void Main(string[] args)
    {
        // Preprocess arguments to allow optional "-i"
        var processedArgs = PreprocessArguments(args);

        Parser.Default.ParseArguments<Options>(processedArgs)
            .WithParsed(RunLanguageUpdate)
            .WithNotParsed(HandleParseError);
    }

    private static void RunLanguageUpdate(Options opts)
    {
        var inputFiles = opts.InputFiles;
        if (inputFiles == null || inputFiles.Count() < 2)
        {
            Console.WriteLine("Error: You must provide 2 input files.");
            return;
        }

        string baseFile = inputFiles.ElementAt(0);
        string updatedFile = inputFiles.ElementAt(1);

        if (opts.OutputFile == null || opts.OutputFile.Length == 0)
        {
            opts.OutputFile = "merged" + Path.GetExtension(baseFile);
        }

        FileMerge fileMerge = new();
        try
        {
            fileMerge.Merge(baseFile, updatedFile, opts.OutputFile, opts.ForceMerge);
        }
        catch (Exception ex)
        {
            Console.WriteLine("Error: " + ex.Message);
        }
    }

    private static void HandleParseError(IEnumerable<Error> errors)
    {

    }

    private static IEnumerable<string> PreprocessArguments(string[] args)
    {
        var argsList = args.ToList();
        int forceMergeIndex = argsList.IndexOf("-f");

        // If "-o" argument is set and "-i" not set, assume all remaining arguments are inputs
        int outputIndex = argsList.IndexOf("-o");
        int inputIndex = argsList.IndexOf("-i");

        if (inputIndex == -1 && outputIndex != -1 && outputIndex + 2 < argsList.Count)
        {
            // Extract output file and only the first 3 remaining arguments
            string outputFile = argsList[outputIndex + 1];
            List<string> remainingArgs = argsList.Skip(outputIndex + 2).ToList();

            // if the first remaining argument isn't '-i'
            if (remainingArgs.Count > 0 && remainingArgs[0] != "-i")
            {
                remainingArgs.Insert(0, "-i");
            }
            // Reconstruct the argument list
            if (forceMergeIndex != -1 && forceMergeIndex < outputIndex)
            {
                return new List<string> { "-f", "-o", outputFile }.Concat(remainingArgs);
            }
            else
            {
                return new List<string> { "-o", outputFile }.Concat(remainingArgs);
            }
        }
        return args; // Return original if no modification is needed
    }
}