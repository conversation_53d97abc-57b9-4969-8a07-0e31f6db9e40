/*
 * Copyright 2021-present Alcon. All rights reserved.
 * Alcon proprietary/confidential.
 * Use is subject to license terms.
 */

using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;

namespace LocalizationLib;

/// <summary>
/// Extracts user-facing text from C++ source files using either regex-based or consolidated processing.
/// </summary>
public class CppExtractor : IExtractor
{
    private readonly ICppProcessor _processor;
    private readonly string _projectPath;
    private readonly ILlmClient _llmClient;

    public CppExtractor(string projectPath = null, string cppProcessorOption = "consolidated", ILlmClient llmClient = null)
    {
        _projectPath = projectPath;
        _llmClient = llmClient;

        // Initialize the appropriate processor based on the option
        _processor = cppProcessorOption.ToLower() switch
        {
            "regex" => new RegexCppProcessor(),
            "consolidated" => new ConsolidatedCppProcessor(projectPath, llmClient),
            "llm" => throw new NotImplementedException("LLM-only processor not implemented yet"),
            _ => throw new ArgumentException($"Invalid C++ processor option: {cppProcessorOption}. Valid options are: 'regex', 'consolidated'")
        };
    }

    public (List<FileProcessingResult> fileResults, List<ReplacementRecord> replacements) Extract(string projectPath)
    {
        Console.WriteLine("Starting C++ text extraction...");
        
        var fileResults = new List<FileProcessingResult>();
        var allReplacements = new List<ReplacementRecord>();

        // Get all C++ files in the project
        var cppFiles = GetCppFiles(projectPath);
        Console.WriteLine($"Found {cppFiles.Count} C++ files to process");

        // Process each file
        foreach (var file in cppFiles)
        {
            try
            {
                Console.WriteLine($"\nProcessing file: {file}");
                var groupId = GetGroupId(file, projectPath);
                
                var result = _processor.ProcessCppFile(file, groupId);
                if (result.Replacements.Any())
                {
                    fileResults.Add(result);
                    allReplacements.AddRange(result.Replacements);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error processing file {file}: {ex.Message}");
                fileResults.Add(new FileProcessingResult
                {
                    FilePath = file,
                    Notes = new List<string> { $"Error: {ex.Message}" }
                });
            }
        }

        Console.WriteLine($"\nC++ extraction complete. Found {allReplacements.Count} strings in {fileResults.Count} files.");
        return (fileResults, allReplacements);
    }

    private List<string> GetCppFiles(string projectPath)
    {
        var cppExtensions = new[] { ".cpp", ".h", ".hpp", ".cxx", ".hxx" };
        var files = new List<string>();

        try
        {
            // Get all files recursively
            var allFiles = Directory.GetFiles(projectPath, "*.*", SearchOption.AllDirectories);
            
            // Filter for C++ files
            files.AddRange(allFiles.Where(f => cppExtensions.Contains(Path.GetExtension(f).ToLower())));
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error getting C++ files: {ex.Message}");
        }

        return files;
    }

    private string GetGroupId(string filePath, string projectPath)
    {
        // Get the relative path from the project root
        var relativePath = Path.GetRelativePath(projectPath, filePath);
        
        // Use the directory structure as the group ID
        var directory = Path.GetDirectoryName(relativePath);
        if (string.IsNullOrEmpty(directory))
        {
            return Path.GetFileNameWithoutExtension(filePath);
        }

        // Convert directory separators to dots and remove any leading/trailing dots
        return directory.Replace(Path.DirectorySeparatorChar, '.')
                       .Replace(Path.AltDirectorySeparatorChar, '.')
                       .Trim('.');
    }
} 