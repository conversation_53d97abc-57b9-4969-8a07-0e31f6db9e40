
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineSystem.cmake:205 (message)"
      - "CMakeLists.txt:3 (project)"
    message: |
      The system is: Windows - 10.0.26100 - AMD64
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCSharpCompiler.cmake:23 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Compiling the CSharp compiler identification source file "CMakeCSharpCompilerId.cs" succeeded.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      0
      MSBuild version 17.11.9+a69bbaaf5 for .NET Framework
      Build started 5/5/2025 10:41:11 AM.
      
      Project "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-replacer\\build\\CMakeFiles\\4.0.0-rc4\\CompilerIdCSharp\\CompilerIdCSharp.csproj" on node 1 (default targets).
      PrepareForBuild:
        Creating directory "obj\\x64\\Debug\\".
      CoreCompile:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Current\\Bin\\Roslyn\\csc.exe /noconfig /nowarn:1701,1702 /fullpaths /nostdlib+ /platform:anycpu32bitpreferred /define:Platformx64;PlatformToolsetv143 /highentropyva+ /reference:"C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\mscorlib.dll" /reference:"C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Core.dll" /debug+ /out:obj\\x64\\Debug\\CompilerIdCSharp.exe /subsystemversion:6.00 /target:exe /utf8output /langversion:7.3 CMakeCSharpCompilerId.cs "obj\\x64\\Debug\\.NETFramework,Version=v4.7.2.AssemblyAttributes.cs"
        CompilerServer: server - server processed compilation - CompilerIdCSharp
      CopyFilesToOutputDirectory:
        Copying file from "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-replacer\\build\\CMakeFiles\\4.0.0-rc4\\CompilerIdCSharp\\obj\\x64\\Debug\\CompilerIdCSharp.exe" to "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-replacer\\build\\CMakeFiles\\4.0.0-rc4\\CompilerIdCSharp\\CompilerIdCSharp.exe".
        CompilerIdCSharp -> D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-replacer\\build\\CMakeFiles\\4.0.0-rc4\\CompilerIdCSharp\\CompilerIdCSharp.exe
        Copying file from "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-replacer\\build\\CMakeFiles\\4.0.0-rc4\\CompilerIdCSharp\\obj\\x64\\Debug\\CompilerIdCSharp.pdb" to "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-replacer\\build\\CMakeFiles\\4.0.0-rc4\\CompilerIdCSharp\\CompilerIdCSharp.pdb".
      PostBuildEvent:
        if not "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Current\\Bin\\Roslyn"=="" if exist "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Current\\Bin\\Roslyn\\csc.exe" set _CSC=C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Current\\Bin\\Roslyn
        if exist "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Current\\Bin\\amd64\\csc.exe" set _CSC=C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Current\\Bin\\amd64
        if "%_CSC%"=="" exit -1
        @echo CMAKE_CSharp_COMPILER=%_CSC%\\csc.exe
        CMAKE_CSharp_COMPILER=C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Current\\Bin\\Roslyn\\csc.exe
      Done Building Project "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-replacer\\build\\CMakeFiles\\4.0.0-rc4\\CompilerIdCSharp\\CompilerIdCSharp.csproj" (default targets).
      
      Build succeeded.
          0 Warning(s)
          0 Error(s)
      
      Time Elapsed 00:00:00.26
      
      
      Compilation of the CSharp compiler identification source "CMakeCSharpCompilerId.cs" produced "CompilerIdCSharp.csproj"
      
      Compilation of the CSharp compiler identification source "CMakeCSharpCompilerId.cs" produced "CompilerIdCSharp.exe"
      
      Compilation of the CSharp compiler identification source "CMakeCSharpCompilerId.cs" produced "CompilerIdCSharp.pdb"
      
      The CSharp compiler identification could not be found in:
        D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-replacer/build/CMakeFiles/4.0.0-rc4/CompilerIdCSharp/CompilerIdCSharp.csproj
      
      The CSharp compiler identification is Microsoft Visual Studio, found in:
        D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-replacer/build/CMakeFiles/4.0.0-rc4/CompilerIdCSharp/CompilerIdCSharp.exe
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeTestCSharpCompiler.cmake:34 (try_compile)"
      - "CMakeLists.txt:3 (project)"
    checks:
      - "Check for working C# compiler: C:/Program Files/Microsoft Visual Studio/2022/Professional/MSBuild/Current/Bin/Roslyn/csc.exe"
    directories:
      source: "D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-replacer/build/CMakeFiles/CMakeScratch/TryCompile-p2gojg"
      binary: "D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-replacer/build/CMakeFiles/CMakeScratch/TryCompile-p2gojg"
    cmakeVariables:
      CMAKE_CSharp_FLAGS: "/platform:x64 /define:TRACE"
      CMAKE_CSharp_FLAGS_DEBUG: "/debug:full /optimize- /warn:3 /errorreport:prompt /define:DEBUG"
      CMAKE_EXE_LINKER_FLAGS: ""
    buildResult:
      variable: "CMAKE_CSharp_COMPILER_WORKS"
      cached: true
      stdout: |
        Change Dir: 'D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-replacer/build/CMakeFiles/CMakeScratch/TryCompile-p2gojg'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_6785f.csproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.11.9+a69bbaaf5 for .NET Framework
        Build started 5/5/2025 10:41:12 AM.
        
        Project "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-replacer\\build\\CMakeFiles\\CMakeScratch\\TryCompile-p2gojg\\cmTC_6785f.csproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-replacer\\build\\CMakeFiles\\CMakeScratch\\TryCompile-p2gojg\\Debug\\".
          Creating directory "obj\\x64\\Debug\\".
        CoreCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Current\\Bin\\Roslyn\\csc.exe /noconfig /nowarn:1701,1702 /fullpaths /nostdlib+ /platform:x64 /errorreport:prompt /warn:3 /define:TRACE;DEBUG /highentropyva+ /reference:"C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\mscorlib.dll" /reference:"C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Core.dll" /debug+ /debug:full /optimize- /out:obj\\x64\\Debug\\cmTC_6785f.exe /subsystemversion:6.00 /target:exe /utf8output /langversion:7.3 D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-replacer\\build\\CMakeFiles\\CMakeScratch\\TryCompile-p2gojg\\testCSharpCompiler.cs "obj\\x64\\Debug\\.NETFramework,Version=v4.7.2.AssemblyAttributes.cs"
          CompilerServer: server - server processed compilation - cmTC_6785f
        CopyFilesToOutputDirectory:
          Copying file from "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-replacer\\build\\CMakeFiles\\CMakeScratch\\TryCompile-p2gojg\\obj\\x64\\Debug\\cmTC_6785f.exe" to "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-replacer\\build\\CMakeFiles\\CMakeScratch\\TryCompile-p2gojg\\Debug\\cmTC_6785f.exe".
          cmTC_6785f -> D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-replacer\\build\\CMakeFiles\\CMakeScratch\\TryCompile-p2gojg\\Debug\\cmTC_6785f.exe
          Copying file from "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-replacer\\build\\CMakeFiles\\CMakeScratch\\TryCompile-p2gojg\\obj\\x64\\Debug\\cmTC_6785f.pdb" to "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-replacer\\build\\CMakeFiles\\CMakeScratch\\TryCompile-p2gojg\\Debug\\cmTC_6785f.pdb".
        Done Building Project "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-replacer\\build\\CMakeFiles\\CMakeScratch\\TryCompile-p2gojg\\cmTC_6785f.csproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:00.24
        
      exitCode: 0
...
