﻿
<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup Label="Globals">
    <ProjectGuid>{A0860F5E-0D2E-3B8F-9F97-F6C5074027B7}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <Configurations>Debug;Release</Configurations>
    <EnableDefaultItems>false</EnableDefaultItems>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
    <ManagedAssembly>true</ManagedAssembly>
    <TargetFramework>net8.0</TargetFramework>
    <AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
    <OutputType>Library</OutputType>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)' == 'Debug'">
    <OutputPath>D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\</OutputPath>
    <AssemblyName>localizationlib_unit_tests</AssemblyName>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    <DebugType>full</DebugType>
    <DefineConstants>TRACE;DEBUG;_DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <LangVersion>latest</LangVersion>
    <Optimize>false</Optimize>
    <Platform>x64</Platform>
    <WarningLevel>3</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)' == 'Release'">
    <OutputPath>D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Release\</OutputPath>
    <AssemblyName>localizationlib_unit_tests</AssemblyName>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    <DebugType>none</DebugType>
    <DefineConstants>TRACE;_DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR</DefineConstants>
    <ErrorReport>queue</ErrorReport>
    <LangVersion>latest</LangVersion>
    <Optimize>true</Optimize>
    <Platform>x64</Platform>
    <WarningLevel>1</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <None Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\CMakeLists.txt">
      <Link>CMakeLists.txt</Link>
    </None>
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeCSharpInformation.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeGenericSystem.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeInitializeConfigs.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeSystemSpecificInformation.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeSystemSpecificInitialize.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\FindGit.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\FindPackageHandleStandardArgs.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\FindPackageMessage.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Windows-Initialize.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Windows.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\WindowsPaths.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="D:\kibisoft\workarea\git\interop\cmake\Globals.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="D:\kibisoft\workarea\git\interop\cmake\VsPackageReferences.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\CMakeFiles\4.0.0-rc4\CMakeCSharpCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\CMakeFiles\4.0.0-rc4\CMakeSystem.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\CMakeFiles\cmake.verify_globs" />
  </ItemGroup>
  <Target Condition="'$(Configuration)' == 'Debug'"
    Name="CustomCommand_Debug_7d11deb2bf4d6cf1d061b61b8bbc7cef"
    Inputs="D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-lib/tests/unit/CMakeLists.txt;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeCSharpInformation.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeGenericSystem.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\FindGit.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Windows-Initialize.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Windows.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\WindowsPaths.cmake;D:\kibisoft\workarea\git\interop\cmake\Globals.cmake;D:\kibisoft\workarea\git\interop\cmake\VsPackageReferences.cmake;D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\CMakeFiles\4.0.0-rc4\CMakeCSharpCompiler.cmake;D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\CMakeFiles\4.0.0-rc4\CMakeSystem.cmake;D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\CMakeFiles\cmake.verify_globs"
    Outputs="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\CMakeFiles\generate.stamp"
    BeforeTargets="CoreCompile"
    DependsOnTargets="PrepareForBuild">
    <Exec Command="echo Building Custom Rule D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-lib/tests/unit/CMakeLists.txt" />
    <Exec Command="setlocal&#10;&quot;C:\Program Files\CMake\bin\cmake.exe&quot; -SD:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-lib/tests/unit -BD:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-lib/tests/unit/build --check-stamp-file D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-lib/tests/unit/build/CMakeFiles/generate.stamp&#10;if %errorlevel% neq 0 goto :cmEnd&#10;:cmEnd&#10;endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone&#10;:cmErrorLevel&#10;exit /b %1&#10;:cmDone&#10;if %errorlevel% neq 0 goto :VCEnd&#10;:VCEnd" />
  </Target>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeCSharpInformation.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeGenericSystem.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeInitializeConfigs.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeSystemSpecificInformation.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeSystemSpecificInitialize.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\FindGit.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\FindPackageHandleStandardArgs.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\FindPackageMessage.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Windows-Initialize.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Windows.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\WindowsPaths.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="D:\kibisoft\workarea\git\interop\cmake\Globals.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="D:\kibisoft\workarea\git\interop\cmake\VsPackageReferences.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\CMakeFiles\4.0.0-rc4\CMakeCSharpCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\CMakeFiles\4.0.0-rc4\CMakeSystem.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\CMakeFiles\cmake.verify_globs" />
  </ItemGroup>
  <Target Condition="'$(Configuration)' == 'Release'"
    Name="CustomCommand_Release_7d11deb2bf4d6cf1d061b61b8bbc7cef"
    Inputs="D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-lib/tests/unit/CMakeLists.txt;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeCSharpInformation.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeGenericSystem.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\FindGit.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Windows-Initialize.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Windows.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\WindowsPaths.cmake;D:\kibisoft\workarea\git\interop\cmake\Globals.cmake;D:\kibisoft\workarea\git\interop\cmake\VsPackageReferences.cmake;D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\CMakeFiles\4.0.0-rc4\CMakeCSharpCompiler.cmake;D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\CMakeFiles\4.0.0-rc4\CMakeSystem.cmake;D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\CMakeFiles\cmake.verify_globs"
    Outputs="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\CMakeFiles\generate.stamp"
    BeforeTargets="CoreCompile"
    DependsOnTargets="PrepareForBuild">
    <Exec Command="echo Building Custom Rule D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-lib/tests/unit/CMakeLists.txt" />
    <Exec Command="setlocal&#10;&quot;C:\Program Files\CMake\bin\cmake.exe&quot; -SD:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-lib/tests/unit -BD:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-lib/tests/unit/build --check-stamp-file D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-lib/tests/unit/build/CMakeFiles/generate.stamp&#10;if %errorlevel% neq 0 goto :cmEnd&#10;:cmEnd&#10;endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone&#10;:cmErrorLevel&#10;exit /b %1&#10;:cmDone&#10;if %errorlevel% neq 0 goto :VCEnd&#10;:VCEnd" />
  </Target>
  <ItemGroup>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\src\BedrockConfigValidationTests.cs">
      <Link>src\BedrockConfigValidationTests.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\src\ConsolidatedCppProcessorTests.cs">
      <Link>src\ConsolidatedCppProcessorTests.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\src\CsExtractorModeTests.cs">
      <Link>src\CsExtractorModeTests.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\src\CsReplacerTests.cs">
      <Link>src\CsReplacerTests.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\src\FileMergerTests.cs">
      <Link>src\FileMergerTests.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\src\LlmClientFactoryTests.cs">
      <Link>src\LlmClientFactoryTests.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\src\LlmVerificationCacheTests.cs">
      <Link>src\LlmVerificationCacheTests.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\src\RegexCppProcessorTests.cs">
      <Link>src\RegexCppProcessorTests.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\src\RegexCsProcessorTests.cs">
      <Link>src\RegexCsProcessorTests.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\src\ReportGeneratorTests.cs">
      <Link>src\ReportGeneratorTests.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\src\RoslynCsProcessorTests.cs">
      <Link>src\RoslynCsProcessorTests.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\src\StringConcatenationProcessorTests.cs">
      <Link>src\StringConcatenationProcessorTests.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\src\StringInterpolationEdgeCaseTests.cs">
      <Link>src\StringInterpolationEdgeCaseTests.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\src\StringInterpolationProcessorTests.cs">
      <Link>src\StringInterpolationProcessorTests.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\src\XamlExtractorModeTests.cs">
      <Link>src\XamlExtractorModeTests.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\src\XamlProcessorTests.cs">
      <Link>src\XamlProcessorTests.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\src\XamlReplacerTests.cs">
      <Link>src\XamlReplacerTests.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\src\XamlTextToCodeBehindProcessorTests.cs">
      <Link>src\XamlTextToCodeBehindProcessorTests.cs</Link>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="NUnit" Version="3.14.0" />
    <PackageReference Include="NUnit3TestAdapter" Version="4.5.0" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.8.0" />
    <PackageReference Include="Moq" Version="4.20.70" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\ZERO_CHECK.vcxproj">
      <Project>{4CCA6B49-B9A4-3195-B68D-768F5CC142CD}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\localizationlib\localizationlib.csproj">
      <Project>{BB6BC777-A517-3A1B-B98D-2F6C0F78586E}</Project>
      <Name>localizationlib</Name>
      <SkipGetTargetFrameworkProperties>true</SkipGetTargetFrameworkProperties>
    </ProjectReference>
  </ItemGroup>
</Project>