/*
 * Copyright 2021-present Alcon. All rights reserved.
 * Alcon proprietary/confidential.
 * Use is subject to license terms.
 */

using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;

namespace LocalizationLib;

/// <summary>
/// Coordinates the localization extraction process.
/// </summary>
public class XamlExtractor : IExtractor
{
    private readonly XamlProcessor _xamlProcessor;

    public XamlExtractor(string projectPath = null, ILlmClient llmClient = null)
    {
        _xamlProcessor = new XamlProcessor(projectPath, llmClient);
    }

    /// <summary>
    /// Processes all .xaml files in a folder, returning any replaced or found items,
    /// but does NOT load/save language file or write a report. 
    /// This lets a higher-level aggregator do that part.
    /// </summary>
    public (List<FileProcessingResult> fileResults, List<ReplacementRecord> replacements) Extract(string projectPath)
    {
        // Get config manager to access skip patterns
        var config = ProjectTextExtractorConfigManager.GetConfigSafe(projectPath);
        var skipPatterns = config?.SkipPatterns ?? new List<string>();

        var allXamlFiles = Directory.GetFiles(projectPath, "*.xaml", SearchOption.AllDirectories)
            .Where(path => !PathUtils.IsInIgnoredDirectory(path) && 
                          !PathUtils.ShouldSkipPath(path, skipPatterns))
            .ToArray();

        Console.WriteLine($"[XAML] Found {allXamlFiles.Length} XAML files under '{projectPath}',"
            + " excluding obj folder and skip patterns.");

        var fileResults = new List<FileProcessingResult>();

        foreach (var xamlPath in allXamlFiles)
        {
            string groupId = LocalizationUtils.GetGroupIdFromXaml(xamlPath);
            var result = _xamlProcessor.ProcessXamlFile(xamlPath, groupId);
            fileResults.Add(result);
        }

        // Return combined results
        return (fileResults, _xamlProcessor.AllReplacementRecords);
    }
}
