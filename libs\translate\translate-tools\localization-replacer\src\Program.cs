/*
 * Copyright 2021-present Alcon. All rights reserved.
 * Alcon proprietary/confidential.
 * Use is subject to license terms.
 */

using CommandLine;
using LocalizationLib;
using System;
using System.Collections.Generic;

namespace LocalizationReplacer;

class Program
{
    static void Main(string[] args)
    {
        Parser.Default.ParseArguments<ToolOptions>(args)
              .WithParsed(RunWithOptions)
              .WithNotParsed(HandleParseError);
    }

    private static void RunWithOptions(ToolOptions opts)
    {
        try
        {
            string fileTypes = opts.FileTypes?.ToLowerInvariant() ?? "xaml";

            var aggregator = new ProjectLocalizationReplacer();
            aggregator.Run(
                projectPath: opts.ProjectPath,
                fileTypes: fileTypes
            );

            Console.WriteLine("Localization replacement completed successfully.");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error: {ex.Message}");
        }
    }

    private static void HandleParseError(IEnumerable<Error> errors)
    {
        // Custom logging can be added here if needed.
    }
}