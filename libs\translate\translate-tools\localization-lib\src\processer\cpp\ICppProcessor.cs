/*
 * Copyright 2021-present Alcon. All rights reserved.
 * Alcon proprietary/confidential.
 * Use is subject to license terms.
 */

using System.Collections.Generic;

namespace LocalizationLib;

/// <summary>
/// Represents a processor that scans C++ files for user-facing text
/// and optionally replaces them for localization.
/// </summary>
public interface ICppProcessor
{
    /// <summary>
    /// A global list of all replacement records generated by this processor.
    /// </summary>
    List<ReplacementRecord> AllReplacementRecords { get; }

    /// <summary>
    /// Processes a single C++ file, returning a FileProcessingResult with found items.
    /// </summary>
    /// <param name="cppPath">Path to the C++ file (.cpp, .h, .hpp, etc.).</param>
    /// <param name="groupId">Logical grouping ID for the discovered translations.</param>
    /// <returns>A FileProcessingResult summarizing findings.</returns>
    FileProcessingResult ProcessCppFile(string cppPath, string groupId);

    /// <summary>
    /// Gets the string type of a replacement record (e.g., "char*", "wchar_t*", "u8string", etc.).
    /// </summary>
    /// <param name="record">The replacement record to get the string type for.</param>
    /// <returns>The string type as a string.</returns>
    string GetStringType(ReplacementRecord record);
} 