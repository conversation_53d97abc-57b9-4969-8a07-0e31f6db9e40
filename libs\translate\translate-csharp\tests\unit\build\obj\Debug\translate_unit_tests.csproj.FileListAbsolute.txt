D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\Debug\testhost.exe
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\Debug\testhost.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\Debug\Microsoft.TestPlatform.PlatformAbstractions.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\Debug\NUnit3.TestAdapter.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\Debug\NUnit3.TestAdapter.pdb
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\Debug\nunit.engine.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\Debug\nunit.engine.api.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\Debug\nunit.engine.core.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\Debug\testcentric.engine.metadata.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\Debug\translate_unit_tests.deps.json
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\Debug\translate_unit_tests.runtimeconfig.json
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\Debug\translate_unit_tests.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\Debug\translate_unit_tests.pdb
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\Debug\Castle.Core.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\Debug\Microsoft.VisualStudio.CodeCoverage.Shim.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\Debug\Microsoft.TestPlatform.CoreUtilities.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\Debug\Microsoft.VisualStudio.TestPlatform.ObjectModel.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\Debug\Microsoft.TestPlatform.CommunicationUtilities.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\Debug\Microsoft.TestPlatform.CrossPlatEngine.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\Debug\Microsoft.TestPlatform.Utilities.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\Debug\Microsoft.VisualStudio.TestPlatform.Common.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\Debug\Moq.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\Debug\Newtonsoft.Json.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\Debug\NuGet.Frameworks.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\Debug\nunit.framework.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\Debug\cs\Microsoft.TestPlatform.CoreUtilities.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\Debug\cs\Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\Debug\de\Microsoft.TestPlatform.CoreUtilities.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\Debug\de\Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\Debug\es\Microsoft.TestPlatform.CoreUtilities.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\Debug\es\Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\Debug\fr\Microsoft.TestPlatform.CoreUtilities.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\Debug\fr\Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\Debug\it\Microsoft.TestPlatform.CoreUtilities.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\Debug\it\Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\Debug\ja\Microsoft.TestPlatform.CoreUtilities.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\Debug\ja\Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\Debug\ko\Microsoft.TestPlatform.CoreUtilities.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\Debug\ko\Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\Debug\pl\Microsoft.TestPlatform.CoreUtilities.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\Debug\pl\Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\Debug\pt-BR\Microsoft.TestPlatform.CoreUtilities.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\Debug\pt-BR\Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\Debug\ru\Microsoft.TestPlatform.CoreUtilities.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\Debug\ru\Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\Debug\tr\Microsoft.TestPlatform.CoreUtilities.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\Debug\tr\Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\Debug\zh-Hans\Microsoft.TestPlatform.CoreUtilities.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\Debug\zh-Hans\Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\Debug\zh-Hant\Microsoft.TestPlatform.CoreUtilities.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\Debug\zh-Hant\Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\Debug\cs\Microsoft.TestPlatform.CommunicationUtilities.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\Debug\cs\Microsoft.TestPlatform.CrossPlatEngine.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\Debug\cs\Microsoft.VisualStudio.TestPlatform.Common.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\Debug\de\Microsoft.TestPlatform.CommunicationUtilities.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\Debug\de\Microsoft.TestPlatform.CrossPlatEngine.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\Debug\de\Microsoft.VisualStudio.TestPlatform.Common.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\Debug\es\Microsoft.TestPlatform.CommunicationUtilities.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\Debug\es\Microsoft.TestPlatform.CrossPlatEngine.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\Debug\es\Microsoft.VisualStudio.TestPlatform.Common.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\Debug\fr\Microsoft.TestPlatform.CommunicationUtilities.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\Debug\fr\Microsoft.TestPlatform.CrossPlatEngine.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\Debug\fr\Microsoft.VisualStudio.TestPlatform.Common.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\Debug\it\Microsoft.TestPlatform.CommunicationUtilities.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\Debug\it\Microsoft.TestPlatform.CrossPlatEngine.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\Debug\it\Microsoft.VisualStudio.TestPlatform.Common.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\Debug\ja\Microsoft.TestPlatform.CommunicationUtilities.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\Debug\ja\Microsoft.TestPlatform.CrossPlatEngine.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\Debug\ja\Microsoft.VisualStudio.TestPlatform.Common.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\Debug\ko\Microsoft.TestPlatform.CommunicationUtilities.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\Debug\ko\Microsoft.TestPlatform.CrossPlatEngine.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\Debug\ko\Microsoft.VisualStudio.TestPlatform.Common.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\Debug\pl\Microsoft.TestPlatform.CommunicationUtilities.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\Debug\pl\Microsoft.TestPlatform.CrossPlatEngine.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\Debug\pl\Microsoft.VisualStudio.TestPlatform.Common.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\Debug\pt-BR\Microsoft.TestPlatform.CommunicationUtilities.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\Debug\pt-BR\Microsoft.TestPlatform.CrossPlatEngine.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\Debug\pt-BR\Microsoft.VisualStudio.TestPlatform.Common.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\Debug\ru\Microsoft.TestPlatform.CommunicationUtilities.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\Debug\ru\Microsoft.TestPlatform.CrossPlatEngine.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\Debug\ru\Microsoft.VisualStudio.TestPlatform.Common.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\Debug\tr\Microsoft.TestPlatform.CommunicationUtilities.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\Debug\tr\Microsoft.TestPlatform.CrossPlatEngine.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\Debug\tr\Microsoft.VisualStudio.TestPlatform.Common.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\Debug\zh-Hans\Microsoft.TestPlatform.CommunicationUtilities.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\Debug\zh-Hans\Microsoft.TestPlatform.CrossPlatEngine.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\Debug\zh-Hans\Microsoft.VisualStudio.TestPlatform.Common.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\Debug\zh-Hant\Microsoft.TestPlatform.CommunicationUtilities.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\Debug\zh-Hant\Microsoft.TestPlatform.CrossPlatEngine.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\Debug\zh-Hant\Microsoft.VisualStudio.TestPlatform.Common.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\Debug\translate_csharp.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\Debug\translate_csharp.pdb
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\obj\Debug\translate_unit_tests.csproj.AssemblyReference.cache
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\obj\Debug\translate_unit_tests.GeneratedMSBuildEditorConfig.editorconfig
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\obj\Debug\translate_unit_tests.AssemblyInfoInputs.cache
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\obj\Debug\translate_unit_tests.AssemblyInfo.cs
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\obj\Debug\translate_unit_tests.csproj.CoreCompileInputs.cache
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\obj\Debug\translate_unit_tests.sourcelink.json
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\obj\Debug\translat.C137C2C3.Up2Date
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\obj\Debug\translate_unit_tests.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\obj\Debug\refint\translate_unit_tests.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\obj\Debug\translate_unit_tests.pdb
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\obj\Debug\translate_unit_tests.genruntimeconfig.cache
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\obj\Debug\ref\translate_unit_tests.dll
