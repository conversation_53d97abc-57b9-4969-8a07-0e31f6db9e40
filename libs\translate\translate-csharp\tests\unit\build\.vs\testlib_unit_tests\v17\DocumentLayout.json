{"Version": 1, "WorkspaceRootPath": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\tests\\unit\\build\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{DBEFA130-DC42-3B19-9C88-AF7B519F8AE5}|testlib\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\teststep.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{147DF982-596E-3A6E-9874-E3DDEEBB09E5}|testlib_unit_tests.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\tests\\unit\\src\\testexecutionwithtabletests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DBEFA130-DC42-3B19-9C88-AF7B519F8AE5}|testlib\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\test.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DBEFA130-DC42-3B19-9C88-AF7B519F8AE5}|testlib\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\teststepfactory.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DBEFA130-DC42-3B19-9C88-AF7B519F8AE5}|testlib\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\testfactory.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DBEFA130-DC42-3B19-9C88-AF7B519F8AE5}|testlib\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\context\\testdata.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DBEFA130-DC42-3B19-9C88-AF7B519F8AE5}|testlib\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\context\\tablefileloader.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{147DF982-596E-3A6E-9874-E3DDEEBB09E5}|testlib_unit_tests.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\tests\\unit\\src\\testcontextresolvetemplatetests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DBEFA130-DC42-3B19-9C88-AF7B519F8AE5}|testlib\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\context\\contextutils.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DBEFA130-DC42-3B19-9C88-AF7B519F8AE5}|testlib\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\context\\jpathresolver.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{147DF982-596E-3A6E-9874-E3DDEEBB09E5}|testlib_unit_tests.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\tests\\unit\\src\\testcontextresolvetests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{147DF982-596E-3A6E-9874-E3DDEEBB09E5}|testlib_unit_tests.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\tests\\unit\\src\\expressionerrormessagetests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DBEFA130-DC42-3B19-9C88-AF7B519F8AE5}|testlib\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\utils\\jtokencomparer.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{147DF982-596E-3A6E-9874-E3DDEEBB09E5}|testlib_unit_tests.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\tests\\unit\\src\\jtokencomparertests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{147DF982-596E-3A6E-9874-E3DDEEBB09E5}|testlib_unit_tests.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\tests\\unit\\src\\jtokencomparercontainstests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{147DF982-596E-3A6E-9874-E3DDEEBB09E5}|testlib_unit_tests.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\tests\\unit\\src\\jsonschemaexpressiontests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{147DF982-596E-3A6E-9874-E3DDEEBB09E5}|testlib_unit_tests.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\tests\\unit\\src\\containexpressiontests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\AppData\\Local\\SourceServer\\2b7926876cc0be507c49f67f0fd5fadd226b4e7cbf3c1bbb06fc29eeb268a49f\\src\\NUnitFramework\\framework\\Assert.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DBEFA130-DC42-3B19-9C88-AF7B519F8AE5}|testlib\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\interop\\rpc\\rpcteststepexecutor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DBEFA130-DC42-3B19-9C88-AF7B519F8AE5}|testlib\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\interop\\rpc\\rpcteststepinputfactory.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{147DF982-596E-3A6E-9874-E3DDEEBB09E5}|testlib_unit_tests.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\tests\\unit\\src\\rpcteststepfactorytests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DBEFA130-DC42-3B19-9C88-AF7B519F8AE5}|testlib\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\interop\\rpc\\rpcteststepfactory.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DBEFA130-DC42-3B19-9C88-AF7B519F8AE5}|testlib\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\interop\\rpc\\rpcteststepinputtemplate.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{147DF982-596E-3A6E-9874-E3DDEEBB09E5}|testlib_unit_tests.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\tests\\unit\\src\\resolvemethodtests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DBEFA130-DC42-3B19-9C88-AF7B519F8AE5}|testlib\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\context\\variablemanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DBEFA130-DC42-3B19-9C88-AF7B519F8AE5}|testlib\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\context\\tablemanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DBEFA130-DC42-3B19-9C88-AF7B519F8AE5}|testlib\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\http\\httpteststepinputfactory.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{147DF982-596E-3A6E-9874-E3DDEEBB09E5}|testlib_unit_tests.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\tests\\unit\\src\\testlibintegrationtests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{147DF982-596E-3A6E-9874-E3DDEEBB09E5}|testlib_unit_tests.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\tests\\unit\\src\\testcontexttabletests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{147DF982-596E-3A6E-9874-E3DDEEBB09E5}|testlib_unit_tests.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\tests\\unit\\src\\testcontextvariabletests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{147DF982-596E-3A6E-9874-E3DDEEBB09E5}|testlib_unit_tests.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\tests\\unit\\src\\csscriptexpressiontests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{147DF982-596E-3A6E-9874-E3DDEEBB09E5}|testlib_unit_tests.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\tests\\unit\\src\\pyscriptexpressiontests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{147DF982-596E-3A6E-9874-E3DDEEBB09E5}|testlib_unit_tests.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\tests\\unit\\src\\constantexpressiontests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{147DF982-596E-3A6E-9874-E3DDEEBB09E5}|testlib_unit_tests.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\tests\\unit\\src\\cmdteststepexecutortests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DBEFA130-DC42-3B19-9C88-AF7B519F8AE5}|testlib\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\interop\\mbus\\mbusteststepexecutor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DBEFA130-DC42-3B19-9C88-AF7B519F8AE5}|testlib\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\expressions\\jsonschemaexpression.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DBEFA130-DC42-3B19-9C88-AF7B519F8AE5}|testlib\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\expressions\\constantexpression.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{147DF982-596E-3A6E-9874-E3DDEEBB09E5}|testlib_unit_tests.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\tests\\unit\\src\\equalexpressiontests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DBEFA130-DC42-3B19-9C88-AF7B519F8AE5}|testlib\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\interop\\mbus\\mbusteststepinputfactory.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DBEFA130-DC42-3B19-9C88-AF7B519F8AE5}|testlib\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\testsuite.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DBEFA130-DC42-3B19-9C88-AF7B519F8AE5}|testlib\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\expressions\\jsonpathexpression.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DBEFA130-DC42-3B19-9C88-AF7B519F8AE5}|testlib\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\cmd\\cmdteststepinputfactory.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{147DF982-596E-3A6E-9874-E3DDEEBB09E5}|testlib_unit_tests.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\tests\\unit\\src\\testsuitetests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DBEFA130-DC42-3B19-9C88-AF7B519F8AE5}|testlib\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\expressions\\binaryexpression.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DBEFA130-DC42-3B19-9C88-AF7B519F8AE5}|testlib\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\expressions\\containexpression.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DBEFA130-DC42-3B19-9C88-AF7B519F8AE5}|testlib\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\expressions\\equalexpression.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DBEFA130-DC42-3B19-9C88-AF7B519F8AE5}|testlib\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\teststepoutputsettings.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{147DF982-596E-3A6E-9874-E3DDEEBB09E5}|testlib_unit_tests.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\tests\\unit\\src\\testfactorytests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{147DF982-596E-3A6E-9874-E3DDEEBB09E5}|testlib_unit_tests.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\tests\\unit\\src\\asserterbasedretryconditiontests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{147DF982-596E-3A6E-9874-E3DDEEBB09E5}|testlib_unit_tests.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\tests\\unit\\src\\teststepfromyamldatatests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DBEFA130-DC42-3B19-9C88-AF7B519F8AE5}|testlib\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\assertions\\iasserter.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DBEFA130-DC42-3B19-9C88-AF7B519F8AE5}|testlib\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\testsuiteconfig.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{147DF982-596E-3A6E-9874-E3DDEEBB09E5}|testlib_unit_tests.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\tests\\unit\\src\\teststepexecutionsettingstests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{147DF982-596E-3A6E-9874-E3DDEEBB09E5}|testlib_unit_tests.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\tests\\unit\\src\\httpteststepexecutortests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{147DF982-596E-3A6E-9874-E3DDEEBB09E5}|testlib_unit_tests.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\tests\\unit\\src\\testcontexttests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DBEFA130-DC42-3B19-9C88-AF7B519F8AE5}|testlib\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\utils\\jtokenextensions.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DBEFA130-DC42-3B19-9C88-AF7B519F8AE5}|testlib\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\assertions\\asserterfactory.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{147DF982-596E-3A6E-9874-E3DDEEBB09E5}|testlib_unit_tests.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\tests\\unit\\src\\mbusteststepfactorytests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DBEFA130-DC42-3B19-9C88-AF7B519F8AE5}|testlib\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\interop\\mbus\\mbusteststepfactory.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DBEFA130-DC42-3B19-9C88-AF7B519F8AE5}|testlib\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\interop\\mbus\\mbusteststepoutput.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DBEFA130-DC42-3B19-9C88-AF7B519F8AE5}|testlib\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\teststepexecutionsettings.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{147DF982-596E-3A6E-9874-E3DDEEBB09E5}|testlib_unit_tests.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\tests\\unit\\src\\baseteststepexecutortests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DBEFA130-DC42-3B19-9C88-AF7B519F8AE5}|testlib\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\baseteststepexecutor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DBEFA130-DC42-3B19-9C88-AF7B519F8AE5}|testlib\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\http\\httpteststepoutputsettings.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DBEFA130-DC42-3B19-9C88-AF7B519F8AE5}|testlib\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\assertions\\assert.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DBEFA130-DC42-3B19-9C88-AF7B519F8AE5}|testlib\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\assertions\\expect.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DBEFA130-DC42-3B19-9C88-AF7B519F8AE5}|testlib\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\utils\\iretrycondition.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DBEFA130-DC42-3B19-9C88-AF7B519F8AE5}|testlib\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\http\\httpteststepexecutor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DBEFA130-DC42-3B19-9C88-AF7B519F8AE5}|testlib\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\http\\httpteststepinput.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DBEFA130-DC42-3B19-9C88-AF7B519F8AE5}|testlib\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\tests\\unit\\build\\testlib\\testlib.csproj||{04B8AB82-A572-4FEF-95CE-5222444B6B64}|", "RelativeMoniker": "D:0:0:{DBEFA130-DC42-3B19-9C88-AF7B519F8AE5}|testlib\\testlib.csproj|solutionrelative:testlib\\testlib.csproj||{04B8AB82-A572-4FEF-95CE-5222444B6B64}|"}, {"AbsoluteMoniker": "D:0:0:{DBEFA130-DC42-3B19-9C88-AF7B519F8AE5}|testlib\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\expressions\\notcontainexpression.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DBEFA130-DC42-3B19-9C88-AF7B519F8AE5}|testlib\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\cmd\\cmdteststepinput.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DBEFA130-DC42-3B19-9C88-AF7B519F8AE5}|testlib\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\cmd\\cmdteststepexecutor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{147DF982-596E-3A6E-9874-E3DDEEBB09E5}|testlib_unit_tests.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\tests\\unit\\src\\startupsteptests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{147DF982-596E-3A6E-9874-E3DDEEBB09E5}|testlib_unit_tests.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\tests\\unit\\src\\cmdteststepfactorytests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DBEFA130-DC42-3B19-9C88-AF7B519F8AE5}|testlib\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\cmd\\cmdteststepinputtemplate.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DBEFA130-DC42-3B19-9C88-AF7B519F8AE5}|testlib\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\http\\httpteststepfactory.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{147DF982-596E-3A6E-9874-E3DDEEBB09E5}|testlib_unit_tests.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\tests\\unit\\src\\httpteststepfactorytests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DBEFA130-DC42-3B19-9C88-AF7B519F8AE5}|testlib\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\interop\\rpc\\rpcteststepoutput.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{147DF982-596E-3A6E-9874-E3DDEEBB09E5}|testlib_unit_tests.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\tests\\unit\\src\\notcontainexpressiontests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DBEFA130-DC42-3B19-9C88-AF7B519F8AE5}|testlib\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\expressions\\lessorequalexpression.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DBEFA130-DC42-3B19-9C88-AF7B519F8AE5}|testlib\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\expressions\\greaterexpression.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DBEFA130-DC42-3B19-9C88-AF7B519F8AE5}|testlib\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\expressions\\greaterorequalexpression.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DBEFA130-DC42-3B19-9C88-AF7B519F8AE5}|testlib\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\iteststepoutput.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DBEFA130-DC42-3B19-9C88-AF7B519F8AE5}|testlib\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\interop\\mbus\\mbusteststepinput.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DBEFA130-DC42-3B19-9C88-AF7B519F8AE5}|testlib\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\tests\\etc\\assemblyinfo.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DBEFA130-DC42-3B19-9C88-AF7B519F8AE5}|testlib\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\http\\httpteststepoutput.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DBEFA130-DC42-3B19-9C88-AF7B519F8AE5}|testlib\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\http\\httpteststepinputtemplate.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DBEFA130-DC42-3B19-9C88-AF7B519F8AE5}|testlib\\testlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\interop\\mbus\\mbusteststepinputtemplate.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 5, "Children": [{"$type": "Document", "DocumentIndex": 3, "Title": "TestStepFactory.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\TestStepFactory.cs", "RelativeDocumentMoniker": "..\\..\\..\\src\\TestStepFactory.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\TestStepFactory.cs", "RelativeToolTip": "..\\..\\..\\src\\TestStepFactory.cs", "ViewState": "AgIAAIoAAAAAAAAAAAAowKEAAABdAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-01-22T12:21:07.303Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "TestFactory.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\TestFactory.cs", "RelativeDocumentMoniker": "..\\..\\..\\src\\TestFactory.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\TestFactory.cs", "RelativeToolTip": "..\\..\\..\\src\\TestFactory.cs", "ViewState": "AgIAAFoAAAAAAAAAAAAewGwAAAAMAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-01-22T12:20:18.373Z", "EditorCaption": ""}, {"$type": "Bookmark", "Name": "ST:0:0:{e1b7d1f8-9b3c-49b1-8f4f-bfc63a88835d}"}, {"$type": "Document", "DocumentIndex": 5, "Title": "TestData.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\context\\TestData.cs", "RelativeDocumentMoniker": "..\\..\\..\\src\\context\\TestData.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\context\\TestData.cs", "RelativeToolTip": "..\\..\\..\\src\\context\\TestData.cs", "ViewState": "AgIAAAwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-01-22T12:16:44.618Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "Test.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\Test.cs", "RelativeDocumentMoniker": "..\\..\\..\\src\\Test.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\Test.cs", "RelativeToolTip": "..\\..\\..\\src\\Test.cs", "ViewState": "AgIAAHEAAAAAAAAAAAAewH8AAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-01-22T02:27:11.191Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "TestStep.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\TestStep.cs", "RelativeDocumentMoniker": "..\\..\\..\\src\\TestStep.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\TestStep.cs", "RelativeToolTip": "..\\..\\..\\src\\TestStep.cs", "ViewState": "AgIAAFkAAAAAAAAAAAA1wGoAAABHAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-01-22T02:27:07.254Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "TestContextResolveTemplateTests.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\tests\\unit\\src\\TestContextResolveTemplateTests.cs", "RelativeDocumentMoniker": "..\\src\\TestContextResolveTemplateTests.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\tests\\unit\\src\\TestContextResolveTemplateTests.cs", "RelativeToolTip": "..\\src\\TestContextResolveTemplateTests.cs", "ViewState": "AgIAAD0AAAAAAAAAAAAgwCEAAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-01-22T02:04:06.291Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 12, "Title": "JTokenComparer.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\utils\\JTokenComparer.cs", "RelativeDocumentMoniker": "..\\..\\..\\src\\utils\\JTokenComparer.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\utils\\JTokenComparer.cs", "RelativeToolTip": "..\\..\\..\\src\\utils\\JTokenComparer.cs", "ViewState": "AgIAABEAAAAAAAAAAIAwwCEAAAASAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-01-20T11:42:07.051Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 15, "Title": "JsonSchemaExpressionTests.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\tests\\unit\\src\\JsonSchemaExpressionTests.cs", "RelativeDocumentMoniker": "..\\src\\JsonSchemaExpressionTests.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\tests\\unit\\src\\JsonSchemaExpressionTests.cs", "RelativeToolTip": "..\\src\\JsonSchemaExpressionTests.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA0AAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-01-20T11:02:00.867Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 14, "Title": "JTokenComparerContainsTests.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\tests\\unit\\src\\JTokenComparerContainsTests.cs", "RelativeDocumentMoniker": "..\\src\\JTokenComparerContainsTests.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\tests\\unit\\src\\JTokenComparerContainsTests.cs", "RelativeToolTip": "..\\src\\JTokenComparerContainsTests.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAB8BAAAFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-01-20T11:01:14.087Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 16, "Title": "ContainExpressionTests.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\tests\\unit\\src\\ContainExpressionTests.cs", "RelativeDocumentMoniker": "..\\src\\ContainExpressionTests.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\tests\\unit\\src\\ContainExpressionTests.cs", "RelativeToolTip": "..\\src\\ContainExpressionTests.cs", "ViewState": "AgIAAFEAAAAAAAAAAAAewGEAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-01-20T10:17:38.642Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "TestExecutionWithTableTests.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\tests\\unit\\src\\TestExecutionWithTableTests.cs", "RelativeDocumentMoniker": "..\\src\\TestExecutionWithTableTests.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\tests\\unit\\src\\TestExecutionWithTableTests.cs", "RelativeToolTip": "..\\src\\TestExecutionWithTableTests.cs", "ViewState": "AgIAAEgAAAAAAAAAAAAewFoAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-01-06T16:32:16.395Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 13, "Title": "JTokenComparerTests.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\tests\\unit\\src\\JTokenComparerTests.cs", "RelativeDocumentMoniker": "..\\src\\JTokenComparerTests.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\tests\\unit\\src\\JTokenComparerTests.cs", "RelativeToolTip": "..\\src\\JTokenComparerTests.cs", "ViewState": "AgIAACwAAAAAAAAAAAAMwDcAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-01-20T10:18:58.527Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "TableFileLoader.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\context\\TableFileLoader.cs", "RelativeDocumentMoniker": "..\\..\\..\\src\\context\\TableFileLoader.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\context\\TableFileLoader.cs", "RelativeToolTip": "..\\..\\..\\src\\context\\TableFileLoader.cs", "ViewState": "AgIAABEAAAAAAAAAAAAQwGwAAAAYAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-01-10T07:12:16.47Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 11, "Title": "ExpressionErrorMessageTests.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\tests\\unit\\src\\ExpressionErrorMessageTests.cs", "RelativeDocumentMoniker": "..\\src\\ExpressionErrorMessageTests.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\tests\\unit\\src\\ExpressionErrorMessageTests.cs", "RelativeToolTip": "..\\src\\ExpressionErrorMessageTests.cs", "ViewState": "AgIAADYAAAAAAAAAAAAewEAAAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-01-21T04:33:08.796Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 10, "Title": "TestContextResolveTests.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\tests\\unit\\src\\TestContextResolveTests.cs", "RelativeDocumentMoniker": "..\\src\\TestContextResolveTests.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\tests\\unit\\src\\TestContextResolveTests.cs", "RelativeToolTip": "..\\src\\TestContextResolveTests.cs", "ViewState": "AgIAAFsAAAAAAAAAAAAgwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-01-22T02:03:45.372Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 8, "Title": "ContextUtils.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\context\\ContextUtils.cs", "RelativeDocumentMoniker": "..\\..\\..\\src\\context\\ContextUtils.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\context\\ContextUtils.cs", "RelativeToolTip": "..\\..\\..\\src\\context\\ContextUtils.cs", "ViewState": "AgIAANkAAAAAAAAAAAAMwPIAAAAEAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-01-22T02:06:33.08Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 9, "Title": "JPathResolver.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\context\\JPathResolver.cs", "RelativeDocumentMoniker": "..\\..\\..\\src\\context\\JPathResolver.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\context\\JPathResolver.cs", "RelativeToolTip": "..\\..\\..\\src\\context\\JPathResolver.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABkAAAAuAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-01-22T02:18:26.342Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 22, "Title": "RpcTestStepInputTemplate.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\interop\\rpc\\RpcTestStepInputTemplate.cs", "RelativeDocumentMoniker": "..\\..\\..\\src\\interop\\rpc\\RpcTestStepInputTemplate.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\interop\\rpc\\RpcTestStepInputTemplate.cs", "RelativeToolTip": "..\\..\\..\\src\\interop\\rpc\\RpcTestStepInputTemplate.cs", "ViewState": "AgIAAA8AAAAAAAAAAAApwB8AAAAmAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-11-25T07:22:42.935Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 23, "Title": "ResolveMethodTests.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\tests\\unit\\src\\ResolveMethodTests.cs", "RelativeDocumentMoniker": "..\\src\\ResolveMethodTests.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\tests\\unit\\src\\ResolveMethodTests.cs", "RelativeToolTip": "..\\src\\ResolveMethodTests.cs", "ViewState": "AgIAAAsAAAAAAAAAAAAewB0AAAASAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-01-09T10:10:25.805Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 20, "Title": "RpcTestStepFactoryTests.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\tests\\unit\\src\\RpcTestStepFactoryTests.cs", "RelativeDocumentMoniker": "..\\src\\RpcTestStepFactoryTests.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\tests\\unit\\src\\RpcTestStepFactoryTests.cs", "RelativeToolTip": "..\\src\\RpcTestStepFactoryTests.cs", "ViewState": "AgIAAEAAAAAAAAAAAAAnwBoAAABBAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-11-05T06:57:17.404Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 21, "Title": "RpcTestStepFactory.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\interop\\rpc\\RpcTestStepFactory.cs", "RelativeDocumentMoniker": "..\\..\\..\\src\\interop\\rpc\\RpcTestStepFactory.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\interop\\rpc\\RpcTestStepFactory.cs", "RelativeToolTip": "..\\..\\..\\src\\interop\\rpc\\RpcTestStepFactory.cs", "ViewState": "AgIAAD8AAAAAAAAAAAAwwFIAAACJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-11-25T07:22:03.519Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 19, "Title": "RpcTestStepInputFactory.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\interop\\rpc\\RpcTestStepInputFactory.cs", "RelativeDocumentMoniker": "..\\..\\..\\src\\interop\\rpc\\RpcTestStepInputFactory.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\interop\\rpc\\RpcTestStepInputFactory.cs", "RelativeToolTip": "..\\..\\..\\src\\interop\\rpc\\RpcTestStepInputFactory.cs", "ViewState": "AgIAALkAAAAAAAAAAIAwwNMAAAAJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-01-09T09:33:43.892Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 24, "Title": "VariableManager.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\context\\VariableManager.cs", "RelativeDocumentMoniker": "..\\..\\..\\src\\context\\VariableManager.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\context\\VariableManager.cs", "RelativeToolTip": "..\\..\\..\\src\\context\\VariableManager.cs", "ViewState": "AgIAAFAAAAAAAAAAAIAwwGEAAAByAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-01-09T07:34:39.089Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 25, "Title": "TableManager.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\context\\TableManager.cs", "RelativeDocumentMoniker": "..\\..\\..\\src\\context\\TableManager.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\context\\TableManager.cs", "RelativeToolTip": "..\\..\\..\\src\\context\\TableManager.cs", "ViewState": "AgIAAIcAAAAAAAAAAAAMwJgAAAA0AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-01-09T07:34:22.45Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 18, "Title": "RpcTestStepExecutor.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\interop\\rpc\\RpcTestStepExecutor.cs", "RelativeDocumentMoniker": "..\\..\\..\\src\\interop\\rpc\\RpcTestStepExecutor.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\interop\\rpc\\RpcTestStepExecutor.cs", "RelativeToolTip": "..\\..\\..\\src\\interop\\rpc\\RpcTestStepExecutor.cs", "ViewState": "AgIAABcAAAAAAAAAAAAQwDIAAAAEAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-11-25T07:21:46.616Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 17, "Title": "Assert.cs", "DocumentMoniker": "C:\\Users\\<USER>\\AppData\\Local\\SourceServer\\2b7926876cc0be507c49f67f0fd5fadd226b4e7cbf3c1bbb06fc29eeb268a49f\\src\\NUnitFramework\\framework\\Assert.cs", "ToolTip": "C:\\Users\\<USER>\\AppData\\Local\\SourceServer\\2b7926876cc0be507c49f67f0fd5fadd226b4e7cbf3c1bbb06fc29eeb268a49f\\src\\NUnitFramework\\framework\\Assert.cs", "ViewState": "AgIAAJQBAAAAAAAAAAAAAJQBAAAQAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-01-03T05:00:38.64Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 26, "Title": "HttpTestStepInputFactory.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\http\\HttpTestStepInputFactory.cs", "RelativeDocumentMoniker": "..\\..\\..\\src\\http\\HttpTestStepInputFactory.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\http\\HttpTestStepInputFactory.cs", "RelativeToolTip": "..\\..\\..\\src\\http\\HttpTestStepInputFactory.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABUAAAAxAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-01-09T09:43:02.085Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 28, "Title": "TestContextTableTests.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\tests\\unit\\src\\TestContextTableTests.cs", "RelativeDocumentMoniker": "..\\src\\TestContextTableTests.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\tests\\unit\\src\\TestContextTableTests.cs", "RelativeToolTip": "..\\src\\TestContextTableTests.cs", "ViewState": "AgIAABQAAAAAAAAAAAAowCYAAAAiAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-01-09T10:02:49.902Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 30, "Title": "CsScriptExpressionTests.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\tests\\unit\\src\\CsScriptExpressionTests.cs", "RelativeDocumentMoniker": "..\\src\\CsScriptExpressionTests.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\tests\\unit\\src\\CsScriptExpressionTests.cs", "RelativeToolTip": "..\\src\\CsScriptExpressionTests.cs", "ViewState": "AgIAAJwAAAAAAAAAAAAAAKEAAAAoAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-11-17T07:25:57.936Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 31, "Title": "PyScriptExpressionTests.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\tests\\unit\\src\\PyScriptExpressionTests.cs", "RelativeDocumentMoniker": "..\\src\\PyScriptExpressionTests.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\tests\\unit\\src\\PyScriptExpressionTests.cs", "RelativeToolTip": "..\\src\\PyScriptExpressionTests.cs", "ViewState": "AgIAABsAAAAAAAAAAAAMwDAAAAAYAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-01-09T10:10:52.275Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 27, "Title": "TestLibIntegrationTests.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\tests\\unit\\src\\TestLibIntegrationTests.cs", "RelativeDocumentMoniker": "..\\src\\TestLibIntegrationTests.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\tests\\unit\\src\\TestLibIntegrationTests.cs", "RelativeToolTip": "..\\src\\TestLibIntegrationTests.cs", "ViewState": "AgIAAB8AAAAAAAAAAAAewCEAAAADAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-01-10T07:14:12.174Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 29, "Title": "TestContextVariableTests.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\tests\\unit\\src\\TestContextVariableTests.cs", "RelativeDocumentMoniker": "..\\src\\TestContextVariableTests.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\tests\\unit\\src\\TestContextVariableTests.cs", "RelativeToolTip": "..\\src\\TestContextVariableTests.cs", "ViewState": "AgIAAGUAAAAAAAAAAAAMwH4AAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-10-19T10:47:06.691Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 33, "Title": "CmdTestStepExecutorTests.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\tests\\unit\\src\\CmdTestStepExecutorTests.cs", "RelativeDocumentMoniker": "..\\src\\CmdTestStepExecutorTests.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\tests\\unit\\src\\CmdTestStepExecutorTests.cs", "RelativeToolTip": "..\\src\\CmdTestStepExecutorTests.cs", "ViewState": "AgIAAAYAAAAAAAAAAAAgwBcAAAAVAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-01-09T10:02:27.328Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 32, "Title": "ConstantExpressionTests.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\tests\\unit\\src\\ConstantExpressionTests.cs", "RelativeDocumentMoniker": "..\\src\\ConstantExpressionTests.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\tests\\unit\\src\\ConstantExpressionTests.cs", "RelativeToolTip": "..\\src\\ConstantExpressionTests.cs", "ViewState": "AgIAAAYAAAAAAAAAAAAwwBUAAAAlAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-01-09T10:06:41.681Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 34, "Title": "MBusTestStepExecutor.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\interop\\mbus\\MBusTestStepExecutor.cs", "RelativeDocumentMoniker": "..\\..\\..\\src\\interop\\mbus\\MBusTestStepExecutor.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\interop\\mbus\\MBusTestStepExecutor.cs", "RelativeToolTip": "..\\..\\..\\src\\interop\\mbus\\MBusTestStepExecutor.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAqwA4AAABXAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-01-09T09:58:56.488Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 35, "Title": "JsonSchemaExpression.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\expressions\\JsonSchemaExpression.cs", "RelativeDocumentMoniker": "..\\..\\..\\src\\expressions\\JsonSchemaExpression.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\expressions\\JsonSchemaExpression.cs", "RelativeToolTip": "..\\..\\..\\src\\expressions\\JsonSchemaExpression.cs", "ViewState": "AgIAACsAAAAAAAAAAAAowD0AAAA4AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-01-09T09:57:10.591Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 36, "Title": "ConstantExpression.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\expressions\\ConstantExpression.cs", "RelativeDocumentMoniker": "..\\..\\..\\src\\expressions\\ConstantExpression.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\expressions\\ConstantExpression.cs", "RelativeToolTip": "..\\..\\..\\src\\expressions\\ConstantExpression.cs", "ViewState": "AgIAAAYAAAAAAAAAAAAgwBcAAAAlAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-01-09T09:56:09.256Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 37, "Title": "EqualExpressionTests.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\tests\\unit\\src\\EqualExpressionTests.cs", "RelativeDocumentMoniker": "..\\src\\EqualExpressionTests.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\tests\\unit\\src\\EqualExpressionTests.cs", "RelativeToolTip": "..\\src\\EqualExpressionTests.cs", "ViewState": "AgIAAAcAAAAAAAAAAAAgwBgAAAAeAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-11-20T06:22:37.742Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 38, "Title": "MBusTestStepInputFactory.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\interop\\mbus\\MBusTestStepInputFactory.cs", "RelativeDocumentMoniker": "..\\..\\..\\src\\interop\\mbus\\MBusTestStepInputFactory.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\interop\\mbus\\MBusTestStepInputFactory.cs", "RelativeToolTip": "..\\..\\..\\src\\interop\\mbus\\MBusTestStepInputFactory.cs", "ViewState": "AgIAAAUAAAAAAAAAAAAgwBYAAAAhAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-01-09T09:43:38.14Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 39, "Title": "TestSuite.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\TestSuite.cs", "RelativeDocumentMoniker": "..\\..\\..\\src\\TestSuite.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\TestSuite.cs", "RelativeToolTip": "..\\..\\..\\src\\TestSuite.cs", "ViewState": "AgIAAM4AAAAAAAAAAAAowN8AAAA7AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-01-03T05:33:44.335Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 40, "Title": "JsonPathExpression.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\expressions\\JsonPathExpression.cs", "RelativeDocumentMoniker": "..\\..\\..\\src\\expressions\\JsonPathExpression.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\expressions\\JsonPathExpression.cs", "RelativeToolTip": "..\\..\\..\\src\\expressions\\JsonPathExpression.cs", "ViewState": "AgIAAAQAAAAAAAAAAAAwwBcAAAAuAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-01-09T09:40:59.995Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 41, "Title": "CmdTestStepInputFactory.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\cmd\\CmdTestStepInputFactory.cs", "RelativeDocumentMoniker": "..\\..\\..\\src\\cmd\\CmdTestStepInputFactory.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\cmd\\CmdTestStepInputFactory.cs", "RelativeToolTip": "..\\..\\..\\src\\cmd\\CmdTestStepInputFactory.cs", "ViewState": "AgIAAAEAAAAAAAAAAAAgwBIAAAAtAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-01-09T09:19:54.004Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 45, "Title": "EqualExpression.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\expressions\\EqualExpression.cs", "RelativeDocumentMoniker": "..\\..\\..\\src\\expressions\\EqualExpression.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\expressions\\EqualExpression.cs", "RelativeToolTip": "..\\..\\..\\src\\expressions\\EqualExpression.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABUAAAAMAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-01-07T07:12:55.772Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 42, "Title": "TestSuiteTests.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\tests\\unit\\src\\TestSuiteTests.cs", "RelativeDocumentMoniker": "..\\src\\TestSuiteTests.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\tests\\unit\\src\\TestSuiteTests.cs", "RelativeToolTip": "..\\src\\TestSuiteTests.cs", "ViewState": "AgIAAFYAAAAAAAAAAAAewG8AAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-01-03T02:55:44.603Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 43, "Title": "BinaryExpression.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\expressions\\BinaryExpression.cs", "RelativeDocumentMoniker": "..\\..\\..\\src\\expressions\\BinaryExpression.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\expressions\\BinaryExpression.cs", "RelativeToolTip": "..\\..\\..\\src\\expressions\\BinaryExpression.cs", "ViewState": "AgIAAAkAAAAAAAAAAAApwBsAAABdAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-01-07T07:15:33.021Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 44, "Title": "ContainExpression.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\expressions\\ContainExpression.cs", "RelativeDocumentMoniker": "..\\..\\..\\src\\expressions\\ContainExpression.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\expressions\\ContainExpression.cs", "RelativeToolTip": "..\\..\\..\\src\\expressions\\ContainExpression.cs", "ViewState": "AgIAAAYAAAAAAAAAAAAAAAsAAABnAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-01-07T07:17:21.781Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 50, "Title": "IAsserter.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\assertions\\IAsserter.cs", "RelativeDocumentMoniker": "..\\..\\..\\src\\assertions\\IAsserter.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\assertions\\IAsserter.cs", "RelativeToolTip": "..\\..\\..\\src\\assertions\\IAsserter.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABwAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-01-06T15:35:52.932Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 46, "Title": "TestStepOutputSettings.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\TestStepOutputSettings.cs", "RelativeDocumentMoniker": "..\\..\\..\\src\\TestStepOutputSettings.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\TestStepOutputSettings.cs", "RelativeToolTip": "..\\..\\..\\src\\TestStepOutputSettings.cs", "ViewState": "AgIAAAMAAAAAAAAAAAAAAB0AAABLAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-01-07T06:32:11.895Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 47, "Title": "TestFactoryTests.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\tests\\unit\\src\\TestFactoryTests.cs", "RelativeDocumentMoniker": "..\\src\\TestFactoryTests.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\tests\\unit\\src\\TestFactoryTests.cs", "RelativeToolTip": "..\\src\\TestFactoryTests.cs", "ViewState": "AgIAAAYAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-01-06T16:35:54.006Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 49, "Title": "TestStepFromYamlDataTests.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\tests\\unit\\src\\TestStepFromYamlDataTests.cs", "RelativeDocumentMoniker": "..\\src\\TestStepFromYamlDataTests.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\tests\\unit\\src\\TestStepFromYamlDataTests.cs", "RelativeToolTip": "..\\src\\TestStepFromYamlDataTests.cs", "ViewState": "AgIAAPsAAAAAAAAAAAAewAoBAAA0AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-01-06T15:37:12.702Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 48, "Title": "AsserterBasedRetryConditionTests.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\tests\\unit\\src\\AsserterBasedRetryConditionTests.cs", "RelativeDocumentMoniker": "..\\src\\AsserterBasedRetryConditionTests.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\tests\\unit\\src\\AsserterBasedRetryConditionTests.cs", "RelativeToolTip": "..\\src\\AsserterBasedRetryConditionTests.cs", "ViewState": "AgIAAEwAAAAAAAAAAAApwFwAAABAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-01-06T15:38:47.925Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 54, "Title": "TestContextTests.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\tests\\unit\\src\\TestContextTests.cs", "RelativeDocumentMoniker": "..\\src\\TestContextTests.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\tests\\unit\\src\\TestContextTests.cs", "RelativeToolTip": "..\\src\\TestContextTests.cs", "ViewState": "AgIAAAwAAAAAAAAAAAAAACIAAAA/AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-11-19T08:05:41.734Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 51, "Title": "TestSuiteConfig.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\TestSuiteConfig.cs", "RelativeDocumentMoniker": "..\\..\\..\\src\\TestSuiteConfig.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\TestSuiteConfig.cs", "RelativeToolTip": "..\\..\\..\\src\\TestSuiteConfig.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA8AAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-01-03T05:33:38.75Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 53, "Title": "HttpTestStepExecutorTests.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\tests\\unit\\src\\HttpTestStepExecutorTests.cs", "RelativeDocumentMoniker": "..\\src\\HttpTestStepExecutorTests.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\tests\\unit\\src\\HttpTestStepExecutorTests.cs", "RelativeToolTip": "..\\src\\HttpTestStepExecutorTests.cs", "ViewState": "AgIAACUAAAAAAAAAAAAgwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-01-03T04:12:21.741Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 52, "Title": "TestStepExecutionSettingsTests.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\tests\\unit\\src\\TestStepExecutionSettingsTests.cs", "RelativeDocumentMoniker": "..\\src\\TestStepExecutionSettingsTests.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\tests\\unit\\src\\TestStepExecutionSettingsTests.cs", "RelativeToolTip": "..\\src\\TestStepExecutionSettingsTests.cs", "ViewState": "AgIAAEQAAAAAAAAAAAAIwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-01-03T04:26:19.606Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 67, "Title": "HttpTestStepExecutor.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\http\\HttpTestStepExecutor.cs", "RelativeDocumentMoniker": "..\\..\\..\\src\\http\\HttpTestStepExecutor.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\http\\HttpTestStepExecutor.cs", "RelativeToolTip": "..\\..\\..\\src\\http\\HttpTestStepExecutor.cs", "ViewState": "AgIAAC0AAAAAAAAAAAAwwEAAAAAdAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-12T16:26:22.637Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 56, "Title": "AsserterFactory.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\assertions\\AsserterFactory.cs", "RelativeDocumentMoniker": "..\\..\\..\\src\\assertions\\AsserterFactory.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\assertions\\AsserterFactory.cs", "RelativeToolTip": "..\\..\\..\\src\\assertions\\AsserterFactory.cs", "ViewState": "AgIAAEEAAAAAAAAAAAAewFMAAABGAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-11-19T10:57:26.6Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 65, "Title": "Expect.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\assertions\\Expect.cs", "RelativeDocumentMoniker": "..\\..\\..\\src\\assertions\\Expect.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\assertions\\Expect.cs", "RelativeToolTip": "..\\..\\..\\src\\assertions\\Expect.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAQAAAADAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-16T02:18:29.972Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 55, "Title": "JTokenExtensions.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\utils\\JTokenExtensions.cs", "RelativeDocumentMoniker": "..\\..\\..\\src\\utils\\JTokenExtensions.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\utils\\JTokenExtensions.cs", "RelativeToolTip": "..\\..\\..\\src\\utils\\JTokenExtensions.cs", "ViewState": "AgIAAAIAAAAAAAAAAIAwwBYAAAAJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-01-01T07:08:12.801Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 60, "Title": "TestStepExecutionSettings.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\TestStepExecutionSettings.cs", "RelativeDocumentMoniker": "..\\..\\..\\src\\TestStepExecutionSettings.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\TestStepExecutionSettings.cs", "RelativeToolTip": "..\\..\\..\\src\\TestStepExecutionSettings.cs", "ViewState": "AgIAACIAAAAAAAAAAAAcwDIAAAANAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-26T06:41:32.342Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 57, "Title": "MBusTestStepFactoryTests.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\tests\\unit\\src\\MBusTestStepFactoryTests.cs", "RelativeDocumentMoniker": "..\\src\\MBusTestStepFactoryTests.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\tests\\unit\\src\\MBusTestStepFactoryTests.cs", "RelativeToolTip": "..\\src\\MBusTestStepFactoryTests.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABQAAAAFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-30T10:37:18.137Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 61, "Title": "BaseTestStepExecutorTests.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\tests\\unit\\src\\BaseTestStepExecutorTests.cs", "RelativeDocumentMoniker": "..\\src\\BaseTestStepExecutorTests.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\tests\\unit\\src\\BaseTestStepExecutorTests.cs", "RelativeToolTip": "..\\src\\BaseTestStepExecutorTests.cs", "ViewState": "AgIAAH8AAAAAAAAAAAAWwBMAAAAMAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-26T06:41:48.301Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 58, "Title": "MBusTestStepFactory.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\interop\\mbus\\MBusTestStepFactory.cs", "RelativeDocumentMoniker": "..\\..\\..\\src\\interop\\mbus\\MBusTestStepFactory.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\interop\\mbus\\MBusTestStepFactory.cs", "RelativeToolTip": "..\\..\\..\\src\\interop\\mbus\\MBusTestStepFactory.cs", "ViewState": "AgIAAEgAAAAAAAAAAAAowEsAAAAJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-30T10:38:41.471Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 59, "Title": "MBusTestStepOutput.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\interop\\mbus\\MBusTestStepOutput.cs", "RelativeDocumentMoniker": "..\\..\\..\\src\\interop\\mbus\\MBusTestStepOutput.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\interop\\mbus\\MBusTestStepOutput.cs", "RelativeToolTip": "..\\..\\..\\src\\interop\\mbus\\MBusTestStepOutput.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-10-21T04:18:19.011Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 68, "Title": "HttpTestStepInput.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\http\\HttpTestStepInput.cs", "RelativeDocumentMoniker": "..\\..\\..\\src\\http\\HttpTestStepInput.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\http\\HttpTestStepInput.cs", "RelativeToolTip": "..\\..\\..\\src\\http\\HttpTestStepInput.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABEAAAAkAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-12T12:29:21.231Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 64, "Title": "Assert.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\assertions\\Assert.cs", "RelativeDocumentMoniker": "..\\..\\..\\src\\assertions\\Assert.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\assertions\\Assert.cs", "RelativeToolTip": "..\\..\\..\\src\\assertions\\Assert.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA8AAAAwAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-16T02:28:03.365Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 62, "Title": "BaseTestStepExecutor.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\BaseTestStepExecutor.cs", "RelativeDocumentMoniker": "..\\..\\..\\src\\BaseTestStepExecutor.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\BaseTestStepExecutor.cs", "RelativeToolTip": "..\\..\\..\\src\\BaseTestStepExecutor.cs", "ViewState": "AgIAAC8AAAAAAAAAAAAIwEoAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-13T11:32:49.299Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 63, "Title": "HttpTestStepOutputSettings.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\http\\HttpTestStepOutputSettings.cs", "RelativeDocumentMoniker": "..\\..\\..\\src\\http\\HttpTestStepOutputSettings.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\http\\HttpTestStepOutputSettings.cs", "RelativeToolTip": "..\\..\\..\\src\\http\\HttpTestStepOutputSettings.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAkAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-17T08:28:46.33Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 66, "Title": "IRetryCondition.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\utils\\IRetryCondition.cs", "RelativeDocumentMoniker": "..\\..\\..\\src\\utils\\IRetryCondition.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\utils\\IRetryCondition.cs", "RelativeToolTip": "..\\..\\..\\src\\utils\\IRetryCondition.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAsAAAAYAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-13T11:41:42.562Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 70, "Title": "NotContainExpression.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\expressions\\NotContainExpression.cs", "RelativeDocumentMoniker": "..\\..\\..\\src\\expressions\\NotContainExpression.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\expressions\\NotContainExpression.cs", "RelativeToolTip": "..\\..\\..\\src\\expressions\\NotContainExpression.cs", "ViewState": "AgIAAAYAAAAAAAAAAAAAABEAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-11-25T08:24:55.731Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 69, "Title": "testlib", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\tests\\unit\\build\\testlib\\testlib.csproj", "RelativeDocumentMoniker": "testlib\\testlib.csproj", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\tests\\unit\\build\\testlib\\testlib.csproj", "RelativeToolTip": "testlib\\testlib.csproj", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000758|", "WhenOpened": "2024-12-10T05:48:36.298Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 73, "Title": "StartupStepTests.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\tests\\unit\\src\\StartupStepTests.cs", "RelativeDocumentMoniker": "..\\src\\StartupStepTests.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\tests\\unit\\src\\StartupStepTests.cs", "RelativeToolTip": "..\\src\\StartupStepTests.cs", "ViewState": "AgIAADgAAAAAAAAAAAAMwEkAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-11-27T05:56:13.18Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 72, "Title": "CmdTestStepExecutor.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\cmd\\CmdTestStepExecutor.cs", "RelativeDocumentMoniker": "..\\..\\..\\src\\cmd\\CmdTestStepExecutor.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\cmd\\CmdTestStepExecutor.cs", "RelativeToolTip": "..\\..\\..\\src\\cmd\\CmdTestStepExecutor.cs", "ViewState": "AgIAABAAAAAAAAAAAAA1wC0AAABXAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-11-25T07:08:57.195Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 71, "Title": "CmdTestStepInput.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\cmd\\CmdTestStepInput.cs", "RelativeDocumentMoniker": "..\\..\\..\\src\\cmd\\CmdTestStepInput.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\cmd\\CmdTestStepInput.cs", "RelativeToolTip": "..\\..\\..\\src\\cmd\\CmdTestStepInput.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABcAAAApAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-12-03T07:17:26.016Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 74, "Title": "CmdTestStepFactoryTests.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\tests\\unit\\src\\CmdTestStepFactoryTests.cs", "RelativeDocumentMoniker": "..\\src\\CmdTestStepFactoryTests.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\tests\\unit\\src\\CmdTestStepFactoryTests.cs", "RelativeToolTip": "..\\src\\CmdTestStepFactoryTests.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABwAAAAxAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-11-25T08:27:46.239Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 75, "Title": "CmdTestStepInputTemplate.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\cmd\\CmdTestStepInputTemplate.cs", "RelativeDocumentMoniker": "..\\..\\..\\src\\cmd\\CmdTestStepInputTemplate.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\cmd\\CmdTestStepInputTemplate.cs", "RelativeToolTip": "..\\..\\..\\src\\cmd\\CmdTestStepInputTemplate.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABQAAAAvAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-11-27T06:38:59.373Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 76, "Title": "HttpTestStepFactory.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\http\\HttpTestStepFactory.cs", "RelativeDocumentMoniker": "..\\..\\..\\src\\http\\HttpTestStepFactory.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\http\\HttpTestStepFactory.cs", "RelativeToolTip": "..\\..\\..\\src\\http\\HttpTestStepFactory.cs", "ViewState": "AgIAAE4AAAAAAAAAAAAowAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-11-26T16:20:44.507Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 77, "Title": "HttpTestStepFactoryTests.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\tests\\unit\\src\\HttpTestStepFactoryTests.cs", "RelativeDocumentMoniker": "..\\src\\HttpTestStepFactoryTests.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\tests\\unit\\src\\HttpTestStepFactoryTests.cs", "RelativeToolTip": "..\\src\\HttpTestStepFactoryTests.cs", "ViewState": "AgIAAAkAAAAAAAAAAAAAABEAAAAlAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-11-19T08:05:32.335Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 78, "Title": "RpcTestStepOutput.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\interop\\rpc\\RpcTestStepOutput.cs", "RelativeDocumentMoniker": "..\\..\\..\\src\\interop\\rpc\\RpcTestStepOutput.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\interop\\rpc\\RpcTestStepOutput.cs", "RelativeToolTip": "..\\..\\..\\src\\interop\\rpc\\RpcTestStepOutput.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-11-25T07:22:37.28Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 79, "Title": "NotContainExpressionTests.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\tests\\unit\\src\\NotContainExpressionTests.cs", "RelativeDocumentMoniker": "..\\src\\NotContainExpressionTests.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\tests\\unit\\src\\NotContainExpressionTests.cs", "RelativeToolTip": "..\\src\\NotContainExpressionTests.cs", "ViewState": "AgIAAEMAAAAAAAAAAAAAAEcAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-11-20T08:00:04.789Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 81, "Title": "GreaterExpression.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\expressions\\GreaterExpression.cs", "RelativeDocumentMoniker": "..\\..\\..\\src\\expressions\\GreaterExpression.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\expressions\\GreaterExpression.cs", "RelativeToolTip": "..\\..\\..\\src\\expressions\\GreaterExpression.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA8AAAAVAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-11-19T10:57:04.456Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 80, "Title": "LessOrEqualExpression.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\expressions\\LessOrEqualExpression.cs", "RelativeDocumentMoniker": "..\\..\\..\\src\\expressions\\LessOrEqualExpression.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\expressions\\LessOrEqualExpression.cs", "RelativeToolTip": "..\\..\\..\\src\\expressions\\LessOrEqualExpression.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA8AAAAVAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-11-19T10:57:19.198Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 87, "Title": "HttpTestStepInputTemplate.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\http\\HttpTestStepInputTemplate.cs", "RelativeDocumentMoniker": "..\\..\\..\\src\\http\\HttpTestStepInputTemplate.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\http\\HttpTestStepInputTemplate.cs", "RelativeToolTip": "..\\..\\..\\src\\http\\HttpTestStepInputTemplate.cs", "ViewState": "AgIAAAgAAAAAAAAAAAAQwAwAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-10-21T04:27:24.834Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 84, "Title": "MBusTestStepInput.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\interop\\mbus\\MBusTestStepInput.cs", "RelativeDocumentMoniker": "..\\..\\..\\src\\interop\\mbus\\MBusTestStepInput.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\interop\\mbus\\MBusTestStepInput.cs", "RelativeToolTip": "..\\..\\..\\src\\interop\\mbus\\MBusTestStepInput.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-11-05T06:46:22.806Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 82, "Title": "GreaterOrEqualExpression.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\expressions\\GreaterOrEqualExpression.cs", "RelativeDocumentMoniker": "..\\..\\..\\src\\expressions\\GreaterOrEqualExpression.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\expressions\\GreaterOrEqualExpression.cs", "RelativeToolTip": "..\\..\\..\\src\\expressions\\GreaterOrEqualExpression.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABAAAAAFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-11-06T10:24:10.84Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 83, "Title": "ITestStepOutput.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\ITestStepOutput.cs", "RelativeDocumentMoniker": "..\\..\\..\\src\\ITestStepOutput.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\ITestStepOutput.cs", "RelativeToolTip": "..\\..\\..\\src\\ITestStepOutput.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAqwA4AAAARAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-10-18T06:14:21.282Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 86, "Title": "HttpTestStepOutput.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\http\\HttpTestStepOutput.cs", "RelativeDocumentMoniker": "..\\..\\..\\src\\http\\HttpTestStepOutput.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\http\\HttpTestStepOutput.cs", "RelativeToolTip": "..\\..\\..\\src\\http\\HttpTestStepOutput.cs", "ViewState": "AgIAAA0AAAAAAAAAAAA1wCMAAAAeAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-10-21T11:27:52.331Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 85, "Title": "AssemblyInfo.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\tests\\etc\\AssemblyInfo.cs", "RelativeDocumentMoniker": "..\\..\\etc\\AssemblyInfo.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\tests\\etc\\AssemblyInfo.cs", "RelativeToolTip": "..\\..\\etc\\AssemblyInfo.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAQAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-10-22T10:16:57.949Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 88, "Title": "MBusTestStepInputTemplate.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\interop\\mbus\\MBusTestStepInputTemplate.cs", "RelativeDocumentMoniker": "..\\..\\..\\src\\interop\\mbus\\MBusTestStepInputTemplate.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\test\\service\\testlib\\src\\interop\\mbus\\MBusTestStepInputTemplate.cs", "RelativeToolTip": "..\\..\\..\\src\\interop\\mbus\\MBusTestStepInputTemplate.cs", "ViewState": "AgIAAAYAAAAAAAAAAAAAAAoAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2024-10-21T04:18:12.729Z", "EditorCaption": ""}]}]}]}