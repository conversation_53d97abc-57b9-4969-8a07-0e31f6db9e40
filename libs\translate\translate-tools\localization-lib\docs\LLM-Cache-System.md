# Speed Up Your Localization with Cache Files

## What happens when you run localization tools?

When you run localization tools on your code, they ask an AI: *"Does this text need to be translated?"*

The AI is smart, but it's also **slow** (30-60 seconds per file) and sometimes **expensive** (if you're using paid AI services).

## The magic cache file

After running the localization tool once, you'll find a new file in your project:

```
YourProject/
├── .llmcache.json          ← This magical file appeared!
├── src/
│   ├── Program.cs
│   └── ...
```

**This file remembers all the AI's decisions.** Next time you run the tool, instead of asking the AI again, it just looks up the answer instantly.

## How much faster?

- **First run**: 30-60 seconds per file ⏳
- **With cache**: 1-3 seconds per file ⚡
- **Big project**: Hours → Minutes 🚀

## How to reuse this cache file

### 1. Keep it for yourself
Just leave the `.llmcache.json` file in your project. Every time you run localization tools, they get faster and faster.

### 2. Share with your team
Copy the cache file to your teammates:

```bash
# Send your cache to a teammate
copy .llmcache.json \\shared\team-cache-2024-06-27.json

# Your teammate copies it to their project
copy \\shared\team-cache-2024-06-27.json .llmcache.json
```

Now your teammate gets instant results for all the text you already analyzed!

### 3. Put it in Git (recommended)
Add the cache file to your repository so everyone automatically gets the benefits:

```bash
git add .llmcache.json
git commit -m "Add AI cache for faster localization"
git push
```

**Result**: Every team member gets lightning-fast localization from day one.

## Common questions

### "Can I delete this file?"
Yes! If you delete `.llmcache.json`, it will be recreated next time you run the tool. You'll just lose the speed benefits until it's rebuilt.

### "How big does this file get?"
Usually 1-5 MB for typical projects. Large projects might be 10-20 MB. It's worth it for the speed!

### "What if I change AI settings?"
The cache is smart - different AI models get separate entries, so you won't get wrong results.

### "Should I put this in Git?"
**Yes, recommended!** Your whole team will thank you for the instant speed boost.

### "What if I don't want it in Git?"
Add this line to your `.gitignore` file:
```
.llmcache.json
```

## If something goes wrong

### Tool seems slow even with cache
Make sure you're running the exact same command on the same project folder.

### Getting errors about the cache file
Delete it and start fresh:
```bash
del .llmcache.json
# Run your localization tool again - cache will rebuild
```

### Want to start over
Just delete the cache file. The tool will create a new one automatically.

## Bottom line

This cache file is like a cheat sheet that remembers all the AI's work.

- **Keep it** = faster tools for you
- **Share it** = faster tools for your team
- **Ignore it** = it works automatically anyway

The localization tools handle everything behind the scenes. You just get the speed benefits!
