{"format": 1, "restore": {"D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-csharp\\tests\\unit\\build\\translate_unit_tests.csproj": {}}, "projects": {"D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-csharp\\tests\\unit\\build\\translate_csharp\\translate_csharp.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-csharp\\tests\\unit\\build\\translate_csharp\\translate_csharp.csproj", "projectName": "translate_csharp", "projectPath": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-csharp\\tests\\unit\\build\\translate_csharp\\translate_csharp.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-csharp\\tests\\unit\\build\\translate_csharp\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "D:\\kibisoft\\workarea\\VisionPlanner\\NGSS_nugets": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.400/PortableRuntimeIdentifierGraph.json"}}}, "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-csharp\\tests\\unit\\build\\translate_unit_tests.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-csharp\\tests\\unit\\build\\translate_unit_tests.csproj", "projectName": "translate_unit_tests", "projectPath": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-csharp\\tests\\unit\\build\\translate_unit_tests.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-csharp\\tests\\unit\\build\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "D:\\kibisoft\\workarea\\VisionPlanner\\NGSS_nugets": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {"D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-csharp\\tests\\unit\\build\\translate_csharp\\translate_csharp.csproj": {"projectPath": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-csharp\\tests\\unit\\build\\translate_csharp\\translate_csharp.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "dependencies": {"Microsoft.NET.Test.Sdk": {"target": "Package", "version": "[17.8.0, )"}, "Moq": {"target": "Package", "version": "[4.20.70, )"}, "NUnit": {"target": "Package", "version": "[3.14.0, )"}, "NUnit3TestAdapter": {"target": "Package", "version": "[4.5.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.400/PortableRuntimeIdentifierGraph.json"}}}}}