/*
 * Copyright 2021-present Alcon. All rights reserved.
 * Alcon proprietary/confidential.
 * Use is subject to license terms.
 */

using System;
using System.Collections.Generic;
using System.IO;
using System.Text.RegularExpressions;

namespace LocalizationLib;

/// <summary>
/// A naive, regex-based CsProcessor that implements ICsProcessor.
/// Finds string literals in .cs code using a regex, 
/// optionally replaces them with Translator.Instance.TranslateM("en:...").
/// </summary>
public class RegexCsProcessor : ICsProcessor
{
    // Our global list of replacements
    public List<ReplacementRecord> AllReplacementRecords { get; } = new List<ReplacementRecord>();

    // A basic regex that tries to match quoted string literals:
    //   "(?:\\.|[^"\\])*" 
    // This won't handle verbatim strings (@""), interpolated strings ($"") or raw strings (C# 11).
    // But it demonstrates a naive approach.
    private static readonly Regex _stringLiteralRegex = new Regex(
        @"(?<!\\)""(?<txt>(?:\\.|[^""\\])*)""",
        RegexOptions.Compiled
    );

    public FileProcessingResult ProcessCsFile(string csPath, string groupId)
    {
        // 1) Read the file
        string originalContent = File.ReadAllText(csPath);

        // We'll store the per-file results in a FileProcessingResult.
        var fileResult = new FileProcessingResult
        {
            FilePath = csPath,
            Changed = false
        };

        // 2) Find all string matches
        MatchCollection matches = _stringLiteralRegex.Matches(originalContent);

        // 3) Process from last to first so we can do in-place text substitution if needed
        for (int i = matches.Count - 1; i >= 0; i--)
        {
            var match = matches[i];
            string matchedText = match.Groups["txt"].Value; // unescaped string content

            // a) skip empty or whitespace-only
            if (string.IsNullOrWhiteSpace(matchedText))
                continue;

            // b) skip bracket/punctuation-only strings
            if (!HasAtLeastOneLetter(matchedText))
                continue;

            // c) skip if inside already-translated code or other checks
            //    For example, if you want to skip "Algorithm = ..." or "Notify(...)"
            //    you can do a naive snippet search around match.Index, or skip if
            //    we see "Translator.Instance.TranslateM" near match.Index.
            if (ShouldSkip(originalContent, match.Index))
                continue;

            // d) gather line/column for reporting
            (int line, int col) = GetLineColumn(originalContent, match.Index);

            var record = new ReplacementRecord
            {
                Property = "(string literal)",
                OriginalValue = matchedText,
                NewKey = $"en:{matchedText}",
                FilePath = csPath,
                LineNumber = line,
                Column = col,
                GroupId = groupId,
                Source = ExtractionSource.CsRegex
            };
            fileResult.Replacements.Add(record);
            AllReplacementRecords.Add(record);
        }

        return fileResult;
    }

    /// <summary>
    /// Rudimentary check for letters. If no letter, assume it's not user-facing text.
    /// </summary>
    private bool HasAtLeastOneLetter(string text)
    {
        foreach (char c in text)
        {
            if (char.IsLetter(c))
            {
                return true;
            }
        }
        return false;
    }

    /// <summary>
    /// Example skip logic:
    /// - skip if near "Translator.Instance.TranslateM(" 
    /// - skip if near "Algorithm =" 
    /// - skip if near "Notify("
    /// etc.
    /// This is naive: it searches around matchIndex in the raw text.
    /// </summary>
    private bool ShouldSkip(string fileContent, int matchIndex)
    {
        // Look up to ~50 chars backwards from the matchIndex
        int lookbackLen = 50;
        int startPos = Math.Max(0, matchIndex - lookbackLen);
        string snippet = fileContent.Substring(startPos, matchIndex - startPos);

        // a) skip if snippet contains "Translator.Instance.TranslateM("
        if (snippet.Contains("Translator.Instance.TranslateM("))
            return true;

        // b) skip if snippet contains "Algorithm ="
        if (snippet.Contains("Algorithm ="))
            return true;

        // c) skip if snippet contains "Notify("
        if (snippet.Contains("Notify(") || snippet.Contains("NotifyPropertyChanged("))
            return true;

        // d) skip if snippet contains "FindResource("
        if (snippet.Contains("FindResource("))
            return true;

        // e) skip if snippet contains "new Uri("
        if (snippet.Contains("new Uri("))
            return true;

        // f) skip if snippet contains "e.PropertyName"
        if (snippet.Contains("e.PropertyName"))
            return true;

        return false;
    }

    /// <summary>
    /// Helper method to compute line and column from a character index.
    /// </summary>
    private (int line, int column) GetLineColumn(string content, int index)
    {
        int line = 1, column = 1;
        for (int i = 0; i < index; i++)
        {
            if (content[i] == '\n')
            {
                line++;
                column = 1;
            }
            else
            {
                column++;
            }
        }
        return (line, column);
    }
}
