﻿
<Project Sdk="Microsoft.NET.Sdk.WindowsDesktop">
  <PropertyGroup Label="Globals">
    <ProjectGuid>{E947F683-9112-3425-B4CE-89F60C32F003}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <UseWPF>true</UseWPF>
    <Configurations>Debug;Release</Configurations>
    <EnableDefaultItems>false</EnableDefaultItems>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
    <ManagedAssembly>true</ManagedAssembly>
    <TargetFramework>net48</TargetFramework>
    <AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
    <OutputType>Library</OutputType>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)' == 'Debug'">
    <OutputPath>D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-text-manager\build\ui_extractor_tool\Debug\</OutputPath>
    <AssemblyName>ui_extractor_tool</AssemblyName>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    <DebugType>full</DebugType>
    <DefineConstants>TRACE;DEBUG;_DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <LangVersion>latest</LangVersion>
    <Optimize>false</Optimize>
    <Platform>x64</Platform>
    <WarningLevel>3</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)' == 'Release'">
    <OutputPath>D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-text-manager\build\ui_extractor_tool\Release\</OutputPath>
    <AssemblyName>ui_extractor_tool</AssemblyName>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    <DebugType>none</DebugType>
    <DefineConstants>TRACE;_DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR</DefineConstants>
    <ErrorReport>queue</ErrorReport>
    <LangVersion>latest</LangVersion>
    <Optimize>true</Optimize>
    <Platform>x64</Platform>
    <WarningLevel>1</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <None Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-uitext-extractor\CMakeLists.txt">
      <Link>CMakeLists.txt</Link>
    </None>
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="D:\kibisoft\workarea\git\interop\cmake\Globals.cmake" />
  </ItemGroup>
  <Target Condition="'$(Configuration)' == 'Debug'"
    Name="CustomCommand_Debug_246de7b198e8db12159b6e664986d449"
    Inputs="D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-uitext-extractor/CMakeLists.txt;D:\kibisoft\workarea\git\interop\cmake\Globals.cmake"
    Outputs="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-text-manager\build\ui_extractor_tool\CMakeFiles\generate.stamp"
    BeforeTargets="CoreCompile"
    DependsOnTargets="PrepareForBuild">
    <Exec Command="echo Building Custom Rule D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-uitext-extractor/CMakeLists.txt" />
    <Exec Command="setlocal&#10;&quot;C:\Program Files\CMake\bin\cmake.exe&quot; -SD:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-text-manager -BD:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-text-manager/build --check-stamp-file D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-text-manager/build/ui_extractor_tool/CMakeFiles/generate.stamp&#10;if %errorlevel% neq 0 goto :cmEnd&#10;:cmEnd&#10;endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone&#10;:cmErrorLevel&#10;exit /b %1&#10;:cmDone&#10;if %errorlevel% neq 0 goto :VCEnd&#10;:VCEnd" />
  </Target>
  <ItemGroup>
    <UpToDateCheckInput Include="D:\kibisoft\workarea\git\interop\cmake\Globals.cmake" />
  </ItemGroup>
  <Target Condition="'$(Configuration)' == 'Release'"
    Name="CustomCommand_Release_246de7b198e8db12159b6e664986d449"
    Inputs="D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-uitext-extractor/CMakeLists.txt;D:\kibisoft\workarea\git\interop\cmake\Globals.cmake"
    Outputs="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-text-manager\build\ui_extractor_tool\CMakeFiles\generate.stamp"
    BeforeTargets="CoreCompile"
    DependsOnTargets="PrepareForBuild">
    <Exec Command="echo Building Custom Rule D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-uitext-extractor/CMakeLists.txt" />
    <Exec Command="setlocal&#10;&quot;C:\Program Files\CMake\bin\cmake.exe&quot; -SD:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-text-manager -BD:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-text-manager/build --check-stamp-file D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-text-manager/build/ui_extractor_tool/CMakeFiles/generate.stamp&#10;if %errorlevel% neq 0 goto :cmEnd&#10;:cmEnd&#10;endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone&#10;:cmErrorLevel&#10;exit /b %1&#10;:cmDone&#10;if %errorlevel% neq 0 goto :VCEnd&#10;:VCEnd" />
  </Target>
  <ItemGroup>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-uitext-extractor\src\Classes\SourceInfo.cs">
      <Link>src\Classes\SourceInfo.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-uitext-extractor\src\Classes\UIExtractedTextInfo.cs">
      <Link>src\Classes\UIExtractedTextInfo.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-uitext-extractor\src\Extractor\IUITextExtractor.cs">
      <Link>src\Extractor\IUITextExtractor.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-uitext-extractor\src\Extractor\TextManager.cs">
      <Link>src\Extractor\TextManager.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-uitext-extractor\src\Extractor\UIExtractor.cs">
      <Link>src\Extractor\UIExtractor.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-uitext-extractor\src\Extractor\UIExtractorWindow.xaml.cs">
      <Link>src\Extractor\UIExtractorWindow.xaml.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-uitext-extractor\src\Extractor\WpfUITextExtractor.cs">
      <Link>src\Extractor\WpfUITextExtractor.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-uitext-extractor\src\Extractor\XmlTextExporter.cs">
      <Link>src\Extractor\XmlTextExporter.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-uitext-extractor\src\SourceMapping\IMatchingStrategy.cs">
      <Link>src\SourceMapping\IMatchingStrategy.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-uitext-extractor\src\SourceMapping\MatchingStrategies.cs">
      <Link>src\SourceMapping\MatchingStrategies.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-uitext-extractor\src\SourceMapping\UIElementSourceMapper.cs">
      <Link>src\SourceMapping\UIElementSourceMapper.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-uitext-extractor\src\Utils\ExcelExporter.cs">
      <Link>src\Utils\ExcelExporter.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-uitext-extractor\src\Utils\TextInfoExtensions.cs">
      <Link>src\Utils\TextInfoExtensions.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-uitext-extractor\src\Utils\TextMatchingUtils.cs">
      <Link>src\Utils\TextMatchingUtils.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-uitext-extractor\src\Utils\UIExtractorConfig.cs">
      <Link>src\Utils\UIExtractorConfig.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-uitext-extractor\src\Utils\UIExtractorConverters.cs">
      <Link>src\Utils\UIExtractorConverters.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-uitext-extractor\src\Utils\UiUtils.cs">
      <Link>src\Utils\UiUtils.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-uitext-extractor\src\ViewModels\BaseViewModel.cs">
      <Link>src\ViewModels\BaseViewModel.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-uitext-extractor\src\ViewModels\FileOperationsViewModel.cs">
      <Link>src\ViewModels\FileOperationsViewModel.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-uitext-extractor\src\ViewModels\FilterViewModel.cs">
      <Link>src\ViewModels\FilterViewModel.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-uitext-extractor\src\ViewModels\ProgressViewModel.cs">
      <Link>src\ViewModels\ProgressViewModel.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-uitext-extractor\src\ViewModels\SourceMappingWindowViewModel.cs">
      <Link>src\ViewModels\SourceMappingWindowViewModel.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-uitext-extractor\src\ViewModels\StatusViewModel.cs">
      <Link>src\ViewModels\StatusViewModel.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-uitext-extractor\src\ViewModels\UIExtractorViewModel.cs">
      <Link>src\ViewModels\UIExtractorViewModel.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-uitext-extractor\src\ViewModels\UITextInfoWindowViewModel.cs">
      <Link>src\ViewModels\UITextInfoWindowViewModel.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-uitext-extractor\src\Views\BulkActions.xaml.cs">
      <Link>src\Views\BulkActions.xaml.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-uitext-extractor\src\Views\FileSelectionPanel.xaml.cs">
      <Link>src\Views\FileSelectionPanel.xaml.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-uitext-extractor\src\Views\FilterPanel.xaml.cs">
      <Link>src\Views\FilterPanel.xaml.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-uitext-extractor\src\Views\SourceMappingWindow.xaml.cs">
      <Link>src\Views\SourceMappingWindow.xaml.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-uitext-extractor\src\Views\TextGrid.xaml.cs">
      <Link>src\Views\TextGrid.xaml.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-uitext-extractor\src\Views\UITextInfoWindow.xaml.cs">
      <Link>src\Views\UITextInfoWindow.xaml.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\src\common\ProjectTextExtractorConfig.cs">
      <Link>ProjectTextExtractorConfig.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\src\common\ProjectTextExtractorConfigManager.cs">
      <Link>ProjectTextExtractorConfigManager.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\src\utils\LLMConfigPathResolver.cs">
      <Link>LLMConfigPathResolver.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\src\utils\LocalizationUtils.cs">
      <Link>LocalizationUtils.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\src\utils\PathUtils.cs">
      <Link>PathUtils.cs</Link>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <Page Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-uitext-extractor\src\Extractor\UIExtractorWindow.xaml">
      <Link>src\Extractor\UIExtractorWindow.xaml</Link>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-uitext-extractor\src\Style\UIExtractorStyles.xaml">
      <Link>src\Style\UIExtractorStyles.xaml</Link>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-uitext-extractor\src\Views\BulkActions.xaml">
      <Link>src\Views\BulkActions.xaml</Link>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-uitext-extractor\src\Views\FileSelectionPanel.xaml">
      <Link>src\Views\FileSelectionPanel.xaml</Link>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-uitext-extractor\src\Views\FilterPanel.xaml">
      <Link>src\Views\FilterPanel.xaml</Link>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-uitext-extractor\src\Views\SourceMappingWindow.xaml">
      <Link>src\Views\SourceMappingWindow.xaml</Link>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-uitext-extractor\src\Views\TextGrid.xaml">
      <Link>src\Views\TextGrid.xaml</Link>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-uitext-extractor\src\Views\UITextInfoWindow.xaml">
      <Link>src\Views\UITextInfoWindow.xaml</Link>
      <SubType>Designer</SubType>
    </Page>
  </ItemGroup>
  <ItemGroup>
    <Reference Include="System.Windows.Forms">
      <CopyLocalSatelliteAssemblies>true</CopyLocalSatelliteAssemblies>
      <ReferenceOutputAssembly>true</ReferenceOutputAssembly>
    </Reference>
    <Reference Include="System.Drawing">
      <CopyLocalSatelliteAssemblies>true</CopyLocalSatelliteAssemblies>
      <ReferenceOutputAssembly>true</ReferenceOutputAssembly>
    </Reference>
    <Reference Include="System.Web">
      <CopyLocalSatelliteAssemblies>true</CopyLocalSatelliteAssemblies>
      <ReferenceOutputAssembly>true</ReferenceOutputAssembly>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="CommunityToolkit.Mvvm" Version="8.2.2" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-text-manager\build\ZERO_CHECK.vcxproj">
      <Project>{973F07E1-9CA2-3229-98F3-3F350DB36A82}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
  </ItemGroup>
</Project>