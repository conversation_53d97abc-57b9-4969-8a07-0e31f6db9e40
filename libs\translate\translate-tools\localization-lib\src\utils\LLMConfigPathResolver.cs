using System;
using System.IO;

namespace LocalizationLib;

public static class LLMConfigPathResolver
{
    public static string ResolveLLMConfigPath()
    {
        // 1. First try the executable directory
        string exePath = AppDomain.CurrentDomain.BaseDirectory;
        string exeConfigPath = Path.Combine(exePath, "llmconfig.json");
        if (File.Exists(exeConfigPath))
        {
            return exeConfigPath;
        }

        // 2. Try the relative path from the assembly location
        string assemblyLocation = typeof(LLMConfigPathResolver).Assembly.Location;
        string assemblyDir = Path.GetDirectoryName(assemblyLocation);
        if (assemblyDir != null)
        {
            string relativeConfigPath = Path.Combine(assemblyDir, "..", "..", "etc", "llmconfig.json");
            string fullRelativePath = Path.GetFullPath(relativeConfigPath);
            if (File.Exists(fullRelativePath))
            {
                return fullRelativePath;
            }
        }

        // 3. Not found in any location
        return null;
    }
} 