using System;
using System.Collections.Generic;

namespace LocalizationLib
{
    public class ProjectTextExtractorConfig
    {
        public string ProjectPath { get; set; }
        public DateTime LastExtraction { get; set; }
        public Dictionary<string, ExtractedTextInfo> ExtractedTexts { get; set; } = new();
        public Dictionary<string, UITextInfo> UITexts { get; set; } = new();
        public List<string> SkipPatterns { get; set; } = new(); // Default skip patterns
        public Dictionary<string, HashSet<string>> CustomControlNonDependencyProperties { get; set; } = new();
    }

    public class UITextInfo
    {
        public string Id { get; set; }
        public string Text { get; set; }
        public string ControlType { get; set; }
        public string ControlName { get; set; }
        public string Group { get; set; } 
        public string FontClass { get; set; }  // For UI elements
        public double FontSize { get; set; }  // For UI elements
        public int Width { get; set; }  // For UI elements
        public int Height { get; set; }  // For UI elements
        public string FontFamily { get; set; }  // For UI elements
        public int FontWeightValue { get; set; }  // For UI elements

        public string BaseUri { get; set; }           // from BaseUriHelper
        public string ElementName { get; set; }     // from (element as FrameworkElement)?.Name
        public string AutomationId { get; set; }    // from AutomationProperties.GetAutomationId

        public string SourceKey { get; set; }  // Key to ExtractedTexts dictionary
        public DateTime LastUpdated { get; set; }
        public TextStatus Status { get; set; } = TextStatus.Open;
        public string Notes { get; set; }
        public bool CanChangeFont { get; set; }
        public bool Wrap { get; set; }
        public string Category { get; set; }

        /// <summary>
        /// Gets the numeric ID for backward compatibility with older code
        /// </summary>
        /// <returns>Integer ID if Id can be parsed as integer, otherwise a hash of the Id string</returns>
        public int GetNumericId()
        {
            if (string.IsNullOrEmpty(Id))
                return 0;
            
            if (int.TryParse(Id, out int numericId))
                return numericId;
            
            return LocalizationUtils.HashString(Id);
        }
        
        /// <summary>
        /// Create a new UITextInfo with an automatically generated ID
        /// </summary>
        /// <param name="text">Text content</param>
        /// <param name="controlType">Type of control</param>
        /// <param name="controlName">Name of the control</param>
        /// <param name="windowName">Window name/URI</param>
        /// <returns>New UITextInfo instance with generated ID</returns>
        public static UITextInfo Create(string text, string controlType, string controlName, Uri baseUrl)
        {
            string id = LocalizationUtils.GenerateUITextId(text, controlType, controlName, baseUrl);
            
            return new UITextInfo
            {
                Id = id,
                Text = text,
                ControlType = controlType,
                ControlName = controlName,
                BaseUri = baseUrl?.ToString(),
                LastUpdated = DateTime.UtcNow
            };
        }

    }

    public class ExtractedTextInfo
    {
        public string FilePath { get; set; }  // Relative to project path
        public int LineNumber { get; set; }
        public int Column { get; set; }
        public int UpdatedLineNumber {get; set;} // lineNumber after replacement
        public int UpdatedColumn {get;set;}      // column after replacement
        public string OriginalText { get; set; }
        public string Property { get; set; }  // For XAML: Content, Text, Header, etc.
        public string GroupId { get; set; }
        public string TextHash { get; set; }  // hash(text)
        public ExtractionSource Source { get; set; }
        public DateTime LastUpdated { get; set; }
        public bool NeedsTranslation { get; set; }
        public TextStatus Status { get; set; } = TextStatus.Open;
        public List<string> Notes { get; set; } = new();
        public string LangTextID { get; set; }
        public string XamlControlType { get; set; }
        public bool IsDependencyProperty { get; set; } = true;
    }



    public enum ExtractionSource
    {
        Xaml,
        CsRoslyn,
        CsRegex,
        CsLlm,
        CppRegex
    }

    public enum TextStatus
    {
        Open,
        Approved,
        Rejected,
        Resolved
    }

    /// Contains information about replacements and notes for a single XAML file.
    /// </summary>
    public class FileProcessingResult
    {
        public string FilePath { get; set; }
        public bool Changed { get; set; } = false;
        public List<ReplacementRecord> Replacements { get; set; } = new List<ReplacementRecord>();
        public List<string> Notes { get; set; } = new List<string>();
    }

    /// <summary>
    /// Represents a single replacement record with location and group details.
    /// </summary>
    public class ReplacementRecord
    {
        public string Property { get; set; }
        public string OriginalValue { get; set; }
        public string NewKey { get; set; }
        public string FilePath { get; set; }
        public int LineNumber { get; set; }
        public int Column { get; set; }
        /// <summary>
        /// The group identifier (e.g., "{projectname.ClassName}") that indicates which group in the XML this resource belongs to.
        /// </summary>
        public string GroupId { get; set; }

        public ExtractionSource Source { get; set; }

        public List<string> Notes { get; set; } = new List<string>();

        public string LineContent { get; set; }

        public bool NeedsTranslation { get; set; } = true;

        /// <summary>
        /// Indicates whether the property is a dependency property that can be bound.
        /// </summary>
        public bool IsDependencyProperty { get; set; } = true;
        public string ControlType { get; set; } = string.Empty;
        /// <summary>
        /// Indicates whether the element is a root-level element in XAML (e.g. Window, UserControl, etc.)
        /// </summary>
        public bool IsRootElement { get; set; } = false;
    }

    public enum ExtractionMode
    {
        RestructureOnly,
        ExtractionOnly,
        RestructureAndExtract
    }

    public class CodeRestructureOptions
    {
        public bool ConvertStringInterpolation { get; set; }
        public bool ConvertStringConcatenation { get; set; }
        public bool ConvertXamlTextToCodeBehind { get; set; }
        // Add more options as needed
    }
}