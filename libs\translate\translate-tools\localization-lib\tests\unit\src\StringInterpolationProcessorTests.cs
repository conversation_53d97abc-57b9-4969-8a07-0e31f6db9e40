using NUnit.Framework;
using System;
using System.IO;
using LocalizationLib;

namespace LocalizationLib.Tests
{
    [TestFixture]
    public class StringInterpolationProcessorTests
    {
        private StringInterpolationProcessor _processor;
        private string _tempFile;

        [SetUp]
        public void Setup()
        {
            _processor = new StringInterpolationProcessor();
            _tempFile = Path.GetTempFileName();
        }

        [TearDown]
        public void TearDown()
        {
            if (File.Exists(_tempFile))
            {
                File.Delete(_tempFile);
            }
        }

        [Test]
        public void ProcessFile_BasicInterpolation_ConvertedCorrectly()
        {
            // Arrange
            string code = "var message = $\"Hello, {name}!\";";
            File.WriteAllText(_tempFile, code);

            // Act
            var result = _processor.ProcessFile(_tempFile);

            // Assert
            Assert.IsTrue(result.Changed, "File should be changed by conversion");
            Assert.AreEqual(
                "var message = string.Format(\"Hello, {0}!\", name);",
                result.UpdatedContent.Trim()
            );
            Assert.AreEqual(1, result.Conversions.Count);
        }

        [Test]
        public void ProcessFile_MultipleInterpolationHoles_ConvertedCorrectly()
        {
            // Arrange
            string code = "var message = $\"Loaded data for {ev.Count} patients out of {ev.MaxCount}\";";
            File.WriteAllText(_tempFile, code);

            // Act
            var result = _processor.ProcessFile(_tempFile);

            // Assert
            Assert.IsTrue(result.Changed);
            Assert.AreEqual(
                "var message = string.Format(\"Loaded data for {0} patients out of {1}\", ev.Count, ev.MaxCount);",
                result.UpdatedContent.Trim()
            );
            Assert.AreEqual(1, result.Conversions.Count);
        }

        [Test]
        public void ProcessFile_EscapedBraces_PreservedCorrectly()
        {
            // Arrange
            // Single-line
            string code = "var message = $\"Format example {{ fixed text, not a {variable} }}\";";
            File.WriteAllText(_tempFile, code);

            // Act
            var result = _processor.ProcessFile(_tempFile);

            // Assert
            Assert.IsTrue(result.Changed);
            // We expect: "Format example {{ fixed text, not a {0} }}"
            // i.e., the double braces around 'fixed text' remain double,
            // and the {variable} -> {0}
            Assert.AreEqual(
                "var message = string.Format(\"Format example {{ fixed text, not a {0} }}\", variable);",
                result.UpdatedContent.Trim()
            );
        }

        [Test]
        public void ProcessFile_SkipVerbatimStrings()
        {
            // Arrange
            // $@" ..." should remain untouched
            string code = "var message = $@\"This is a verbatim {sample} string\";";
            File.WriteAllText(_tempFile, code);

            // Act
            var result = _processor.ProcessFile(_tempFile);

            // Assert
            Assert.IsFalse(result.Changed);
            Assert.AreEqual(code, result.UpdatedContent);
            Assert.AreEqual(0, result.Conversions.Count);
        }

        [Test]
        public void ProcessFile_SkipMultilineStrings()
        {
            // Arrange
            // Even if it's not marked verbatim, if it goes across multiple lines, skip
            string code = @"var message = $""Line1 {val}
Line2 more {val2}"";";
            File.WriteAllText(_tempFile, code);

            // Act
            var result = _processor.ProcessFile(_tempFile);

            // Assert
            Assert.IsFalse(result.Changed, "Should skip multiline interpolations");
            Assert.AreEqual(code, result.UpdatedContent);
            Assert.AreEqual(0, result.Conversions.Count);
        }

        [Test]
        public void ProcessFile_InLineCommentSkipped()
        {
            // Arrange
            // There's an interpolation in a comment
            string code = @"// var debug = $""Debug {test}"";
// Another comment $""Unused {expr}""
var realCode = $""Actual {codeWork} to convert"";";
            File.WriteAllText(_tempFile, code);

            // Act
            var result = _processor.ProcessFile(_tempFile);

            // Assert
            Assert.IsTrue(result.Changed);
            // The first line remains as is, second line also remains as is,
            // third line is converted
            string[] lines = result.UpdatedContent.Split('\n');
            Assert.AreEqual("// var debug = $\"Debug {test}\";", lines[0].TrimEnd('\r'));
            Assert.AreEqual("// Another comment $\"Unused {expr}\"", lines[1].TrimEnd('\r'));
            Assert.IsTrue(lines[2].Contains("string.Format(\"Actual {0} to convert\", codeWork)"));
            Assert.AreEqual(1, result.Conversions.Count);
        }

        [Test]
        public void ProcessFile_NullCoalescingCase_ConvertedCorrectly()
        {
            // Arrange
            string code = "var message = $\"Current client id: {clientId ?? \\\"(none)\\\"}\";";
            File.WriteAllText(_tempFile, code);

            // Act
            var result = _processor.ProcessFile(_tempFile);

            // Assert
            Assert.IsTrue(result.Changed);
            string expected = "var message = string.Format(\"Current client id: {0}\", clientId ?? \\\"(none)\\\");";
            Assert.AreEqual(expected, result.UpdatedContent.Trim());
        }

        [Test]
        public void ProcessFile_TernaryOperatorCase_ConvertedCorrectly()
        {
            // Arrange
            string code = "var message = $\"Status: {(isEnabled ? \\\"Enabled\\\" : \\\"Disabled\\\").ToUpper()}\";";
            File.WriteAllText(_tempFile, code);

            // Act
            var result = _processor.ProcessFile(_tempFile);

            // Assert
            Assert.IsTrue(result.Changed);
            // We want:
            // var message = string.Format("Status: {0}", (isEnabled ? "Enabled" : "Disabled").ToUpper());
            Assert.IsTrue(result.UpdatedContent.Contains("string.Format(\"Status: {0}\", (isEnabled ? \\\"Enabled\\\" : \\\"Disabled\\\").ToUpper())"));
        }

        [Test]
        public void ProcessFile_FormatSpecifier_ConvertedCorrectly()
        {
            // Arrange
            string code = "var priceMessage = $\"Price: {price:C2}\";";
            File.WriteAllText(_tempFile, code);

            // Act
            var result = _processor.ProcessFile(_tempFile);

            // Assert
            Assert.IsTrue(result.Changed);
            Assert.AreEqual(
                "var priceMessage = string.Format(\"Price: {0:C2}\", price);",
                result.UpdatedContent.Trim()
            );
        }
    }
}
