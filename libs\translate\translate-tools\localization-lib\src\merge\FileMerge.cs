/*
 * Copyright 2021-present Alcon. All rights reserved.
 * Alcon proprietary/confidential.
 * Use is subject to license terms.
 */

using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;
using System.Xml.Linq;

namespace LocalizationLib;

public class FileMerge
{
    private readonly List<string> _supportedExtensions = [".xml"];
    private List<string> _baseFilelineNumbers = new List<string>();
    private List<string> _updatedFilelineNumbers = new List<string>();
    private string _baseFileName;
    private string _updatedFileName;
    private bool _forceMerge;

    /// <summary>
    /// Merges the updated file into the base file
    /// </summary>
    /// <param name="baseFile"></param>
    /// <param name="updatedFile"></param>
    /// <param name="outputFile"></param>
    public void Merge(string baseFile, string updatedFile, string outputFile, bool forceMerge = false)
    {
        _forceMerge = forceMerge;
        try
        {
            string extension = ValidateAndExtractExtension(baseFile, updatedFile, outputFile);
            switch (extension)
            {
                case ".xml":
                    _baseFileName = Path.GetFileName(baseFile);
                    _updatedFileName = Path.GetFileName(updatedFile);
                    _baseFilelineNumbers = File.ReadAllText(Path.GetFullPath(baseFile)).Split("\n").Select(line => line.Trim()).ToList();
                    _updatedFilelineNumbers = File.ReadAllText(Path.GetFullPath(updatedFile)).Split("\n").Select(line => line.Trim()).ToList();
                    if (MergeXMLFiles(baseFile, updatedFile, outputFile))
                    {
                        Console.WriteLine($"Files succesfully merged into {outputFile}");
                    }
                    break;
                default:
                    Console.WriteLine($"This extension isn't supported for now. Supported extensions: {string.Join(", ", _supportedExtensions)}");
                    break;
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine("Error : " + ex.Message);
        }
    }

    /// <summary>
    /// Merges 2 XML files
    /// <param name="baseFile"></param>
    /// <param name="updatedFile"></param>
    /// <param name="outputFile"></param>
    public bool MergeXMLFiles(string baseFile, string updatedFile, string outputFile)
    {
        try
        {
            XDocument xmlBaseDoc = XDocument.Load(Path.GetFullPath(baseFile));
            XDocument xmlUpdatedDoc = XDocument.Load(Path.GetFullPath(updatedFile));

            MergeElementIntoXML(xmlUpdatedDoc.Root, xmlBaseDoc);

            if (!File.Exists(outputFile))
            {
                File.WriteAllText(outputFile, "");
            }
            xmlBaseDoc.Save(outputFile);
            return true;
        }
        catch (Exception ex)
        {
            Console.WriteLine("Exception found:" + ex.Message);
            return false;
        }
    }

    /// <summary>
    /// Recursively merges element into the XML file
    /// </summary>
    /// <param name="element"></param>
    /// <param name="xmlFile"></param>
    private void MergeElementIntoXML(XElement element, XDocument xmlFile)
    {
        bool skipChildren = false; // boolean to skip processing element's children when it was recently added
        var matchedElem = FindMatchingElement(element, xmlFile);

        if (matchedElem != null)
        {
            var baseLineNumber = _baseFilelineNumbers.FindIndex(elem => NormalizeForComparison(elem) == NormalizeForComparison(matchedElem.ToString())) + 1;
            var updatedLineNumber = _updatedFilelineNumbers.FindIndex(elem => NormalizeForComparison(elem) == NormalizeForComparison(element.ToString())) + 1;

            // check for missing attributes
            foreach (var attr in element.Attributes())
            {
                var matchedAttr = matchedElem.Attribute(attr.Name.ToString());
                if (matchedAttr == null)
                {
                    Console.WriteLine($"[INFO] Updated element on line {baseLineNumber}: <{GetElementNameAndId(matchedElem)} /> to include attribute {attr.Name}=\"{attr.Value}\".");
                    matchedElem.SetAttributeValue(attr.Name, attr.Value);
                }
                else if (!matchedAttr.Value.Equals(attr.Value))
                {
                    if (_forceMerge)
                    {
                        Console.WriteLine($"[WARNING] Changed attribute value: \n  ({_baseFileName}, {baseLineNumber}): {matchedAttr.Name}=\"{matchedAttr.Value}\" into {attr.Name}=\"{attr.Value}\" from ({_updatedFileName},{updatedLineNumber})\"");
                        matchedElem.SetAttributeValue(attr.Name, attr.Value);
                    }
                    else
                    {
                        Console.WriteLine($"[WARNING] Merge couldn't be done automatically! Different attribute value: \n  ({_baseFileName}, {baseLineNumber}): {matchedAttr.Name}=\"{matchedAttr.Value}\" \n  ({_updatedFileName},{updatedLineNumber}): {attr.Name}=\"{attr.Value}\"");
                    }
                }
            }
        }
        else
        {
            var parentNode = FindMatchingParent(element.Parent, xmlFile);
            if (parentNode != null)
            {
                parentNode.Add(element);
                skipChildren = true;
                Console.WriteLine($"[INFO] Added new element : <{GetElementNameAndId(element)}>.");
            }
        }

        if (skipChildren)
        {
            return;
        }

        var elements = element?.Elements().ToList();
        for (int i = 0; i < elements.Count; i++)
        {
            MergeElementIntoXML(elements[i], xmlFile);
        }
    }

    /// <summary>
    /// Returns the unique identifier string after a predefined list of unique identifiers attributes
    /// </summary>
    /// <param name="element"></param>
    /// <returns></returns>
    private static string FindUniqueIdentifier(XElement element)
    {
        List<string> possibleUniqueId = ["name", "id"];
        foreach (var uniqueIdentifier in possibleUniqueId)
        {
            if (element?.Attribute(uniqueIdentifier) != null)
            {
                return uniqueIdentifier;
            }
        }
        return "notfound";
    }

    /// <summary>
    /// Returns the matching element by the unique identifier attribute, if there is an unique identifier attribute
    /// OR
    /// Returns the matching element by it's name, if the unique identifier attribute is missing
    /// </summary>
    /// <param name="element"></param>
    /// <param name="xmlFile"></param>
    /// <returns></returns>
    private XElement FindMatchingElement(XElement element, XDocument xmlFile)
    {
        // Ensure all parents exist and retrieve the correct parent
        var matchingParent = FindMatchingParent(element.Parent, xmlFile);
        if (matchingParent == null) return null;

        // Search for the matching element only within the found parent
        var uniqueIdentifier = FindUniqueIdentifier(element);
        if (uniqueIdentifier == "notfound")
        {
            return matchingParent.Element(element.Name);
        }

        if (matchingParent == xmlFile.Root)
        {
            return matchingParent;
        }

        return matchingParent.Elements(element.Name)
            .FirstOrDefault(e => e.Attribute(uniqueIdentifier)?.Value == element.Attribute(uniqueIdentifier)?.Value);
    }

    /// <summary>
    /// Returns the matching parent
    /// </summary>
    /// <param name="parent"></param>
    /// <param name="xmlFile"></param>
    /// <returns></returns>
    private XElement FindMatchingParent(XElement parent, XDocument xmlFile)
    {
        var uniqueIdentifier = FindUniqueIdentifier(parent);
        if (uniqueIdentifier == "notfound")
        {
            return xmlFile.Descendants(parent?.Name).FirstOrDefault();
        }
        // Find the matching parent in the document
        var matchingParent = xmlFile.Descendants(parent?.Name)
            .FirstOrDefault(e => e.Attribute(uniqueIdentifier)?.Value == parent.Attribute(uniqueIdentifier)?.Value);

        // Recursively ensure its parent exists as well
        return (matchingParent != null && FindMatchingParent(parent?.Parent, xmlFile) != null) ? matchingParent : xmlFile.Root;
    }

    /// <summary>
    /// Validates the files to have the same extension and returns it.
    /// </summary>
    /// <param name="baseFile"></param>
    /// <param name="updatedFile"></param>
    /// <param name="outputFile"></param>
    /// <returns></returns>
    /// <exception cref="Exception"></exception>
    private static string ValidateAndExtractExtension(string baseFile, string updatedFile, string outputFile)
    {
        if (Path.GetExtension(baseFile) != Path.GetExtension(updatedFile) && Path.GetExtension(baseFile) != Path.GetExtension(outputFile))
        {
            throw new Exception("Extensions should match");
        }

        string extension = Path.GetExtension(baseFile);
        switch (extension)
        {
            case ".xml":
                return extension;
            default:
                return null;
        }
    }

    /// <summary>
    /// Return element.Name combined with element.Id
    /// </summary>
    /// <param name="element"></param>
    /// <returns></returns>
    private string GetElementNameAndId(XElement element)
    {
        var uniqueId = FindUniqueIdentifier(element);
        string uniqueIdStr = "";
        if (uniqueId != "notfound")
        {
            uniqueIdStr = $" {uniqueId}=\"{element.Attribute(uniqueId).Value}\"";
        }
        return element.Name + uniqueIdStr;
    }

    /// <summary>
    /// Normalize string to have exact comparison (When convertin xmlElement to string there might be additional spacing added)
    /// </summary>
    /// <param name="str"></param>
    /// <returns></returns>
    private string NormalizeForComparison(string str)
    {
        return Regex.Replace(str.Trim(), @"\s*(?=/>)", ""); // Removes spaces before "/>"
    }
}