
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.30/Modules/CMakeDetermineSystem.cmake:205 (message)"
      - "CMakeLists.txt:3 (project)"
    message: |
      The system is: Windows - 10.0.22631 - AMD64
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-3.30/Modules/CMakeDetermineCSharpCompiler.cmake:23 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Compiling the CSharp compiler identification source file "CMakeCSharpCompilerId.cs" succeeded.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      0
      MSBuild version 17.11.9+a69bbaaf5 for .NET Framework
      Build started 1/24/2025 2:28:33 PM.
      
      Project "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-csharp\\tests\\unit\\build\\CMakeFiles\\3.30.1\\CompilerIdCSharp\\CompilerIdCSharp.csproj" on node 1 (default targets).
      PrepareForBuild:
        Creating directory "obj\\x64\\Debug\\".
      CoreCompile:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Current\\Bin\\Roslyn\\csc.exe /noconfig /nowarn:1701,1702 /fullpaths /nostdlib+ /platform:anycpu32bitpreferred /define:Platformx64;PlatformToolsetv143 /highentropyva+ /reference:"C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\mscorlib.dll" /reference:"C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Core.dll" /debug+ /out:obj\\x64\\Debug\\CompilerIdCSharp.exe /subsystemversion:6.00 /target:exe /utf8output /langversion:7.3 CMakeCSharpCompilerId.cs "obj\\x64\\Debug\\.NETFramework,Version=v4.7.2.AssemblyAttributes.cs"
        CompilerServer: server - server processed compilation - CompilerIdCSharp
      CopyFilesToOutputDirectory:
        Copying file from "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-csharp\\tests\\unit\\build\\CMakeFiles\\3.30.1\\CompilerIdCSharp\\obj\\x64\\Debug\\CompilerIdCSharp.exe" to "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-csharp\\tests\\unit\\build\\CMakeFiles\\3.30.1\\CompilerIdCSharp\\CompilerIdCSharp.exe".
        CompilerIdCSharp -> D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-csharp\\tests\\unit\\build\\CMakeFiles\\3.30.1\\CompilerIdCSharp\\CompilerIdCSharp.exe
        Copying file from "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-csharp\\tests\\unit\\build\\CMakeFiles\\3.30.1\\CompilerIdCSharp\\obj\\x64\\Debug\\CompilerIdCSharp.pdb" to "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-csharp\\tests\\unit\\build\\CMakeFiles\\3.30.1\\CompilerIdCSharp\\CompilerIdCSharp.pdb".
      PostBuildEvent:
        if not "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Current\\Bin\\Roslyn"=="" if exist "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Current\\Bin\\Roslyn\\csc.exe" set _CSC=C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Current\\Bin\\Roslyn
        if exist "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Current\\Bin\\amd64\\csc.exe" set _CSC=C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Current\\Bin\\amd64
        if "%_CSC%"=="" exit -1
        @echo CMAKE_CSharp_COMPILER=%_CSC%\\csc.exe
        CMAKE_CSharp_COMPILER=C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Current\\Bin\\Roslyn\\csc.exe
      Done Building Project "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-csharp\\tests\\unit\\build\\CMakeFiles\\3.30.1\\CompilerIdCSharp\\CompilerIdCSharp.csproj" (default targets).
      
      Build succeeded.
          0 Warning(s)
          0 Error(s)
      
      Time Elapsed 00:00:00.73
      
      
      Compilation of the CSharp compiler identification source "CMakeCSharpCompilerId.cs" produced "CompilerIdCSharp.csproj"
      
      Compilation of the CSharp compiler identification source "CMakeCSharpCompilerId.cs" produced "CompilerIdCSharp.exe"
      
      Compilation of the CSharp compiler identification source "CMakeCSharpCompilerId.cs" produced "CompilerIdCSharp.pdb"
      
      The CSharp compiler identification could not be found in:
        D:/kibisoft/workarea/git/interop/libs/translate/translate-csharp/tests/unit/build/CMakeFiles/3.30.1/CompilerIdCSharp/CompilerIdCSharp.csproj
      
      The CSharp compiler identification is Microsoft Visual Studio, found in:
        D:/kibisoft/workarea/git/interop/libs/translate/translate-csharp/tests/unit/build/CMakeFiles/3.30.1/CompilerIdCSharp/CompilerIdCSharp.exe
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.30/Modules/CMakeTestCSharpCompiler.cmake:34 (try_compile)"
      - "CMakeLists.txt:3 (project)"
    checks:
      - "Check for working C# compiler: C:/Program Files/Microsoft Visual Studio/2022/Professional/MSBuild/Current/Bin/Roslyn/csc.exe"
    directories:
      source: "D:/kibisoft/workarea/git/interop/libs/translate/translate-csharp/tests/unit/build/CMakeFiles/CMakeScratch/TryCompile-3034ve"
      binary: "D:/kibisoft/workarea/git/interop/libs/translate/translate-csharp/tests/unit/build/CMakeFiles/CMakeScratch/TryCompile-3034ve"
    cmakeVariables:
      CMAKE_CSharp_FLAGS: "/platform:x64 /define:TRACE"
      CMAKE_CSharp_FLAGS_DEBUG: "/debug:full /optimize- /warn:3 /errorreport:prompt /define:DEBUG"
      CMAKE_EXE_LINKER_FLAGS: ""
    buildResult:
      variable: "CMAKE_CSharp_COMPILER_WORKS"
      cached: true
      stdout: |
        Change Dir: 'D:/kibisoft/workarea/git/interop/libs/translate/translate-csharp/tests/unit/build/CMakeFiles/CMakeScratch/TryCompile-3034ve'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_945ef.csproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.11.9+a69bbaaf5 for .NET Framework
        Build started 1/24/2025 2:28:35 PM.
        
        Project "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-csharp\\tests\\unit\\build\\CMakeFiles\\CMakeScratch\\TryCompile-3034ve\\cmTC_945ef.csproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-csharp\\tests\\unit\\build\\CMakeFiles\\CMakeScratch\\TryCompile-3034ve\\Debug\\".
          Creating directory "obj\\x64\\Debug\\".
        CoreCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Current\\Bin\\Roslyn\\csc.exe /noconfig /nowarn:1701,1702 /fullpaths /nostdlib+ /platform:x64 /errorreport:prompt /warn:3 /define:TRACE;DEBUG /highentropyva+ /reference:"C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\mscorlib.dll" /reference:"C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Core.dll" /debug+ /debug:full /optimize- /out:obj\\x64\\Debug\\cmTC_945ef.exe /subsystemversion:6.00 /target:exe /utf8output /langversion:7.3 D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-csharp\\tests\\unit\\build\\CMakeFiles\\CMakeScratch\\TryCompile-3034ve\\testCSharpCompiler.cs "obj\\x64\\Debug\\.NETFramework,Version=v4.7.2.AssemblyAttributes.cs"
          CompilerServer: server - server processed compilation - cmTC_945ef
        CopyFilesToOutputDirectory:
          Copying file from "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-csharp\\tests\\unit\\build\\CMakeFiles\\CMakeScratch\\TryCompile-3034ve\\obj\\x64\\Debug\\cmTC_945ef.exe" to "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-csharp\\tests\\unit\\build\\CMakeFiles\\CMakeScratch\\TryCompile-3034ve\\Debug\\cmTC_945ef.exe".
          cmTC_945ef -> D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-csharp\\tests\\unit\\build\\CMakeFiles\\CMakeScratch\\TryCompile-3034ve\\Debug\\cmTC_945ef.exe
          Copying file from "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-csharp\\tests\\unit\\build\\CMakeFiles\\CMakeScratch\\TryCompile-3034ve\\obj\\x64\\Debug\\cmTC_945ef.pdb" to "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-csharp\\tests\\unit\\build\\CMakeFiles\\CMakeScratch\\TryCompile-3034ve\\Debug\\cmTC_945ef.pdb".
        Done Building Project "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-csharp\\tests\\unit\\build\\CMakeFiles\\CMakeScratch\\TryCompile-3034ve\\cmTC_945ef.csproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:00.36
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-3.30/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "D:/kibisoft/workarea/git/interop/libs/translate/translate-csharp/CMakeLists.txt:3 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      0
      MSBuild version 17.11.9+a69bbaaf5 for .NET Framework
      Build started 1/24/2025 2:28:36 PM.
      
      Project "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-csharp\\tests\\unit\\build\\CMakeFiles\\3.30.1\\CompilerIdC\\CompilerIdC.vcxproj" on node 1 (default targets).
      PrepareForBuild:
        Creating directory "Debug\\".
        Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
        Creating directory "Debug\\CompilerIdC.tlog\\".
      InitializeBuildStatus:
        Creating "Debug\\CompilerIdC.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
        Touching "Debug\\CompilerIdC.tlog\\unsuccessfulbuild".
      ClCompile:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Tools\\MSVC\\14.41.34120\\bin\\HostX64\\x64\\CL.exe /c /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc143.pdb" /external:W0 /Gd /TC /FC /errorReport:queue CMakeCCompilerId.c
        CMakeCCompilerId.c
      Link:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Tools\\MSVC\\14.41.34120\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdC.exe" /INCREMENTAL:NO /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdC.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdC.lib" /MACHINE:X64 Debug\\CMakeCCompilerId.obj
        CompilerIdC.vcxproj -> D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-csharp\\tests\\unit\\build\\CMakeFiles\\3.30.1\\CompilerIdC\\CompilerIdC.exe
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_C_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_C_COMPILER=C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Tools\\MSVC\\14.41.34120\\bin\\Hostx64\\x64\\cl.exe
      FinalizeBuildStatus:
        Deleting file "Debug\\CompilerIdC.tlog\\unsuccessfulbuild".
        Touching "Debug\\CompilerIdC.tlog\\CompilerIdC.lastbuildstate".
      Done Building Project "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-csharp\\tests\\unit\\build\\CMakeFiles\\3.30.1\\CompilerIdC\\CompilerIdC.vcxproj" (default targets).
      
      Build succeeded.
          0 Warning(s)
          0 Error(s)
      
      Time Elapsed 00:00:00.95
      
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CompilerIdC.exe"
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CompilerIdC.vcxproj"
      
      The C compiler identification is MSVC, found in:
        D:/kibisoft/workarea/git/interop/libs/translate/translate-csharp/tests/unit/build/CMakeFiles/3.30.1/CompilerIdC/CompilerIdC.exe
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-3.30/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "D:/kibisoft/workarea/git/interop/libs/translate/translate-csharp/CMakeLists.txt:3 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      0
      MSBuild version 17.11.9+a69bbaaf5 for .NET Framework
      Build started 1/24/2025 2:28:37 PM.
      
      Project "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-csharp\\tests\\unit\\build\\CMakeFiles\\3.30.1\\CompilerIdCXX\\CompilerIdCXX.vcxproj" on node 1 (default targets).
      PrepareForBuild:
        Creating directory "Debug\\".
        Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
        Creating directory "Debug\\CompilerIdCXX.tlog\\".
      InitializeBuildStatus:
        Creating "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
        Touching "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild".
      ClCompile:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Tools\\MSVC\\14.41.34120\\bin\\HostX64\\x64\\CL.exe /c /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc143.pdb" /external:W0 /Gd /TP /FC /errorReport:queue CMakeCXXCompilerId.cpp
        CMakeCXXCompilerId.cpp
      Link:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Tools\\MSVC\\14.41.34120\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdCXX.exe" /INCREMENTAL:NO /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdCXX.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdCXX.lib" /MACHINE:X64 Debug\\CMakeCXXCompilerId.obj
        CompilerIdCXX.vcxproj -> D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-csharp\\tests\\unit\\build\\CMakeFiles\\3.30.1\\CompilerIdCXX\\CompilerIdCXX.exe
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_CXX_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_CXX_COMPILER=C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Tools\\MSVC\\14.41.34120\\bin\\Hostx64\\x64\\cl.exe
      FinalizeBuildStatus:
        Deleting file "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild".
        Touching "Debug\\CompilerIdCXX.tlog\\CompilerIdCXX.lastbuildstate".
      Done Building Project "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-csharp\\tests\\unit\\build\\CMakeFiles\\3.30.1\\CompilerIdCXX\\CompilerIdCXX.vcxproj" (default targets).
      
      Build succeeded.
          0 Warning(s)
          0 Error(s)
      
      Time Elapsed 00:00:00.59
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.exe"
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.vcxproj"
      
      The CXX compiler identification is MSVC, found in:
        D:/kibisoft/workarea/git/interop/libs/translate/translate-csharp/tests/unit/build/CMakeFiles/3.30.1/CompilerIdCXX/CompilerIdCXX.exe
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.30/Modules/CMakeDetermineCompilerABI.cmake:74 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.30/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "D:/kibisoft/workarea/git/interop/libs/translate/translate-csharp/CMakeLists.txt:3 (project)"
    checks:
      - "Detecting C compiler ABI info"
    directories:
      source: "D:/kibisoft/workarea/git/interop/libs/translate/translate-csharp/tests/unit/build/CMakeFiles/CMakeScratch/TryCompile-yz5y96"
      binary: "D:/kibisoft/workarea/git/interop/libs/translate/translate-csharp/tests/unit/build/CMakeFiles/CMakeScratch/TryCompile-yz5y96"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_POSITION_INDEPENDENT_CODE: "ON"
    buildResult:
      variable: "CMAKE_C_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'D:/kibisoft/workarea/git/interop/libs/translate/translate-csharp/tests/unit/build/CMakeFiles/CMakeScratch/TryCompile-yz5y96'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_5d2b5.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.11.9+a69bbaaf5 for .NET Framework
        Build started 1/24/2025 2:28:38 PM.
        
        Project "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-csharp\\tests\\unit\\build\\CMakeFiles\\CMakeScratch\\TryCompile-yz5y96\\cmTC_5d2b5.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_5d2b5.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-csharp\\tests\\unit\\build\\CMakeFiles\\CMakeScratch\\TryCompile-yz5y96\\Debug\\".
          Creating directory "cmTC_5d2b5.dir\\Debug\\cmTC_5d2b5.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_5d2b5.dir\\Debug\\cmTC_5d2b5.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_5d2b5.dir\\Debug\\cmTC_5d2b5.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Tools\\MSVC\\14.41.34120\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_5d2b5.dir\\Debug\\\\" /Fd"cmTC_5d2b5.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-3.30\\Modules\\CMakeCCompilerABI.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.41.34120 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_5d2b5.dir\\Debug\\\\" /Fd"cmTC_5d2b5.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-3.30\\Modules\\CMakeCCompilerABI.c"
          CMakeCCompilerABI.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Tools\\MSVC\\14.41.34120\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-csharp\\tests\\unit\\build\\CMakeFiles\\CMakeScratch\\TryCompile-yz5y96\\Debug\\cmTC_5d2b5.exe" /INCREMENTAL /ILK:"cmTC_5d2b5.dir\\Debug\\cmTC_5d2b5.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"D:/kibisoft/workarea/git/interop/libs/translate/translate-csharp/tests/unit/build/CMakeFiles/CMakeScratch/TryCompile-yz5y96/Debug/cmTC_5d2b5.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"D:/kibisoft/workarea/git/interop/libs/translate/translate-csharp/tests/unit/build/CMakeFiles/CMakeScratch/TryCompile-yz5y96/Debug/cmTC_5d2b5.lib" /MACHINE:X64 cmTC_5d2b5.dir\\Debug\\CMakeCCompilerABI.obj
          cmTC_5d2b5.vcxproj -> D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-csharp\\tests\\unit\\build\\CMakeFiles\\CMakeScratch\\TryCompile-yz5y96\\Debug\\cmTC_5d2b5.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_5d2b5.dir\\Debug\\cmTC_5d2b5.tlog\\unsuccessfulbuild".
          Touching "cmTC_5d2b5.dir\\Debug\\cmTC_5d2b5.tlog\\cmTC_5d2b5.lastbuildstate".
        Done Building Project "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-csharp\\tests\\unit\\build\\CMakeFiles\\CMakeScratch\\TryCompile-yz5y96\\cmTC_5d2b5.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:00.62
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.30/Modules/CMakeDetermineCompilerABI.cmake:218 (message)"
      - "C:/Program Files/CMake/share/cmake-3.30/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "D:/kibisoft/workarea/git/interop/libs/translate/translate-csharp/CMakeLists.txt:3 (project)"
    message: |
      Parsed C implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?))("|,| |$)]
        linker tool for 'C': C:/Program Files/Microsoft Visual Studio/2022/Professional/VC/Tools/MSVC/14.41.34120/bin/HostX64/x64/link.exe
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.30/Modules/Internal/CMakeDetermineLinkerId.cmake:40 (message)"
      - "C:/Program Files/CMake/share/cmake-3.30/Modules/CMakeDetermineCompilerABI.cmake:255 (cmake_determine_linker_id)"
      - "C:/Program Files/CMake/share/cmake-3.30/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "D:/kibisoft/workarea/git/interop/libs/translate/translate-csharp/CMakeLists.txt:3 (project)"
    message: |
      Running the C compiler's linker: "C:/Program Files/Microsoft Visual Studio/2022/Professional/VC/Tools/MSVC/14.41.34120/bin/HostX64/x64/link.exe" "-v"
      Microsoft (R) Incremental Linker Version 14.41.34120.0
      Copyright (C) Microsoft Corporation.  All rights reserved.
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.30/Modules/CMakeDetermineCompilerABI.cmake:74 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.30/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "D:/kibisoft/workarea/git/interop/libs/translate/translate-csharp/CMakeLists.txt:3 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "D:/kibisoft/workarea/git/interop/libs/translate/translate-csharp/tests/unit/build/CMakeFiles/CMakeScratch/TryCompile-04nbrw"
      binary: "D:/kibisoft/workarea/git/interop/libs/translate/translate-csharp/tests/unit/build/CMakeFiles/CMakeScratch/TryCompile-04nbrw"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_CXX_SCAN_FOR_MODULES: "OFF"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_POSITION_INDEPENDENT_CODE: "ON"
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'D:/kibisoft/workarea/git/interop/libs/translate/translate-csharp/tests/unit/build/CMakeFiles/CMakeScratch/TryCompile-04nbrw'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_65997.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.11.9+a69bbaaf5 for .NET Framework
        Build started 1/24/2025 2:28:39 PM.
        
        Project "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-csharp\\tests\\unit\\build\\CMakeFiles\\CMakeScratch\\TryCompile-04nbrw\\cmTC_65997.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_65997.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-csharp\\tests\\unit\\build\\CMakeFiles\\CMakeScratch\\TryCompile-04nbrw\\Debug\\".
          Creating directory "cmTC_65997.dir\\Debug\\cmTC_65997.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_65997.dir\\Debug\\cmTC_65997.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_65997.dir\\Debug\\cmTC_65997.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Tools\\MSVC\\14.41.34120\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /std:c++17 /Fo"cmTC_65997.dir\\Debug\\\\" /Fd"cmTC_65997.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-3.30\\Modules\\CMakeCXXCompilerABI.cpp"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.41.34120 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /std:c++17 /Fo"cmTC_65997.dir\\Debug\\\\" /Fd"cmTC_65997.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-3.30\\Modules\\CMakeCXXCompilerABI.cpp"
          CMakeCXXCompilerABI.cpp
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Tools\\MSVC\\14.41.34120\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-csharp\\tests\\unit\\build\\CMakeFiles\\CMakeScratch\\TryCompile-04nbrw\\Debug\\cmTC_65997.exe" /INCREMENTAL /ILK:"cmTC_65997.dir\\Debug\\cmTC_65997.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"D:/kibisoft/workarea/git/interop/libs/translate/translate-csharp/tests/unit/build/CMakeFiles/CMakeScratch/TryCompile-04nbrw/Debug/cmTC_65997.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"D:/kibisoft/workarea/git/interop/libs/translate/translate-csharp/tests/unit/build/CMakeFiles/CMakeScratch/TryCompile-04nbrw/Debug/cmTC_65997.lib" /MACHINE:X64 cmTC_65997.dir\\Debug\\CMakeCXXCompilerABI.obj
          cmTC_65997.vcxproj -> D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-csharp\\tests\\unit\\build\\CMakeFiles\\CMakeScratch\\TryCompile-04nbrw\\Debug\\cmTC_65997.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_65997.dir\\Debug\\cmTC_65997.tlog\\unsuccessfulbuild".
          Touching "cmTC_65997.dir\\Debug\\cmTC_65997.tlog\\cmTC_65997.lastbuildstate".
        Done Building Project "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-csharp\\tests\\unit\\build\\CMakeFiles\\CMakeScratch\\TryCompile-04nbrw\\cmTC_65997.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:00.64
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.30/Modules/CMakeDetermineCompilerABI.cmake:218 (message)"
      - "C:/Program Files/CMake/share/cmake-3.30/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "D:/kibisoft/workarea/git/interop/libs/translate/translate-csharp/CMakeLists.txt:3 (project)"
    message: |
      Parsed CXX implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?))("|,| |$)]
        linker tool for 'CXX': C:/Program Files/Microsoft Visual Studio/2022/Professional/VC/Tools/MSVC/14.41.34120/bin/HostX64/x64/link.exe
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.30/Modules/Internal/CMakeDetermineLinkerId.cmake:40 (message)"
      - "C:/Program Files/CMake/share/cmake-3.30/Modules/CMakeDetermineCompilerABI.cmake:255 (cmake_determine_linker_id)"
      - "C:/Program Files/CMake/share/cmake-3.30/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "D:/kibisoft/workarea/git/interop/libs/translate/translate-csharp/CMakeLists.txt:3 (project)"
    message: |
      Running the CXX compiler's linker: "C:/Program Files/Microsoft Visual Studio/2022/Professional/VC/Tools/MSVC/14.41.34120/bin/HostX64/x64/link.exe" "-v"
      Microsoft (R) Incremental Linker Version 14.41.34120.0
      Copyright (C) Microsoft Corporation.  All rights reserved.
...

---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineSystem.cmake:205 (message)"
      - "CMakeLists.txt:3 (project)"
    message: |
      The system is: Windows - 10.0.22631 - AMD64
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCSharpCompiler.cmake:23 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Compiling the CSharp compiler identification source file "CMakeCSharpCompilerId.cs" succeeded.
      Compiler:  
      Build flags: /platform:x64;/define:TRACE
      Id flags:  
      
      The output was:
      0
      MSBuild version 17.11.9+a69bbaaf5 for .NET Framework
      Build started 3/17/2025 9:47:33 AM.
      
      Project "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-csharp\\tests\\unit\\build\\CMakeFiles\\4.0.0-rc4\\CompilerIdCSharp\\CompilerIdCSharp.csproj" on node 1 (default targets).
      PrepareForBuild:
        Creating directory "obj\\x64\\Debug\\".
      CoreCompile:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Current\\Bin\\Roslyn\\csc.exe /noconfig /nowarn:1701,1702 /fullpaths /nostdlib+ /platform:anycpu32bitpreferred /define:Platformx64;PlatformToolsetv143 /highentropyva+ /reference:"C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\mscorlib.dll" /reference:"C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Core.dll" /debug+ /out:obj\\x64\\Debug\\CompilerIdCSharp.exe /subsystemversion:6.00 /target:exe /utf8output /langversion:7.3 CMakeCSharpCompilerId.cs "obj\\x64\\Debug\\.NETFramework,Version=v4.7.2.AssemblyAttributes.cs"
        CompilerServer: server - server processed compilation - CompilerIdCSharp
      CopyFilesToOutputDirectory:
        Copying file from "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-csharp\\tests\\unit\\build\\CMakeFiles\\4.0.0-rc4\\CompilerIdCSharp\\obj\\x64\\Debug\\CompilerIdCSharp.exe" to "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-csharp\\tests\\unit\\build\\CMakeFiles\\4.0.0-rc4\\CompilerIdCSharp\\CompilerIdCSharp.exe".
        CompilerIdCSharp -> D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-csharp\\tests\\unit\\build\\CMakeFiles\\4.0.0-rc4\\CompilerIdCSharp\\CompilerIdCSharp.exe
        Copying file from "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-csharp\\tests\\unit\\build\\CMakeFiles\\4.0.0-rc4\\CompilerIdCSharp\\obj\\x64\\Debug\\CompilerIdCSharp.pdb" to "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-csharp\\tests\\unit\\build\\CMakeFiles\\4.0.0-rc4\\CompilerIdCSharp\\CompilerIdCSharp.pdb".
      PostBuildEvent:
        if not "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Current\\Bin\\Roslyn"=="" if exist "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Current\\Bin\\Roslyn\\csc.exe" set _CSC=C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Current\\Bin\\Roslyn
        if exist "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Current\\Bin\\amd64\\csc.exe" set _CSC=C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Current\\Bin\\amd64
        if "%_CSC%"=="" exit -1
        @echo CMAKE_CSharp_COMPILER=%_CSC%\\csc.exe
        CMAKE_CSharp_COMPILER=C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Current\\Bin\\Roslyn\\csc.exe
      Done Building Project "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-csharp\\tests\\unit\\build\\CMakeFiles\\4.0.0-rc4\\CompilerIdCSharp\\CompilerIdCSharp.csproj" (default targets).
      
      Build succeeded.
          0 Warning(s)
          0 Error(s)
      
      Time Elapsed 00:00:00.36
      
      
      Compilation of the CSharp compiler identification source "CMakeCSharpCompilerId.cs" produced "CompilerIdCSharp.csproj"
      
      Compilation of the CSharp compiler identification source "CMakeCSharpCompilerId.cs" produced "CompilerIdCSharp.exe"
      
      Compilation of the CSharp compiler identification source "CMakeCSharpCompilerId.cs" produced "CompilerIdCSharp.pdb"
      
      The CSharp compiler identification could not be found in:
        D:/kibisoft/workarea/git/interop/libs/translate/translate-csharp/tests/unit/build/CMakeFiles/4.0.0-rc4/CompilerIdCSharp/CompilerIdCSharp.csproj
      
      The CSharp compiler identification is Microsoft Visual Studio, found in:
        D:/kibisoft/workarea/git/interop/libs/translate/translate-csharp/tests/unit/build/CMakeFiles/4.0.0-rc4/CompilerIdCSharp/CompilerIdCSharp.exe
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeTestCSharpCompiler.cmake:34 (try_compile)"
      - "CMakeLists.txt:3 (project)"
    checks:
      - "Check for working C# compiler: C:/Program Files/Microsoft Visual Studio/2022/Professional/MSBuild/Current/Bin/Roslyn/csc.exe"
    directories:
      source: "D:/kibisoft/workarea/git/interop/libs/translate/translate-csharp/tests/unit/build/CMakeFiles/CMakeScratch/TryCompile-xhvpbj"
      binary: "D:/kibisoft/workarea/git/interop/libs/translate/translate-csharp/tests/unit/build/CMakeFiles/CMakeScratch/TryCompile-xhvpbj"
    cmakeVariables:
      CMAKE_CSharp_FLAGS: "/platform:x64 /define:TRACE"
      CMAKE_CSharp_FLAGS_DEBUG: "/debug:full /optimize- /warn:3 /errorreport:prompt /define:DEBUG"
      CMAKE_EXE_LINKER_FLAGS: ""
    buildResult:
      variable: "CMAKE_CSharp_COMPILER_WORKS"
      cached: true
      stdout: |
        Change Dir: 'D:/kibisoft/workarea/git/interop/libs/translate/translate-csharp/tests/unit/build/CMakeFiles/CMakeScratch/TryCompile-xhvpbj'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_898c9.csproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.11.9+a69bbaaf5 for .NET Framework
        Build started 3/17/2025 9:47:33 AM.
        
        Project "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-csharp\\tests\\unit\\build\\CMakeFiles\\CMakeScratch\\TryCompile-xhvpbj\\cmTC_898c9.csproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-csharp\\tests\\unit\\build\\CMakeFiles\\CMakeScratch\\TryCompile-xhvpbj\\Debug\\".
          Creating directory "obj\\x64\\Debug\\".
        CoreCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Current\\Bin\\Roslyn\\csc.exe /noconfig /nowarn:1701,1702 /fullpaths /nostdlib+ /platform:x64 /errorreport:prompt /warn:3 /define:TRACE;DEBUG /highentropyva+ /reference:"C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\mscorlib.dll" /reference:"C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Core.dll" /debug+ /debug:full /optimize- /out:obj\\x64\\Debug\\cmTC_898c9.exe /subsystemversion:6.00 /target:exe /utf8output /langversion:7.3 D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-csharp\\tests\\unit\\build\\CMakeFiles\\CMakeScratch\\TryCompile-xhvpbj\\testCSharpCompiler.cs "obj\\x64\\Debug\\.NETFramework,Version=v4.7.2.AssemblyAttributes.cs"
          CompilerServer: server - server processed compilation - cmTC_898c9
        CopyFilesToOutputDirectory:
          Copying file from "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-csharp\\tests\\unit\\build\\CMakeFiles\\CMakeScratch\\TryCompile-xhvpbj\\obj\\x64\\Debug\\cmTC_898c9.exe" to "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-csharp\\tests\\unit\\build\\CMakeFiles\\CMakeScratch\\TryCompile-xhvpbj\\Debug\\cmTC_898c9.exe".
          cmTC_898c9 -> D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-csharp\\tests\\unit\\build\\CMakeFiles\\CMakeScratch\\TryCompile-xhvpbj\\Debug\\cmTC_898c9.exe
          Copying file from "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-csharp\\tests\\unit\\build\\CMakeFiles\\CMakeScratch\\TryCompile-xhvpbj\\obj\\x64\\Debug\\cmTC_898c9.pdb" to "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-csharp\\tests\\unit\\build\\CMakeFiles\\CMakeScratch\\TryCompile-xhvpbj\\Debug\\cmTC_898c9.pdb".
        Done Building Project "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-csharp\\tests\\unit\\build\\CMakeFiles\\CMakeScratch\\TryCompile-xhvpbj\\cmTC_898c9.csproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:00.18
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "D:/kibisoft/workarea/git/interop/libs/translate/translate-csharp/CMakeLists.txt:3 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
      Compiler:  
      Build flags: /DWIN32;/D_WINDOWS
      Id flags:  
      
      The output was:
      0
      MSBuild version 17.11.9+a69bbaaf5 for .NET Framework
      Build started 3/17/2025 9:47:34 AM.
      
      Project "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-csharp\\tests\\unit\\build\\CMakeFiles\\4.0.0-rc4\\CompilerIdC\\CompilerIdC.vcxproj" on node 1 (default targets).
      PrepareForBuild:
        Creating directory "Debug\\".
        Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
        Creating directory "Debug\\CompilerIdC.tlog\\".
      InitializeBuildStatus:
        Creating "Debug\\CompilerIdC.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
        Touching "Debug\\CompilerIdC.tlog\\unsuccessfulbuild".
      ClCompile:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Tools\\MSVC\\14.41.34120\\bin\\HostX64\\x64\\CL.exe /c /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc143.pdb" /external:W0 /Gd /TC /FC /errorReport:queue CMakeCCompilerId.c
        CMakeCCompilerId.c
      Link:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Tools\\MSVC\\14.41.34120\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdC.exe" /INCREMENTAL:NO /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdC.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdC.lib" /MACHINE:X64 Debug\\CMakeCCompilerId.obj
        CompilerIdC.vcxproj -> D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-csharp\\tests\\unit\\build\\CMakeFiles\\4.0.0-rc4\\CompilerIdC\\CompilerIdC.exe
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_C_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_C_COMPILER=C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Tools\\MSVC\\14.41.34120\\bin\\Hostx64\\x64\\cl.exe
      FinalizeBuildStatus:
        Deleting file "Debug\\CompilerIdC.tlog\\unsuccessfulbuild".
        Touching "Debug\\CompilerIdC.tlog\\CompilerIdC.lastbuildstate".
      Done Building Project "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-csharp\\tests\\unit\\build\\CMakeFiles\\4.0.0-rc4\\CompilerIdC\\CompilerIdC.vcxproj" (default targets).
      
      Build succeeded.
          0 Warning(s)
          0 Error(s)
      
      Time Elapsed 00:00:00.63
      
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CompilerIdC.exe"
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CompilerIdC.vcxproj"
      
      The C compiler identification is MSVC, found in:
        D:/kibisoft/workarea/git/interop/libs/translate/translate-csharp/tests/unit/build/CMakeFiles/4.0.0-rc4/CompilerIdC/CompilerIdC.exe
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "D:/kibisoft/workarea/git/interop/libs/translate/translate-csharp/CMakeLists.txt:3 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler:  
      Build flags: /DWIN32;/D_WINDOWS;/EHsc
      Id flags:  
      
      The output was:
      0
      MSBuild version 17.11.9+a69bbaaf5 for .NET Framework
      Build started 3/17/2025 9:47:35 AM.
      
      Project "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-csharp\\tests\\unit\\build\\CMakeFiles\\4.0.0-rc4\\CompilerIdCXX\\CompilerIdCXX.vcxproj" on node 1 (default targets).
      PrepareForBuild:
        Creating directory "Debug\\".
        Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
        Creating directory "Debug\\CompilerIdCXX.tlog\\".
      InitializeBuildStatus:
        Creating "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
        Touching "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild".
      ClCompile:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Tools\\MSVC\\14.41.34120\\bin\\HostX64\\x64\\CL.exe /c /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc143.pdb" /external:W0 /Gd /TP /FC /errorReport:queue CMakeCXXCompilerId.cpp
        CMakeCXXCompilerId.cpp
      Link:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Tools\\MSVC\\14.41.34120\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdCXX.exe" /INCREMENTAL:NO /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdCXX.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdCXX.lib" /MACHINE:X64 Debug\\CMakeCXXCompilerId.obj
        CompilerIdCXX.vcxproj -> D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-csharp\\tests\\unit\\build\\CMakeFiles\\4.0.0-rc4\\CompilerIdCXX\\CompilerIdCXX.exe
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_CXX_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_CXX_COMPILER=C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Tools\\MSVC\\14.41.34120\\bin\\Hostx64\\x64\\cl.exe
      FinalizeBuildStatus:
        Deleting file "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild".
        Touching "Debug\\CompilerIdCXX.tlog\\CompilerIdCXX.lastbuildstate".
      Done Building Project "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-csharp\\tests\\unit\\build\\CMakeFiles\\4.0.0-rc4\\CompilerIdCXX\\CompilerIdCXX.vcxproj" (default targets).
      
      Build succeeded.
          0 Warning(s)
          0 Error(s)
      
      Time Elapsed 00:00:00.40
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.exe"
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.vcxproj"
      
      The CXX compiler identification is MSVC, found in:
        D:/kibisoft/workarea/git/interop/libs/translate/translate-csharp/tests/unit/build/CMakeFiles/4.0.0-rc4/CompilerIdCXX/CompilerIdCXX.exe
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:83 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "D:/kibisoft/workarea/git/interop/libs/translate/translate-csharp/CMakeLists.txt:3 (project)"
    checks:
      - "Detecting C compiler ABI info"
    directories:
      source: "D:/kibisoft/workarea/git/interop/libs/translate/translate-csharp/tests/unit/build/CMakeFiles/CMakeScratch/TryCompile-ljaf30"
      binary: "D:/kibisoft/workarea/git/interop/libs/translate/translate-csharp/tests/unit/build/CMakeFiles/CMakeScratch/TryCompile-ljaf30"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_POSITION_INDEPENDENT_CODE: "ON"
    buildResult:
      variable: "CMAKE_C_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'D:/kibisoft/workarea/git/interop/libs/translate/translate-csharp/tests/unit/build/CMakeFiles/CMakeScratch/TryCompile-ljaf30'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_c2278.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.11.9+a69bbaaf5 for .NET Framework
        Build started 3/17/2025 9:47:35 AM.
        
        Project "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-csharp\\tests\\unit\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ljaf30\\cmTC_c2278.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_c2278.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-csharp\\tests\\unit\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ljaf30\\Debug\\".
          Creating directory "cmTC_c2278.dir\\Debug\\cmTC_c2278.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_c2278.dir\\Debug\\cmTC_c2278.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_c2278.dir\\Debug\\cmTC_c2278.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Tools\\MSVC\\14.41.34120\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_c2278.dir\\Debug\\\\" /Fd"cmTC_c2278.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-4.0\\Modules\\CMakeCCompilerABI.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.41.34120 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_c2278.dir\\Debug\\\\" /Fd"cmTC_c2278.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-4.0\\Modules\\CMakeCCompilerABI.c"
          CMakeCCompilerABI.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Tools\\MSVC\\14.41.34120\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-csharp\\tests\\unit\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ljaf30\\Debug\\cmTC_c2278.exe" /INCREMENTAL /ILK:"cmTC_c2278.dir\\Debug\\cmTC_c2278.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"D:/kibisoft/workarea/git/interop/libs/translate/translate-csharp/tests/unit/build/CMakeFiles/CMakeScratch/TryCompile-ljaf30/Debug/cmTC_c2278.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"D:/kibisoft/workarea/git/interop/libs/translate/translate-csharp/tests/unit/build/CMakeFiles/CMakeScratch/TryCompile-ljaf30/Debug/cmTC_c2278.lib" /MACHINE:X64 cmTC_c2278.dir\\Debug\\CMakeCCompilerABI.obj
          cmTC_c2278.vcxproj -> D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-csharp\\tests\\unit\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ljaf30\\Debug\\cmTC_c2278.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_c2278.dir\\Debug\\cmTC_c2278.tlog\\unsuccessfulbuild".
          Touching "cmTC_c2278.dir\\Debug\\cmTC_c2278.tlog\\cmTC_c2278.lastbuildstate".
        Done Building Project "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-csharp\\tests\\unit\\build\\CMakeFiles\\CMakeScratch\\TryCompile-ljaf30\\cmTC_c2278.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:00.37
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:227 (message)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "D:/kibisoft/workarea/git/interop/libs/translate/translate-csharp/CMakeLists.txt:3 (project)"
    message: |
      Parsed C implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|link\\.exe|lld-link(\\.exe)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|link\\.exe|lld-link(\\.exe)?))("|,| |$)]
        linker tool for 'C': C:/Program Files/Microsoft Visual Studio/2022/Professional/VC/Tools/MSVC/14.41.34120/bin/HostX64/x64/link.exe
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/Internal/CMakeDetermineLinkerId.cmake:36 (message)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:264 (cmake_determine_linker_id)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "D:/kibisoft/workarea/git/interop/libs/translate/translate-csharp/CMakeLists.txt:3 (project)"
    message: |
      Running the C compiler's linker: "C:/Program Files/Microsoft Visual Studio/2022/Professional/VC/Tools/MSVC/14.41.34120/bin/HostX64/x64/link.exe" "-v"
      Microsoft (R) Incremental Linker Version 14.41.34120.0
      Copyright (C) Microsoft Corporation.  All rights reserved.
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:83 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "D:/kibisoft/workarea/git/interop/libs/translate/translate-csharp/CMakeLists.txt:3 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "D:/kibisoft/workarea/git/interop/libs/translate/translate-csharp/tests/unit/build/CMakeFiles/CMakeScratch/TryCompile-kjwgiz"
      binary: "D:/kibisoft/workarea/git/interop/libs/translate/translate-csharp/tests/unit/build/CMakeFiles/CMakeScratch/TryCompile-kjwgiz"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_CXX_SCAN_FOR_MODULES: "OFF"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_POSITION_INDEPENDENT_CODE: "ON"
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'D:/kibisoft/workarea/git/interop/libs/translate/translate-csharp/tests/unit/build/CMakeFiles/CMakeScratch/TryCompile-kjwgiz'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_c152e.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.11.9+a69bbaaf5 for .NET Framework
        Build started 3/17/2025 9:47:36 AM.
        
        Project "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-csharp\\tests\\unit\\build\\CMakeFiles\\CMakeScratch\\TryCompile-kjwgiz\\cmTC_c152e.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_c152e.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-csharp\\tests\\unit\\build\\CMakeFiles\\CMakeScratch\\TryCompile-kjwgiz\\Debug\\".
          Creating directory "cmTC_c152e.dir\\Debug\\cmTC_c152e.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_c152e.dir\\Debug\\cmTC_c152e.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_c152e.dir\\Debug\\cmTC_c152e.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Tools\\MSVC\\14.41.34120\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /std:c++17 /Fo"cmTC_c152e.dir\\Debug\\\\" /Fd"cmTC_c152e.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-4.0\\Modules\\CMakeCXXCompilerABI.cpp"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.41.34120 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /std:c++17 /Fo"cmTC_c152e.dir\\Debug\\\\" /Fd"cmTC_c152e.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-4.0\\Modules\\CMakeCXXCompilerABI.cpp"
          CMakeCXXCompilerABI.cpp
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Tools\\MSVC\\14.41.34120\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-csharp\\tests\\unit\\build\\CMakeFiles\\CMakeScratch\\TryCompile-kjwgiz\\Debug\\cmTC_c152e.exe" /INCREMENTAL /ILK:"cmTC_c152e.dir\\Debug\\cmTC_c152e.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"D:/kibisoft/workarea/git/interop/libs/translate/translate-csharp/tests/unit/build/CMakeFiles/CMakeScratch/TryCompile-kjwgiz/Debug/cmTC_c152e.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"D:/kibisoft/workarea/git/interop/libs/translate/translate-csharp/tests/unit/build/CMakeFiles/CMakeScratch/TryCompile-kjwgiz/Debug/cmTC_c152e.lib" /MACHINE:X64 cmTC_c152e.dir\\Debug\\CMakeCXXCompilerABI.obj
          cmTC_c152e.vcxproj -> D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-csharp\\tests\\unit\\build\\CMakeFiles\\CMakeScratch\\TryCompile-kjwgiz\\Debug\\cmTC_c152e.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_c152e.dir\\Debug\\cmTC_c152e.tlog\\unsuccessfulbuild".
          Touching "cmTC_c152e.dir\\Debug\\cmTC_c152e.tlog\\cmTC_c152e.lastbuildstate".
        Done Building Project "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-csharp\\tests\\unit\\build\\CMakeFiles\\CMakeScratch\\TryCompile-kjwgiz\\cmTC_c152e.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:00.36
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:227 (message)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "D:/kibisoft/workarea/git/interop/libs/translate/translate-csharp/CMakeLists.txt:3 (project)"
    message: |
      Parsed CXX implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|link\\.exe|lld-link(\\.exe)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|link\\.exe|lld-link(\\.exe)?))("|,| |$)]
        linker tool for 'CXX': C:/Program Files/Microsoft Visual Studio/2022/Professional/VC/Tools/MSVC/14.41.34120/bin/HostX64/x64/link.exe
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/Internal/CMakeDetermineLinkerId.cmake:36 (message)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:264 (cmake_determine_linker_id)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "D:/kibisoft/workarea/git/interop/libs/translate/translate-csharp/CMakeLists.txt:3 (project)"
    message: |
      Running the CXX compiler's linker: "C:/Program Files/Microsoft Visual Studio/2022/Professional/VC/Tools/MSVC/14.41.34120/bin/HostX64/x64/link.exe" "-v"
      Microsoft (R) Incremental Linker Version 14.41.34120.0
      Copyright (C) Microsoft Corporation.  All rights reserved.
...
