/*
 * Copyright 2021-present Alcon. All rights reserved.
 * Alcon proprietary/confidential.
 * Use is subject to license terms.
 */

using System;
using System.IO;
using System.Text.RegularExpressions;
using System.Security.Cryptography;
using System.Text;

namespace LocalizationLib;

public static class LocalizationUtils
{
    /// <summary>
    /// Computes a hash for a given text (used for unique string IDs).
    /// </summary>
    public static int HashString(string text)
    {
        if (string.IsNullOrEmpty(text))
        {
            return 0;
        }
        unchecked
        {
            int hash = 23;
            foreach (char c in text)
            {
                hash = hash * 31 + c;
            }
            return hash;
        }
    }

    /// <summary>
    /// Generates a consistent UUID based on input string.
    /// This is deterministic and will always produce the same UUID for the same input.
    /// </summary>
    /// <param name="input">The string to generate a UUID from</param>
    /// <returns>A deterministic UUID derived from the input</returns>
    public static Guid GenerateConsistentGuid(string input)
    {
        if (string.IsNullOrEmpty(input))
            return Guid.Empty;

        // Use MD5 to generate a deterministic byte array from the input string
        // MD5 is used here for deterministic hash generation, not for security purposes
        using (var md5 = MD5.Create())
        {
            byte[] hash = md5.ComputeHash(Encoding.UTF8.GetBytes(input));
            
            // Set the version bits for a version 3 UUID (name-based UUID using MD5)
            hash[6] = (byte)((hash[6] & 0x0F) | 0x30);
            hash[8] = (byte)((hash[8] & 0x3F) | 0x80);
            
            return new Guid(hash);
        }
    }

    /// <summary>
    /// Generates a consistent, collision-resistant ID for a UI text element.
    /// </summary>
    /// <param name="text">The text content</param>
    /// <param name="controlType">The type of control containing the text</param>
    /// <param name="elementName">The name of the element</param>
    /// <param name="baseUri">The base URI of the element</param>
    /// <param name="additionalContext">Optional additional context information</param>
    /// <returns>A string ID that is collision-resistant and consistent</returns>
    public static string GenerateUITextId(string text, string controlType, string elementName, Uri baseUri, string additionalContext = null)
    {
        var builder = new StringBuilder();
        
        // Add all relevant context to create a unique fingerprint
        builder.Append(text ?? "");
        builder.Append("||");
        builder.Append(controlType ?? "");
        builder.Append("||");
        builder.Append(elementName ?? "");
        builder.Append("||");
        builder.Append(baseUri?.ToString() ?? "");
        
        if (!string.IsNullOrEmpty(additionalContext))
        {
            builder.Append("||");
            builder.Append(additionalContext);
        }
        
        // Generate a consistent GUID from the combined string
        var guid = GenerateConsistentGuid(builder.ToString());
        
        // Return GUID as a string (without hyphens to make it more compact)
        return guid.ToString("N");
    }
    
    /// <summary>
    /// Generates a consistent, collision-resistant ID for a source text element.
    /// </summary>
    /// <param name="text">The text content</param>
    /// <param name="filePath">The file path where the text is located</param>
    /// <param name="lineNumber">The line number in the file</param>
    /// <param name="columnNumber">The column number in the line</param>
    /// <param name="groupId">The group ID associated with the text</param>
    /// <returns>A string ID that is collision-resistant and consistent</returns>
    public static string GenerateSourceTextId(string text, string filePath, int lineNumber, int columnNumber, string groupId)
    {
        var builder = new StringBuilder();
        
        // Add all relevant context to create a unique fingerprint
        builder.Append(text ?? "");
        builder.Append("||");
        builder.Append(filePath ?? "");
        builder.Append("||");
        builder.Append(lineNumber);
        builder.Append("||");
        builder.Append(columnNumber);
        builder.Append("||");
        builder.Append(groupId ?? "");
        
        // Generate a consistent GUID from the combined string
        var guid = GenerateConsistentGuid(builder.ToString());
        
        // Return GUID as a string (without hyphens to make it more compact)
        return guid.ToString("N");
    }

    /// <summary>
    /// Extracts the file path with the help of groupId
    /// </summary>
    /// <param name="projectPath"></param>
    /// <param name="groupId"></param>
    /// <param name="fileExtension"></param>
    /// <returns></returns>
    public static string ExtractFilePath(string projectPath, string groupId, string fileExtension)
    {
        // Search all possible matching files in the project directory
        var possibleFiles = Directory.GetFiles(projectPath, $"*{fileExtension}", SearchOption.AllDirectories);

        // get namespace and className from groupId
        int lastDotIndex = groupId.LastIndexOf('.');
        string className = lastDotIndex != -1 ? groupId.Substring(lastDotIndex + 1) : string.Empty;
        string namespaceOnly = lastDotIndex != -1 ? groupId.Substring(0, lastDotIndex) : groupId;

        foreach (var file in possibleFiles)
        {
            bool containsNamespace = false;
            bool containsClassName = false;
            foreach (var line in File.ReadLines(file))
            {
                if (line.Contains(namespaceOnly))
                {
                    containsNamespace = true;
                }
                if (line.Contains(className))
                {
                    containsClassName = true;
                }
                if (containsNamespace && containsClassName)
                {
                    return file;
                }
            }
        }
        return string.Empty;
    }

    /// <summary>
    /// Extract the line number for the text inside the filePath
    /// </summary>
    /// <param name="filePath"></param>
    /// <param name="text"></param>
    /// <returns></returns>
    public static int ExtractSourceLine(string filePath, string text)
    {
        if (!PathExists(filePath))
        {
            return 0;
        }

        string[] lines = File.ReadAllLines(filePath);

        for (int i = 0; i < lines.Length; i++)
        {
            if (lines[i].Contains($"\"{text}\""))
            {
                return i + 1;
            }
        }
        return 0;
    }

    public static bool PathExists(string path)
    {
        // Return true if it�s either a valid file or a valid directory
        return File.Exists(path) || Directory.Exists(path);
    }

    /// <summary>
    /// Converts an absolute file path to a relative path based on the project directory.
    /// Example:
    ///     Absolute: "C:\Users\<USER>\Projects\MyApp\Views\MainWindow.xaml"
    ///     Relative: "Views\MainWindow.xaml"
    /// </summary>
    public static string GetRelativePath(string fullPath, string projectDirectory)
    {
        try
        {
            if (fullPath == null)
            {
                return null;
            }

            Uri fullPathUri = new Uri(fullPath);
            Uri baseUri = new Uri(projectDirectory.TrimEnd('\\') + "\\");
            return Uri.UnescapeDataString(baseUri.MakeRelativeUri(fullPathUri).ToString()).Replace('/', '\\');
        }
        catch
        {
            return fullPath; // fallback to absolute if conversion fails
        }
    }

    public static string GetGroupIdFromCs(string csPath)
    {
        // Read the entire content of the C# file.
        string csContent = File.ReadAllText(csPath);

        // Extract the namespace using a regex (e.g., "namespace TRICS.Planner.UI.Settings.App")
        var namespaceMatch = Regex.Match(csContent, @"namespace\s+([\w\.]+)");

        // Get the file name.
        string fileName = Path.GetFileName(csPath);
        string fileNameWithoutExt;

        // If the file name ends with ".xaml.cs", remove that suffix properly.
        if (fileName.EndsWith(".xaml.cs", StringComparison.OrdinalIgnoreCase))
        {
            fileNameWithoutExt = fileName.Substring(0, fileName.Length - ".xaml.cs".Length);
        }
        else
        {
            // For standard .cs files.
            fileNameWithoutExt = Path.GetFileNameWithoutExtension(csPath);
        }

        // Try to locate a class/struct/interface declaration that matches the file name.
        string classPattern = $@"\b(public|internal|protected|private)?\s*(static\s+)?(partial\s+)?(class|struct|interface)\s+{Regex.Escape(fileNameWithoutExt)}\b";
        var classMatch = Regex.Match(csContent, classPattern);

        if (namespaceMatch.Success && classMatch.Success)
        {
            // If both namespace and matching type are found, use them for the group id.
            string namespaceName = namespaceMatch.Groups[1].Value;
            return $"{namespaceName}.{fileNameWithoutExt}";
        }
        else
        {
            // Fallback: use the parent folder and the processed file name.
            string parentFolder = Path.GetFileName(Path.GetDirectoryName(csPath));
            return $"{parentFolder}.{fileNameWithoutExt}";
        }
    }

    public static string GetGroupIdFromXaml(string xamlPath)
    {
        // Read the entire content of the XAML file
        string xamlContent = File.ReadAllText(xamlPath);

        // Look for the x:Class attribute (e.g., x:Class="TRICS.Planner.UI.Settings.SurgeonProfile.UserAppSettingsControl")
        var match = Regex.Match(xamlContent, @"x:Class\s*=\s*""([^""]+)""");

        if (match.Success)
        {
            // If found, return the value as the unique group identifier
            return match.Groups[1].Value;
        }
        else
        {
            // Fallback: use the parent folder and file name (without extension)
            string fileNameWithoutExt = Path.GetFileNameWithoutExtension(xamlPath);
            string parentFolder = Path.GetFileName(Path.GetDirectoryName(xamlPath));
            return $"{parentFolder}.{fileNameWithoutExt}";
        }
    }

    public static string GetGroupFromPath(string filePath)
    {
        if (filePath.EndsWith(".xaml", StringComparison.OrdinalIgnoreCase))
        {
            return GetGroupIdFromXaml(filePath);
        }
        else if (filePath.EndsWith(".cs", StringComparison.OrdinalIgnoreCase))
        {
            return GetGroupIdFromCs(filePath);
        }
        else
        {
            // Fallback: use the parent folder and file name
            string fileName = Path.GetFileName(filePath);
            string parentFolder = Path.GetFileName(Path.GetDirectoryName(filePath));
            return $"{parentFolder}.{fileName}";
        }
    }

    /// <summary>
    /// Generates a unique ID for a LocalizedString in XML export.
    /// This ensures consistent IDs for the same text with the same properties.
    /// </summary>
    /// <param name="text">The text content</param>
    /// <param name="fontClass">Optional font class name</param>
    /// <param name="width">Optional width of the control</param>
    /// <param name="height">Optional height of the control</param>
    /// <param name="additionalContext">Optional additional context to ensure uniqueness</param>
    /// <returns>A string ID that can be used in XML export</returns>
    public static string GenerateXmlExportId(string text, string fontClass = null, int width = 0, int height = 0, string additionalContext = null)
    {
        // For empty or null input, return "0" as the ID
        if (string.IsNullOrEmpty(text))
        {
            return "0";
        }
        
        var builder = new StringBuilder();
        
        // Add key components to create a unique identifier
        builder.Append("en:");
        builder.Append(text);
        
        if (!string.IsNullOrEmpty(fontClass))
        {
            builder.Append("|f:");
            builder.Append(fontClass);
        }
        
        if (width > 0)
        {
            builder.Append("|w:");
            builder.Append(width);
        }
        
        if (height > 0)
        {
            builder.Append("|h:");
            builder.Append(height);
        }
        
        if (!string.IsNullOrEmpty(additionalContext))
        {
            builder.Append("|ctx:");
            builder.Append(additionalContext);
        }
        
        // Use the existing HashString method to generate a unique int
        int hash = HashString(builder.ToString());
        
        // Return the hash as a string
        return hash.ToString();
    }

    internal static string GetUITextKey(string controlType, string elementName, Uri baseUri, string text)
    {
        return $"{controlType}:{elementName}:{baseUri}:{HashString(text)}";
    }
}
