/*
 * Copyright 2021-present Alcon. All rights reserved.
 * Alcon proprietary/confidential.
 * Use is subject to license terms.
 */

using NUnit.Framework;
using System;
using System.IO;
using System.Linq;

namespace LocalizationLib.Tests;

[TestFixture]
public class XamlProcessorTests
{
    private string _tempFolder;

    // Sample XAML content with hard-coded texts using double quotes.
    private readonly string _sampleXaml =
@"<UserControl xmlns=""http://schemas.microsoft.com/winfx/2006/xaml/presentation""
    xmlns:x=""http://schemas.microsoft.com/winfx/2006/xaml""
    Title=""Sample"">
    <Grid>
        <Button Content=""Cancel"" />
        <TextBlock Text=""Hello World"" />
        <Label ToolTip=""Info"" />
    </Grid>
</UserControl>";

    [SetUp]
    public void SetUp()
    {
        // Create a unique temporary folder for the tests.
        _tempFolder = Path.Combine(Path.GetTempPath(), "XamlProcessorTests_" + Guid.NewGuid());
        Directory.CreateDirectory(_tempFolder);
    }

    [TearDown]
    public void TearDown()
    {
        // Clean up the temporary folder after tests.
        if (Directory.Exists(_tempFolder))
        {
            Directory.Delete(_tempFolder, true);
        }
    }

    /// <summary>
    /// Tests that processing a XAML file with no eligible hard-coded text (empty or already bound)
    /// does not record any replacements and does not mark the file as changed.
    /// </summary>
    [Test]
    public void ProcessXamlFile_NoEligibleReplacements_NoChange()
    {
        // Arrange: Create a XAML file with empty attributes and already bound values.
        string xamlNoReplacements =
@"<UserControl xmlns=""http://schemas.microsoft.com/winfx/2006/xaml/presentation""
    xmlns:x=""http://schemas.microsoft.com/winfx/2006/xaml""
    Title="""">
    <Grid>
        <Button Content=""{Binding SomeConverter}"" />
        <TextBlock Text="""" />
    </Grid>
</UserControl>";
        string tempFile = Path.Combine(_tempFolder, "TestNoRepl.xaml");
        File.WriteAllText(tempFile, xamlNoReplacements);

        var processor = new XamlProcessor();
        string testGroupId = "TestGroup.NoRepl";

        // Act: Process the file.
        FileProcessingResult result = processor.ProcessXamlFile(tempFile, testGroupId);

        // Assert: Ensure the file is not marked as changed and no replacements are recorded.
        Assert.IsFalse(result.Changed, "File should not be marked as changed.");
        Assert.IsEmpty(result.Replacements, "No replacements should be recorded.");

        // Verify file content remains unchanged.
        string postContent = File.ReadAllText(tempFile);
        Assert.AreEqual(xamlNoReplacements, postContent, "File content should remain unchanged.");
    }

    /// <summary>
    /// Tests that processing a XAML file with no hard-coded texts produces no changes.
    /// </summary>
    [Test]
    public void ProcessXamlFile_NoHardCodedText_NoChange()
    {
        // Arrange: Create a XAML file with no eligible hard-coded texts.
        string xamlNoText =
@"<UserControl xmlns=""http://schemas.microsoft.com/winfx/2006/xaml/presentation""
    xmlns:x=""http://schemas.microsoft.com/winfx/2006/xaml""
    Title="""">
    <Grid>
        <Button Content="""" />
    </Grid>
</UserControl>";
        string tempFile = Path.Combine(_tempFolder, "Test3.xaml");
        File.WriteAllText(tempFile, xamlNoText);

        var processor = new XamlProcessor();

        // Act: Process the file.
        FileProcessingResult result = processor.ProcessXamlFile(tempFile);

        // Assert: File should remain unchanged.
        Assert.IsFalse(result.Changed, "File should not be marked as changed.");
        Assert.IsEmpty(result.Replacements, "No replacements should be recorded.");
    }

    [Test]
    public void ProcessXamlFile_DependencyPropertyDetection()
    {
        // Arrange: Create a XAML file with both dependency and non-dependency properties
        string xamlWithMixedProperties =
@"<UserControl xmlns=""http://schemas.microsoft.com/winfx/2006/xaml/presentation""
    xmlns:x=""http://schemas.microsoft.com/winfx/2006/xaml"">
    <Grid>
        <Button Content=""Click Me"" Name=""btnClick"" Tag=""button1"" />
        <TextBlock Text=""Hello World"" DataContext=""{Binding}"" />
        <ComboBox ItemsSource=""{Binding Items}"" SelectedItem=""{Binding Selected}"" />
    </Grid>
</UserControl>";
        string tempFile = Path.Combine(_tempFolder, "TestDependencyProps.xaml");
        File.WriteAllText(tempFile, xamlWithMixedProperties);

        var processor = new XamlProcessor();

        // Act: Process the file
        FileProcessingResult result = processor.ProcessXamlFile(tempFile);

        // Assert: Check that only supported properties are processed
        Assert.AreEqual(2, result.Replacements.Count, "Should find 2 supported text properties");

        // Content property should be a dependency property
        var contentRecord = result.Replacements.First(r => r.Property == "Content");
        Assert.IsTrue(contentRecord.IsDependencyProperty, "Content should be a dependency property");
        Assert.AreEqual("Click Me", contentRecord.OriginalValue);

        // Text property should be a dependency property
        var textRecord = result.Replacements.First(r => r.Property == "Text");
        Assert.IsTrue(textRecord.IsDependencyProperty, "Text should be a dependency property");
        Assert.AreEqual("Hello World", textRecord.OriginalValue);
    }

    [Test]
    public void ProcessXamlFile_NonDependencyPropertyHandling()
    {
        // Arrange: Create a XAML file with non-dependency properties
        string xamlWithNonDependencyProps =
@"<UserControl xmlns=""http://schemas.microsoft.com/winfx/2006/xaml/presentation""
    xmlns:x=""http://schemas.microsoft.com/winfx/2006/xaml"">
    <Grid>
        <Button Tag=""button1"" Name=""btn1"" />
        <ComboBox ItemsSource=""{Binding Items}"" SelectedValue=""{Binding Value}"" />
    </Grid>
</UserControl>";
        string tempFile = Path.Combine(_tempFolder, "TestNonDependencyProps.xaml");
        File.WriteAllText(tempFile, xamlWithNonDependencyProps);

        var processor = new XamlProcessor();

        // Act: Process the file
        FileProcessingResult result = processor.ProcessXamlFile(tempFile);

        // Assert: Check that no properties are processed since none are supported
        Assert.AreEqual(0, result.Replacements.Count, "Should not process any properties since none are supported");
    }

    [Test]
    public void ProcessXamlFile_AllSupportedProperties()
    {
        // Arrange: Create a XAML file with all supported properties
        string xamlWithAllSupportedProps =
@"<UserControl xmlns=""http://schemas.microsoft.com/winfx/2006/xaml/presentation""
    xmlns:x=""http://schemas.microsoft.com/winfx/2006/xaml""
    xmlns:UiWidgetMeasurement=""clr-namespace:Alcon.UiWidgetMeasurement;assembly=Alcon.UiWidgetMeasurement""
    Title=""Main Window"">
    <Grid>
        <Button Content=""Click Me"" />
        <TextBlock Text=""Hello World"" />
        <GroupBox Header=""Group 1"" />
        <ToolTip Content=""Help Text"" />
    </Grid>
</UserControl>";
        string tempFile = Path.Combine(_tempFolder, "TestAllSupportedProps.xaml");
        File.WriteAllText(tempFile, xamlWithAllSupportedProps);

        var processor = new XamlProcessor();

        // Act: Process the file
        FileProcessingResult result = processor.ProcessXamlFile(tempFile);

        // Assert: Check that all supported properties are processed
        Assert.AreEqual(5, result.Replacements.Count, "Should find all supported text properties");

        // Verify each supported property is processed
        Assert.IsTrue(result.Replacements.Any(r => r.Property == "Title" && r.OriginalValue == "Main Window"));
        Assert.IsTrue(result.Replacements.Any(r => r.Property == "Content" && r.OriginalValue == "Click Me"));
        Assert.IsTrue(result.Replacements.Any(r => r.Property == "Text" && r.OriginalValue == "Hello World"));
        Assert.IsTrue(result.Replacements.Any(r => r.Property == "Header" && r.OriginalValue == "Group 1"));
        Assert.IsTrue(result.Replacements.Any(r => r.Property == "Content" && r.OriginalValue == "Help Text"));
    }

    [Test]
    public void ProcessXamlFile_CustomControlNonDependencyProperty()
    {
        // Arrange: Create a XAML file with a custom control that has a non-dependency property
        string xamlWithCustomControl =
@"<UserControl xmlns=""http://schemas.microsoft.com/winfx/2006/xaml/presentation""
    xmlns:x=""http://schemas.microsoft.com/winfx/2006/xaml""
    xmlns:UiWidgetMeasurement=""clr-namespace:Alcon.UiWidgetMeasurement;assembly=Alcon.UiWidgetMeasurement"">
    <Grid>
        <UiWidgetMeasurement:RefractionControl 
            x:Name=""RefractionControl""
            Title=""PreOp Phakic Refraction""
            DataContext=""{Binding PreOp.NonPhakicEyeData.PhakicPrescription}"" />
    </Grid>
</UserControl>";
        string tempFile = Path.Combine(_tempFolder, "TestCustomControl.xaml");
        File.WriteAllText(tempFile, xamlWithCustomControl);

        var processor = new XamlProcessor();

        // Act: Process the file
        FileProcessingResult result = processor.ProcessXamlFile(tempFile);

        Assert.AreEqual(1, result.Replacements.Count, "Should process Title property, but mark as non-dependency");
        var titleRecord = result.Replacements.First();
        Assert.AreEqual("Title", titleRecord.Property);
        Assert.AreEqual("PreOp Phakic Refraction", titleRecord.OriginalValue);
        Assert.IsFalse(titleRecord.IsDependencyProperty, "Title should not be a dependency property");
    }

    [Test]
    public void ProcessXamlFile_MixedCustomAndStandardControls()
    {
        // Arrange: Create a XAML file with both custom and standard controls
        string xamlWithMixedControls =
@"<UserControl xmlns=""http://schemas.microsoft.com/winfx/2006/xaml/presentation""
    xmlns:x=""http://schemas.microsoft.com/winfx/2006/xaml""
    xmlns:UiWidgetMeasurement=""clr-namespace:Alcon.UiWidgetMeasurement;assembly=Alcon.UiWidgetMeasurement"">
    <Grid>
        <UiWidgetMeasurement:RefractionControl 
            x:Name=""RefractionControl""
            Title=""PreOp Phakic Refraction""
            DataContext=""{Binding PreOp.NonPhakicEyeData.PhakicPrescription}"" />
        <Button Content=""Click Me"" />
        <TextBlock Text=""Hello World"" />
    </Grid>
</UserControl>";
        string tempFile = Path.Combine(_tempFolder, "TestMixedControls.xaml");
        File.WriteAllText(tempFile, xamlWithMixedControls);

        var processor = new XamlProcessor();

        // Act: Process the file
        FileProcessingResult result = processor.ProcessXamlFile(tempFile);

        Assert.AreEqual(3, result.Replacements.Count, "Should process all properties, including non-dependency Title");
        Assert.IsTrue(result.Replacements.Any(r => r.Property == "Content" && r.OriginalValue == "Click Me" && r.IsDependencyProperty));
        Assert.IsTrue(result.Replacements.Any(r => r.Property == "Text" && r.OriginalValue == "Hello World" && r.IsDependencyProperty));
        Assert.IsTrue(result.Replacements.Any(r => r.Property == "Title" && r.OriginalValue == "PreOp Phakic Refraction" && !r.IsDependencyProperty));
    }

    [Test]
    public void ProcessXamlFile_RefractionControlSpecificCase()
    {
        // Arrange: Create a XAML file with the specific RefractionControl case
        string xamlWithRefractionControl =
@"<UserControl xmlns=""http://schemas.microsoft.com/winfx/2006/xaml/presentation""
    xmlns:x=""http://schemas.microsoft.com/winfx/2006/xaml""
    xmlns:UiWidgetMeasurement=""clr-namespace:Alcon.UiWidgetMeasurement;assembly=Alcon.UiWidgetMeasurement"">
    <Grid>
        <UiWidgetMeasurement:RefractionControl 
            x:Name=""RefractionControl""
            Title=""PreOp Phakic Refraction""
            DataContext=""{Binding PreOp.NonPhakicEyeData.PhakicPrescription}"" />
    </Grid>
</UserControl>";
        string tempFile = Path.Combine(_tempFolder, "TestRefractionControl.xaml");
        File.WriteAllText(tempFile, xamlWithRefractionControl);

        var processor = new XamlProcessor();

        // Act: Process the file
        FileProcessingResult result = processor.ProcessXamlFile(tempFile);

        Assert.AreEqual(1, result.Replacements.Count, "Should process Title property, but mark as non-dependency");
        var titleRecord = result.Replacements.First();
        Assert.AreEqual("Title", titleRecord.Property);
        Assert.AreEqual("PreOp Phakic Refraction", titleRecord.OriginalValue);
        Assert.IsFalse(titleRecord.IsDependencyProperty, "Title should not be a dependency property");
    }

    [Test]
    public void ProcessXamlFile_RefractionControlWithMultipleProperties()
    {
        // Arrange: Create a XAML file with RefractionControl and multiple properties
        string xamlWithRefractionControl =
@"<UserControl xmlns=""http://schemas.microsoft.com/winfx/2006/xaml/presentation""
    xmlns:x=""http://schemas.microsoft.com/winfx/2006/xaml""
    xmlns:UiWidgetMeasurement=""clr-namespace:Alcon.UiWidgetMeasurement;assembly=Alcon.UiWidgetMeasurement"">
    <Grid>
        <UiWidgetMeasurement:RefractionControl 
            x:Name=""RefractionControl""
            Title=""PreOp Phakic Refraction""
            Content=""Some Content""
            DataContext=""{Binding PreOp.NonPhakicEyeData.PhakicPrescription}"" />
    </Grid>
</UserControl>";
        string tempFile = Path.Combine(_tempFolder, "TestRefractionControlMultiple.xaml");
        File.WriteAllText(tempFile, xamlWithRefractionControl);

        var processor = new XamlProcessor();

        // Act: Process the file
        FileProcessingResult result = processor.ProcessXamlFile(tempFile);

        Assert.AreEqual(2, result.Replacements.Count, "Should process both Content and Title properties");
        Assert.IsTrue(result.Replacements.Any(r => r.Property == "Content" && r.OriginalValue == "Some Content" && r.IsDependencyProperty));
        Assert.IsTrue(result.Replacements.Any(r => r.Property == "Title" && r.OriginalValue == "PreOp Phakic Refraction" && !r.IsDependencyProperty));
    }

    [Test]
    public void ProcessXamlFile_ContentProperty()
    {
        // Arrange: Create a XAML file with different controls using Content property
        string xamlWithContent =
@"<UserControl xmlns=""http://schemas.microsoft.com/winfx/2006/xaml/presentation""
    xmlns:x=""http://schemas.microsoft.com/winfx/2006/xaml"">
    <Grid>
        <Button Content=""Click Me"" />
        <ContentControl Content=""Custom Content"" />
        <Label Content=""Label Content"" />
        <ToolTip Content=""Tooltip Content"" />
    </Grid>
</UserControl>";
        string tempFile = Path.Combine(_tempFolder, "TestContentProperty.xaml");
        File.WriteAllText(tempFile, xamlWithContent);

        var processor = new XamlProcessor();

        // Act: Process the file
        FileProcessingResult result = processor.ProcessXamlFile(tempFile);

        // Assert: Check that all Content properties are processed
        Assert.AreEqual(4, result.Replacements.Count, "Should process all Content properties");
        Assert.IsTrue(result.Replacements.Any(r => r.Property == "Content" && r.OriginalValue == "Click Me"));
        Assert.IsTrue(result.Replacements.Any(r => r.Property == "Content" && r.OriginalValue == "Custom Content"));
        Assert.IsTrue(result.Replacements.Any(r => r.Property == "Content" && r.OriginalValue == "Label Content"));
        Assert.IsTrue(result.Replacements.Any(r => r.Property == "Content" && r.OriginalValue == "Tooltip Content"));
    }

    [Test]
    public void ProcessXamlFile_TextProperty()
    {
        // Arrange: Create a XAML file with different controls using Text property
        string xamlWithText =
@"<UserControl xmlns=""http://schemas.microsoft.com/winfx/2006/xaml/presentation""
    xmlns:x=""http://schemas.microsoft.com/winfx/2006/xaml"">
    <Grid>
        <TextBlock Text=""Block Text"" />
        <TextBox Text=""Box Text"" />
        <Run Text=""Run Text"" />
    </Grid>
</UserControl>";
        string tempFile = Path.Combine(_tempFolder, "TestTextProperty.xaml");
        File.WriteAllText(tempFile, xamlWithText);

        var processor = new XamlProcessor();

        // Act: Process the file
        FileProcessingResult result = processor.ProcessXamlFile(tempFile);

        // Assert: Check that all Text properties are processed
        Assert.AreEqual(3, result.Replacements.Count, "Should process all Text properties");
        Assert.IsTrue(result.Replacements.Any(r => r.Property == "Text" && r.OriginalValue == "Block Text"));
        Assert.IsTrue(result.Replacements.Any(r => r.Property == "Text" && r.OriginalValue == "Box Text"));
        Assert.IsTrue(result.Replacements.Any(r => r.Property == "Text" && r.OriginalValue == "Run Text"));
    }

    [Test]
    public void ProcessXamlFile_HeaderProperty()
    {
        // Arrange: Create a XAML file with different controls using Header property
        string xamlWithHeader =
@"<UserControl xmlns=""http://schemas.microsoft.com/winfx/2006/xaml/presentation""
    xmlns:x=""http://schemas.microsoft.com/winfx/2006/xaml"">
    <Grid>
        <GroupBox Header=""Group Header"" />
        <TabItem Header=""Tab Header"" />
        <GridViewColumn Header=""Column Header"" />
    </Grid>
</UserControl>";
        string tempFile = Path.Combine(_tempFolder, "TestHeaderProperty.xaml");
        File.WriteAllText(tempFile, xamlWithHeader);

        var processor = new XamlProcessor();

        // Act: Process the file
        FileProcessingResult result = processor.ProcessXamlFile(tempFile);

        // Assert: Check that all Header properties are processed
        Assert.AreEqual(3, result.Replacements.Count, "Should process all Header properties");
        Assert.IsTrue(result.Replacements.Any(r => r.Property == "Header" && r.OriginalValue == "Group Header"));
        Assert.IsTrue(result.Replacements.Any(r => r.Property == "Header" && r.OriginalValue == "Tab Header"));
        Assert.IsTrue(result.Replacements.Any(r => r.Property == "Header" && r.OriginalValue == "Column Header"));
    }

    [Test]
    public void ProcessXamlFile_ToolTipProperty()
    {
        // Arrange: Create a XAML file with different controls using ToolTip property
        string xamlWithToolTip =
@"<UserControl xmlns=""http://schemas.microsoft.com/winfx/2006/xaml/presentation""
    xmlns:x=""http://schemas.microsoft.com/winfx/2006/xaml"">
    <Grid>
        <Button ToolTip=""Button Tooltip"" />
        <TextBlock ToolTip=""Text Tooltip"" />
        <Image ToolTip=""Image Tooltip"" />
    </Grid>
</UserControl>";
        string tempFile = Path.Combine(_tempFolder, "TestToolTipProperty.xaml");
        File.WriteAllText(tempFile, xamlWithToolTip);

        var processor = new XamlProcessor();

        // Act: Process the file
        FileProcessingResult result = processor.ProcessXamlFile(tempFile);

        // Assert: Check that all ToolTip properties are processed
        Assert.AreEqual(3, result.Replacements.Count, "Should process all ToolTip properties");
        Assert.IsTrue(result.Replacements.Any(r => r.Property == "ToolTip" && r.OriginalValue == "Button Tooltip"));
        Assert.IsTrue(result.Replacements.Any(r => r.Property == "ToolTip" && r.OriginalValue == "Text Tooltip"));
        Assert.IsTrue(result.Replacements.Any(r => r.Property == "ToolTip" && r.OriginalValue == "Image Tooltip"));
    }

    [Test]
    public void ProcessXamlFile_TitleProperty()
    {
        // Arrange: Create a XAML file with different controls using Title property
        string xamlWithTitle =
@"<UserControl xmlns=""http://schemas.microsoft.com/winfx/2006/xaml/presentation""
    xmlns:x=""http://schemas.microsoft.com/winfx/2006/xaml""
    Title=""Control Title"">
    <Grid>
        <Window Title=""Window Title"" />
        <Page Title=""Page Title"" />
    </Grid>
</UserControl>";
        string tempFile = Path.Combine(_tempFolder, "TestTitleProperty.xaml");
        File.WriteAllText(tempFile, xamlWithTitle);

        var processor = new XamlProcessor();

        // Act: Process the file
        FileProcessingResult result = processor.ProcessXamlFile(tempFile);

        // Assert: Check that all Title properties are processed
        Assert.AreEqual(3, result.Replacements.Count, "Should process all Title properties");
        Assert.IsTrue(result.Replacements.Any(r => r.Property == "Title" && r.OriginalValue == "Control Title"));
        Assert.IsTrue(result.Replacements.Any(r => r.Property == "Title" && r.OriginalValue == "Window Title"));
        Assert.IsTrue(result.Replacements.Any(r => r.Property == "Title" && r.OriginalValue == "Page Title"));
    }

    [Test]
    public void ProcessXamlFile_MixedSupportedProperties()
    {
        // Arrange: Create a XAML file with a mix of supported properties and bindings
        string xamlWithMixedProps =
@"<UserControl xmlns=""http://schemas.microsoft.com/winfx/2006/xaml/presentation""
    xmlns:x=""http://schemas.microsoft.com/winfx/2006/xaml""
    Title=""Main Title"">
    <Grid>
        <Button Content=""Click Me"" ToolTip=""Button Info"" />
        <TextBlock Text=""{Binding Message}"" />
        <GroupBox Header=""Settings"">
            <TextBox Text=""Enter text"" />
        </GroupBox>
        <TabItem Header=""{Binding TabName}"" />
    </Grid>
</UserControl>";
        string tempFile = Path.Combine(_tempFolder, "TestMixedProps.xaml");
        File.WriteAllText(tempFile, xamlWithMixedProps);

        var processor = new XamlProcessor();

        // Act: Process the file
        FileProcessingResult result = processor.ProcessXamlFile(tempFile);

        // Assert: Check that only non-bound supported properties are processed
        Assert.AreEqual(5, result.Replacements.Count, "Should process only non-bound supported properties");
        Assert.IsTrue(result.Replacements.Any(r => r.Property == "Title" && r.OriginalValue == "Main Title"));
        Assert.IsTrue(result.Replacements.Any(r => r.Property == "Content" && r.OriginalValue == "Click Me"));
        Assert.IsTrue(result.Replacements.Any(r => r.Property == "ToolTip" && r.OriginalValue == "Button Info"));
        Assert.IsTrue(result.Replacements.Any(r => r.Property == "Header" && r.OriginalValue == "Settings"));
        Assert.IsTrue(result.Replacements.Any(r => r.Property == "Text" && r.OriginalValue == "Enter text"));
    }

    [Test]
    public void ProcessXamlFile_LineAndColumnCalculation()
    {
        // Arrange: Create a XAML file with properties at specific positions
        string xamlWithSpecificPositions =
@"<UserControl xmlns=""http://schemas.microsoft.com/winfx/2006/xaml/presentation""
              xmlns:x=""http://schemas.microsoft.com/winfx/2006/xaml"">
    <Grid>
        <Button Content=""First""
                ToolTip=""Second"" />
        <TextBlock 
            Text=""Third"" />
        <Label Content=""Fourth""
               Header=""Fifth"" />
    </Grid>
</UserControl>";
        string tempFile = Path.Combine(_tempFolder, "TestLineColumn.xaml");
        File.WriteAllText(tempFile, xamlWithSpecificPositions);

        var processor = new XamlProcessor();

        // Act: Process the file
        FileProcessingResult result = processor.ProcessXamlFile(tempFile);

        // Assert: Check line and column numbers for each property
        var replacements = result.Replacements
            .OrderBy(r => r.LineNumber)
            .ThenBy(r => r.Column)
            .ToList();

        Assert.AreEqual(5, replacements.Count, "Should find all five properties");

        // Verify each property's position and content
        var expectedPositions = new[]
        {
            // Property     Value      Line  Column  Line Content
            ("Content",    "First",     4,    26,    "        <Button Content=\"First\""),
            ("ToolTip",    "Second",    5,    26,    "                ToolTip=\"Second\" />"),
            ("Text",       "Third",     7,    19,    "            Text=\"Third\" />"),
            ("Content",    "Fourth",    8,    25,    "        <Label Content=\"Fourth\""),
            ("Header",     "Fifth",    9,    24,    "               Header=\"Fifth\" />")
        };

        foreach (var (property, value, line, col, content) in expectedPositions)
        {
            var record = replacements.First(r => r.OriginalValue == value);
            Assert.Multiple(() =>
            {
                Assert.That(record.Property, Is.EqualTo(property), $"Property for '{value}'");
                Assert.That(record.LineNumber, Is.EqualTo(line), $"Line number for '{value}'");
                Assert.That(record.Column, Is.EqualTo(col), $"Column for '{value}'");
                Assert.That(record.LineContent.TrimEnd(), Is.EqualTo(content), $"Line content for '{value}'");
            });
        }
    }

    [Test]
    public void ProcessXamlFile_EncodedCharacters()
    {
        // Arrange: Create a XAML file with encoded characters
        string xamlWithEncodedChars =
@"<UserControl xmlns=""http://schemas.microsoft.com/winfx/2006/xaml/presentation""
    xmlns:x=""http://schemas.microsoft.com/winfx/2006/xaml"">
    <Grid>
        <TextBlock Text=""Show &#038; Edit Details"" />
        <TextBlock Text=""ALCON Vision Planner Training mode.&#x0a; NOT FOR CLINICAL USE.&#x0a; For Alcon internal training only."" />
        <TextBlock Text=""Copyright &#169; 2024"" />
    </Grid>
</UserControl>";
        string tempFile = Path.Combine(_tempFolder, "TestEncodedChars.xaml");
        File.WriteAllText(tempFile, xamlWithEncodedChars);

        var processor = new XamlProcessor();

        // Act: Process the file
        FileProcessingResult result = processor.ProcessXamlFile(tempFile);

        // Assert: Check that all texts with encoded characters are processed correctly
        Assert.AreEqual(3, result.Replacements.Count, "Should process all texts with encoded characters");

        // Verify each text's position and content
        var replacements = result.Replacements.OrderBy(r => r.LineNumber).ToList();

        // Check first text with ampersand
        var ampersandRecord = replacements[0];
        Assert.Multiple(() =>
        {
            Assert.That(ampersandRecord.Property, Is.EqualTo("Text"));
            Assert.That(ampersandRecord.OriginalValue, Is.EqualTo("Show &#038; Edit Details"));
            Assert.That(ampersandRecord.LineNumber, Is.GreaterThan(0), "Line number should be valid");
            Assert.That(ampersandRecord.Column, Is.GreaterThan(0), "Column should be valid");
            Assert.That(ampersandRecord.LineContent, Does.Contain("Show &#038; Edit Details"));
        });

        // Check second text with newlines
        var newlineRecord = replacements[1];
        Assert.Multiple(() =>
        {
            Assert.That(newlineRecord.Property, Is.EqualTo("Text"));
            Assert.That(newlineRecord.OriginalValue, Is.EqualTo("ALCON Vision Planner Training mode.&#x0a; NOT FOR CLINICAL USE.&#x0a; For Alcon internal training only."));
            Assert.That(newlineRecord.LineNumber, Is.GreaterThan(0), "Line number should be valid");
            Assert.That(newlineRecord.Column, Is.GreaterThan(0), "Column should be valid");
            Assert.That(newlineRecord.LineContent, Does.Contain("ALCON Vision Planner Training mode.&#x0a;"));
        });

        // Check third text with copyright symbol
        var copyrightRecord = replacements[2];
        Assert.Multiple(() =>
        {
            Assert.That(copyrightRecord.Property, Is.EqualTo("Text"));
            Assert.That(copyrightRecord.OriginalValue, Is.EqualTo("Copyright &#169; 2024"));
            Assert.That(copyrightRecord.LineNumber, Is.GreaterThan(0), "Line number should be valid");
            Assert.That(copyrightRecord.Column, Is.GreaterThan(0), "Column should be valid");
            Assert.That(copyrightRecord.LineContent, Does.Contain("Copyright &#169; 2024"));
        });
    }

    [Test]
    public void ProcessXamlFile_EncodedCharactersWithAttributes()
    {
        // Arrange: Create a XAML file with encoded characters and additional attributes
        string xamlWithEncodedCharsAndAttrs =
@"<UserControl xmlns=""http://schemas.microsoft.com/winfx/2006/xaml/presentation""
    xmlns:x=""http://schemas.microsoft.com/winfx/2006/xaml"">
    <Grid>
        <TextBlock Name=""ArgosTrainingTextBox""
                   FontFamily=""Calibri, Arial""
                   FontSize=""24""
                   Foreground=""{DynamicResource EditBoxErrorAdornerColor}""
                   Text=""ALCON Vision Planner Training mode.&#x0a; NOT FOR CLINICAL USE.&#x0a; For Alcon internal training only.""
                   TextAlignment=""Center""
                   TextWrapping=""Wrap"" />
        <TextBlock Style=""{StaticResource TabelHighlightedTextBlockStyle}""
                   VerticalAlignment=""Center""
                   Padding=""5,0,5,0""
                   x:Name=""label""
                   Text=""Show &#038; Edit Details""
                   FontSize=""13""
                   FontWeight=""Bold"" />
    </Grid>
</UserControl>";
        string tempFile = Path.Combine(_tempFolder, "TestEncodedCharsWithAttrs.xaml");
        File.WriteAllText(tempFile, xamlWithEncodedCharsAndAttrs);

        var processor = new XamlProcessor();

        // Act: Process the file
        FileProcessingResult result = processor.ProcessXamlFile(tempFile);

        // Assert: Check that all texts with encoded characters are processed correctly
        Assert.AreEqual(2, result.Replacements.Count, "Should process all texts with encoded characters");

        // Verify each text's position and content
        var replacements = result.Replacements.OrderBy(r => r.LineNumber).ToList();

        // Check first text with newlines and multiple attributes
        var newlineRecord = replacements[0];
        Assert.Multiple(() =>
        {
            Assert.That(newlineRecord.Property, Is.EqualTo("Text"));
            Assert.That(newlineRecord.OriginalValue, Is.EqualTo("ALCON Vision Planner Training mode.&#x0a; NOT FOR CLINICAL USE.&#x0a; For Alcon internal training only."));
            Assert.That(newlineRecord.LineNumber, Is.GreaterThan(0), "Line number should be valid");
            Assert.That(newlineRecord.Column, Is.GreaterThan(0), "Column should be valid");
            Assert.That(newlineRecord.ControlType, Is.EqualTo("TextBlock"));
            Assert.That(newlineRecord.LineContent, Does.Contain("Text=\"ALCON Vision Planner Training mode.&#x0a;"));
        });

        // Check second text with ampersand and multiple attributes
        var ampersandRecord = replacements[1];
        Assert.Multiple(() =>
        {
            Assert.That(ampersandRecord.Property, Is.EqualTo("Text"));
            Assert.That(ampersandRecord.OriginalValue, Is.EqualTo("Show &#038; Edit Details"));
            Assert.That(ampersandRecord.LineNumber, Is.GreaterThan(0), "Line number should be valid");
            Assert.That(ampersandRecord.Column, Is.GreaterThan(0), "Column should be valid");
            Assert.That(ampersandRecord.ControlType, Is.EqualTo("TextBlock"));
            Assert.That(ampersandRecord.LineContent, Does.Contain("Text=\"Show &#038; Edit Details\""));
        });
    }

    [Test]
    public void ProcessXamlFile_TextWithBackslash()
    {
        // Arrange: XAML with a backslash in the Text property
        string xamlWithBackslash =
    @"<UserControl xmlns=""http://schemas.microsoft.com/winfx/2006/xaml/presentation""
    xmlns:x=""http://schemas.microsoft.com/winfx/2006/xaml"">
    <Grid>
        <TextBlock Grid.Row=""0"" Grid.Column=""0"" Style=""{StaticResource VPTextBlockStyle}""
                   Text=""Cyl (D) \ Age (yrs)"" />
    </Grid>
</UserControl>";
        string tempFile = Path.Combine(_tempFolder, "TestBackslashText.xaml");
        File.WriteAllText(tempFile, xamlWithBackslash);

        var processor = new XamlProcessor();

        // Act: Process the file
        FileProcessingResult result = processor.ProcessXamlFile(tempFile);

        // Assert: Should extract the text with the backslash exactly as in the XAML
        Assert.AreEqual(1, result.Replacements.Count, "Should find one Text property");
        var record = result.Replacements.First();
        Assert.That(record.Property, Is.EqualTo("Text"));
        Assert.That(record.OriginalValue, Is.EqualTo(@"Cyl (D) \ Age (yrs)"));
        // If you want to check the raw value as it appears in the XAML (if you store it)
        // Assert.That(record.RawOriginalValue, Is.EqualTo(@"Cyl (D) \ Age (yrs)"));
    }

    [Test]
    public void GetMergedCustomControlNonDependencyProperties_OnlyHardcoded()
    {
        var processor = new XamlProcessor(); // No project path, so no config
        var merged = processor.GetMergedCustomControlNonDependencyProperties();

        Assert.IsTrue(merged.ContainsKey("RefractionControl"));
        CollectionAssert.AreEquivalent(new[] { "Title", "Header" }, merged["RefractionControl"]);
    }

    [Test]
    public void GetMergedCustomControlNonDependencyProperties_OnlyConfig()
    {
        string tempConfigDir = Path.Combine(_tempFolder, "OnlyConfig");
        Directory.CreateDirectory(tempConfigDir);
        string configPath = Path.Combine(tempConfigDir, "ProjectTextExtractorConfig.json");

        var configJson = @"
        {
            ""CustomControlNonDependencyProperties"": {
                ""ConfigOnlyControl"": [""ConfigProp1"", ""ConfigProp2""]
            }
        }";
        File.WriteAllText(configPath, configJson);

        ProjectTextExtractorConfigManager.Reset();
        var processor = new XamlProcessor(tempConfigDir);
        var merged = processor.GetMergedCustomControlNonDependencyProperties();

        Assert.IsTrue(merged.ContainsKey("ConfigOnlyControl"));
        CollectionAssert.AreEquivalent(new[] { "ConfigProp1", "ConfigProp2" }, merged["ConfigOnlyControl"]);
    }

    [Test]
    public void GetMergedCustomControlNonDependencyProperties_MergeConfigAndHardcoded()
    {
        string tempConfigDir = Path.Combine(_tempFolder, "MergeConfigAndHardcoded");
        Directory.CreateDirectory(tempConfigDir);
        string configPath = Path.Combine(tempConfigDir, "ProjectTextExtractorConfig.json");

        var configJson = @"
        {
            ""CustomControlNonDependencyProperties"": {
                ""RefractionControl"": [""Header"", ""ExtraConfigProp""]
            }
        }";
        File.WriteAllText(configPath, configJson);

        ProjectTextExtractorConfigManager.Reset();
        var processor = new XamlProcessor(tempConfigDir);
        var merged = processor.GetMergedCustomControlNonDependencyProperties();

        // Should contain Title (hardcoded), Header (both), and ExtraConfigProp (config)
        Assert.IsTrue(merged.ContainsKey("RefractionControl"));
        CollectionAssert.AreEquivalent(new[] { "Title", "Header", "ExtraConfigProp" }, merged["RefractionControl"]);
    }

    [Test]
    public void GetMergedCustomControlNonDependencyProperties_InvalidConfigFile()
    {
        string tempConfigDir = Path.Combine(_tempFolder, "InvalidConfig");
        Directory.CreateDirectory(tempConfigDir);
        string configPath = Path.Combine(tempConfigDir, "ProjectTextExtractorConfig.json");

        // Write invalid JSON
        File.WriteAllText(configPath, "{ invalid json }");

        ProjectTextExtractorConfigManager.Reset();
        var processor = new XamlProcessor(tempConfigDir);
        var merged = processor.GetMergedCustomControlNonDependencyProperties();

        Assert.IsTrue(merged.ContainsKey("RefractionControl"));
        CollectionAssert.AreEquivalent(new[] { "Title", "Header" }, merged["RefractionControl"]);
    }

    [Test]
    public void GetMergedCustomControlNonDependencyProperties_EmptyConfigSection()
    {
        string tempConfigDir = Path.Combine(_tempFolder, "EmptyConfigSection");
        Directory.CreateDirectory(tempConfigDir);
        string configPath = Path.Combine(tempConfigDir, "ProjectTextExtractorConfig.json");

        var configJson = @"{ ""CustomControlNonDependencyProperties"": {} }";
        File.WriteAllText(configPath, configJson);

        ProjectTextExtractorConfigManager.Reset();
        var processor = new XamlProcessor(tempConfigDir);
        var merged = processor.GetMergedCustomControlNonDependencyProperties();

        Assert.IsTrue(merged.ContainsKey("RefractionControl"));
        CollectionAssert.AreEquivalent(new[] { "Title", "Header" }, merged["RefractionControl"]);
    }

    [Test]
    public void ProcessXamlFile_CustomControlPropertyFromConfig_IsNonDependency()
    {
        // Arrange: Create config with a custom control and property
        string tempConfigDir = Path.Combine(_tempFolder, "CustomControlConfig");
        Directory.CreateDirectory(tempConfigDir);
        string configPath = Path.Combine(tempConfigDir, "ProjectTextExtractorConfig.json");
        var configJson = @"
        {
            ""CustomControlNonDependencyProperties"": {
                ""MyCustomControl"": [""SpecialProp""]
            }
        }";
        File.WriteAllText(configPath, configJson);

        ProjectTextExtractorConfigManager.Reset();
        var processor = new XamlProcessor(tempConfigDir);

        // XAML with the custom control and property
        string xaml = @"
<UserControl xmlns=""http://schemas.microsoft.com/winfx/2006/xaml/presentation""
    xmlns:x=""http://schemas.microsoft.com/winfx/2006/xaml""
    xmlns:local=""clr -namespace:MyNamespace"">
    <Grid>
        <local:MyCustomControl SpecialProp=""TestValue"" />
    </Grid>
</UserControl>";
        string tempFile = Path.Combine(tempConfigDir, "TestCustomControlConfig.xaml");
        File.WriteAllText(tempFile, xaml);

        // Act
        var result = processor.ProcessXamlFile(tempFile);

        // Assert
        Assert.AreEqual(1, result.Replacements.Count, "Should process SpecialProp as a non-dependency property");
        var record = result.Replacements.First();
        Assert.AreEqual("SpecialProp", record.Property);
        Assert.AreEqual("TestValue", record.OriginalValue);
        Assert.IsFalse(record.IsDependencyProperty, "SpecialProp should be marked as non-dependency");
    }

    [Test]
    public void ProcessXamlFile_MergedCustomControlProperties_AreRespected()
    {
        // Arrange: Config adds an extra property to a hardcoded control
        string tempConfigDir = Path.Combine(_tempFolder, "MergedPropsConfig");
        Directory.CreateDirectory(tempConfigDir);
        string configPath = Path.Combine(tempConfigDir, "ProjectTextExtractorConfig.json");
        var configJson = @"
        {
            ""CustomControlNonDependencyProperties"": {
                ""RefractionControl"": [""ExtraProp""]
            }
        }";
        File.WriteAllText(configPath, configJson);

        ProjectTextExtractorConfigManager.Reset();
        var processor = new XamlProcessor(tempConfigDir);

        // XAML with both hardcoded and config properties
        string xaml = @"
<UserControl xmlns=""http://schemas.microsoft.com/winfx/2006/xaml/presentation""
    xmlns:x=""http://schemas.microsoft.com/winfx/2006/xaml""
    xmlns:UiWidgetMeasurement=""clr -namespace:Alcon.UiWidgetMeasurement;assembly=Alcon.UiWidgetMeasurement"" >
    <Grid>
        <UiWidgetMeasurement:RefractionControl Title=""T1"" Header =""H1"" ExtraProp =""E1"" />
    </Grid>
</UserControl>";
        string tempFile = Path.Combine(tempConfigDir, "TestMergedProps.xaml");
        File.WriteAllText(tempFile, xaml);

        // Act
        var result = processor.ProcessXamlFile(tempFile);

        // Assert: All three should be processed as non-dependency properties
        Assert.AreEqual(3, result.Replacements.Count, "Should process all three properties");
        var props = result.Replacements.ToDictionary(r => r.Property, r => r);
        Assert.IsFalse(props["Title"].IsDependencyProperty);
        Assert.IsFalse(props["Header"].IsDependencyProperty);
        Assert.IsFalse(props["ExtraProp"].IsDependencyProperty);
    }

    [Test]
    public void ProcessXamlFile_StandardSupportedProperty_IsDependency()
    {
        // Arrange: No config, just hardcoded supported property
        var processor = new XamlProcessor();

        string xaml = @"
        <UserControl xmlns=""http://schemas.microsoft.com/winfx/2006/xaml/presentation""
            xmlns:x=""http://schemas.microsoft.com/winfx/2006/xaml"">
            <Grid>
                <Button Content=""Click Me"" />
            </Grid>
        </UserControl>";
        string tempFile = Path.Combine(_tempFolder, "TestStandardSupported.xaml");
        File.WriteAllText(tempFile, xaml);

        // Act
        var result = processor.ProcessXamlFile(tempFile);

        // Assert
        Assert.AreEqual(1, result.Replacements.Count, "Should process Content property");
        var record = result.Replacements.First();
        Assert.AreEqual("Content", record.Property);
        Assert.IsTrue(record.IsDependencyProperty, "Content should be a dependency property");
    }

    [Test]
    public void ProcessXamlFile_DuplicateTextValues_ExtractsAllOccurrences()
    {
        // Arrange: Create a XAML file with duplicate text values in different elements
        // This reproduces the issue where "Recently Updated" appears twice but only first occurrence is extracted
        string xamlWithDuplicateText =
@"<UserControl xmlns=""http://schemas.microsoft.com/winfx/2006/xaml/presentation""
    xmlns:x=""http://schemas.microsoft.com/winfx/2006/xaml"">
    <Grid>
        <StackPanel>
            <RadioButton GroupName=""PlanCriteriaRadioGroup"" Content=""All"" />
            <RadioButton GroupName=""PlanCriteriaRadioGroup"" Content=""Coming Soon"" />
            <RadioButton GroupName=""PlanCriteriaRadioGroup"" Content=""Recently Updated"" />
            <RadioButton GroupName=""PlanCriteriaRadioGroup"" Content=""Exported"" />
        </StackPanel>
        <StackPanel>
            <RadioButton GroupName=""SurgeryCriteriaRadioGroup"" Content=""All"" />
            <RadioButton GroupName=""SurgeryCriteriaRadioGroup"" Content=""Special Cases"" />
            <RadioButton GroupName=""SurgeryCriteriaRadioGroup"" Content=""Recently Updated"" />
        </StackPanel>
    </Grid>
</UserControl>";
        string tempFile = Path.Combine(_tempFolder, "TestDuplicateText.xaml");
        File.WriteAllText(tempFile, xamlWithDuplicateText);

        var processor = new XamlProcessor();

        // Act: Process the file
        FileProcessingResult result = processor.ProcessXamlFile(tempFile);

        // Assert: Should find all text occurrences, including both "Recently Updated" instances
        var allContentValues = result.Replacements
            .Where(r => r.Property == "Content")
            .Select(r => r.OriginalValue)
            .ToList();

        // Should find all unique content values
        var expectedValues = new[] { "All", "Coming Soon", "Recently Updated", "Exported", "Special Cases" };
        foreach (var expectedValue in expectedValues)
        {
            Assert.IsTrue(allContentValues.Contains(expectedValue),
                $"Should find Content property with value '{expectedValue}'");
        }

        // Most importantly: should find TWO occurrences of "Recently Updated"
        var recentlyUpdatedCount = allContentValues.Count(v => v == "Recently Updated");
        Assert.AreEqual(2, recentlyUpdatedCount,
            "Should find exactly 2 occurrences of 'Recently Updated' text");

        // Verify that both occurrences have different line numbers
        var recentlyUpdatedRecords = result.Replacements
            .Where(r => r.Property == "Content" && r.OriginalValue == "Recently Updated")
            .ToList();

        Assert.AreEqual(2, recentlyUpdatedRecords.Count, "Should have 2 replacement records for 'Recently Updated'");

        var lineNumbers = recentlyUpdatedRecords.Select(r => r.LineNumber).Distinct().ToList();
        Assert.AreEqual(2, lineNumbers.Count,
            "The two 'Recently Updated' occurrences should be on different lines");

        // Verify line numbers are reasonable (should be around lines 7 and 12 based on the XAML structure)
        Assert.IsTrue(lineNumbers.All(ln => ln > 0 && ln <= 15),
            "Line numbers should be within reasonable range");
    }

    [Test]
    public void ProcessXamlFile_PatientPreviewControlSample_ExtractsBothRecentlyUpdatedOccurrences()
    {
        // Arrange: Use the actual PatientPreviewControl.xaml sample file
        // The sample file is in the unit test etc/samples folder
        string sampleFilePath = Path.Combine(Path.GetDirectoryName(Path.GetDirectoryName(_tempFolder)), "etc", "samples", "PatientPreviewControl.xaml");

        // Verify the sample file exists
        Assert.IsTrue(File.Exists(sampleFilePath), $"Sample file should exist at: {sampleFilePath}");

        var processor = new XamlProcessor();

        // Act: Process the sample file
        FileProcessingResult result = processor.ProcessXamlFile(sampleFilePath);

        // Assert: Should find both "Recently Updated" occurrences
        var recentlyUpdatedRecords = result.Replacements
            .Where(r => r.Property == "Content" && r.OriginalValue == "Recently Updated")
            .ToList();

        Assert.AreEqual(2, recentlyUpdatedRecords.Count,
            "Should find exactly 2 occurrences of 'Recently Updated' in PatientPreviewControl.xaml");

        // Verify that both occurrences have different line numbers
        var lineNumbers = recentlyUpdatedRecords.Select(r => r.LineNumber).Distinct().ToList();
        Assert.AreEqual(2, lineNumbers.Count,
            "The two 'Recently Updated' occurrences should be on different lines");

        // Verify the line numbers match the expected positions in the file (lines 59 and 71)
        var sortedLineNumbers = lineNumbers.OrderBy(ln => ln).ToList();
        Assert.AreEqual(59, sortedLineNumbers[0], "First 'Recently Updated' should be on line 59");
        Assert.AreEqual(71, sortedLineNumbers[1], "Second 'Recently Updated' should be on line 71");

        // Verify line content contains the expected context
        var firstRecord = recentlyUpdatedRecords.First(r => r.LineNumber == 59);
        var secondRecord = recentlyUpdatedRecords.First(r => r.LineNumber == 71);

        Assert.IsTrue(firstRecord.LineContent.Contains("PlanCriteriaRadioGroup"),
            "First occurrence should be in PlanCriteriaRadioGroup context");
        Assert.IsTrue(secondRecord.LineContent.Contains("SurgeryCriteriaRadioGroup"),
            "Second occurrence should be in SurgeryCriteriaRadioGroup context");
    }
}
