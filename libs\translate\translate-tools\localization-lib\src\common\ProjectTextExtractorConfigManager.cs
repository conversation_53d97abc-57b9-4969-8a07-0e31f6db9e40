using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;

namespace LocalizationLib
{
    /// <summary>
    /// Manages configuration for project text extraction, including verification results and extracted texts.
    /// </summary>
    public class ProjectTextExtractorConfigManager
    {
        private static ProjectTextExtractorConfigManager _instance;
        private static readonly object _lock = new object();

        private readonly string _configPath;
        private ProjectTextExtractorConfig _config;
        private readonly string _projectPath;

        private ProjectTextExtractorConfigManager(string projectPath, bool needLoadSubConfig = false)
        {
            _projectPath = projectPath;
            _configPath = Path.Combine(projectPath, "ProjectTextExtractorConfig.json");
            LoadOrCreateConfig(needLoadSubConfig);
        }

        /// <summary>
        /// Gets the singleton instance of ProjectTextExtractorConfigManager.
        /// </summary>
        public static ProjectTextExtractorConfigManager Instance
        {
            get
            {
                if (_instance == null)
                {
                    throw new InvalidOperationException("ProjectTextExtractorConfigManager not initialized. Call Initialize first.");
                }
                return _instance;
            }
        }

        /// <summary>
        /// Initializes the ProjectTextExtractorConfigManager with the specified project path.
        /// </summary>
        public static ProjectTextExtractorConfigManager Initialize(string projectPath, bool needLoadSubConfig = false)
        {
            if (_instance == null)
            {
                lock (_lock)
                {
                    if (_instance == null)
                    {
                        _instance = new ProjectTextExtractorConfigManager(projectPath, needLoadSubConfig);
                    }
                }
            }
            else if (_instance._projectPath != projectPath)
            {
                throw new InvalidOperationException(
                    string.Format("ProjectTextExtractorConfigManager already initialized with different project path: {0}",
                    _instance._projectPath));
            }
            return _instance;
        }

        /// <summary>
        /// Resets the singleton instance so it can be initialized with a new project path.
        /// </summary>
        public static void Reset()
        {
            lock (_lock)
            {
                _instance = null;
            }
        }

        /// <summary>
        /// Switches to a new project path without losing in-memory configuration data.
        /// This is the preferred method instead of Reset() + Initialize() when you want to preserve unsaved changes.
        /// </summary>
        /// <param name="newProjectPath">The new project path to switch to</param>
        /// <param name="saveCurrentBeforeSwitching">Whether to save the current config before switching</param>
        /// <param name="needLoadSubConfig">Whether to load subconfigs for the new path</param>
        /// <returns>The instance with the new project path</returns>
        public static ProjectTextExtractorConfigManager SwitchProject(
            string newProjectPath,
            bool saveCurrentBeforeSwitching = false,
            bool needLoadSubConfig = false)
        {
            if (string.IsNullOrEmpty(newProjectPath))
            {
                throw new ArgumentNullException(nameof(newProjectPath));
            }

            lock (_lock)
            {
                // If we already have an instance with this path, just return it
                if (_instance != null && _instance._projectPath == newProjectPath)
                {
                    return _instance;
                }

                // Create a new instance with the new path
                var newInstance = new ProjectTextExtractorConfigManager(newProjectPath, needLoadSubConfig);

                // If we have an existing instance with data
                if (_instance != null)
                {
                    // Save the current config if requested
                    if (saveCurrentBeforeSwitching)
                    {
                        _instance.SaveConfig();
                    }
                }

                // Update the singleton instance
                _instance = newInstance;

                return _instance;
            }
        }

        private void LoadOrCreateConfig(bool needLoadSubConfig)
        {
            try
            {
                LoadMainConfig();
                if (needLoadSubConfig)
                {
                    LoadSubfolderConfigs();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine("Error loading config: {0}", ex.Message);
                _config = new ProjectTextExtractorConfig { ProjectPath = _projectPath };
            }
        }

        private void LoadMainConfig()
        {
            if (File.Exists(_configPath))
            {
                string json = File.ReadAllText(_configPath);
                try
                {
                    _config = JsonConvert.DeserializeObject<ProjectTextExtractorConfig>(json);
                    if (_config == null)
                    {
                        _config = new ProjectTextExtractorConfig { ProjectPath = _projectPath };
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine("Error deserializing config: {0}", ex.Message);
                    _config = new ProjectTextExtractorConfig { ProjectPath = _projectPath };
                }
            }
            else
            {
                _config = new ProjectTextExtractorConfig { ProjectPath = _projectPath };
            }
        }

        private void LoadSubfolderConfigs()
        {
            string[] directories = Directory.GetDirectories(_projectPath, "*", SearchOption.AllDirectories);

            // Skip processing if no skip patterns defined
            if (_config?.SkipPatterns == null || !_config.SkipPatterns.Any())
            {
                return;
            }

            foreach (string dir in directories)
            {
                // Get relative path for pattern matching
                string relativePath = PathHelpers.GetRelativePath(_projectPath, dir).Replace('/', '\\');

                // Skip if the directory matches any skip pattern
                bool shouldSkip = _config.SkipPatterns.Any(pattern =>
                {
                    // Convert wildcard pattern to regex pattern
                    string regexPattern = "^.*" +
                        pattern.Replace("\\", "\\\\")  // Escape backslashes
                              .Replace("*", ".*")      // Convert wildcard to regex
                              .Replace("?", ".") +     // Convert single char wildcard
                        "$";

                    return System.Text.RegularExpressions.Regex.IsMatch(relativePath, regexPattern,
                        System.Text.RegularExpressions.RegexOptions.IgnoreCase);
                });

                if (shouldSkip)
                {
                    continue;
                }

                string subConfigPath = Path.Combine(dir, "ProjectTextExtractorConfig.json");
                if (!File.Exists(subConfigPath)) continue;

                try
                {
                    MergeSubfolderConfig(dir, subConfigPath);
                }
                catch (Exception ex)
                {
                    Console.WriteLine("Error loading subfolder config {0}: {1}", subConfigPath, ex.Message);
                }
            }
        }

        private void MergeSubfolderConfig(string dir, string subConfigPath)
        {
            // Sub-config merging no longer needed for verification results
            // Only merge other configuration data if needed
        }



        public string GenerateVerificationKey(ReplacementRecord record)
        {
            return GenerateVerificationKey(record.FilePath, record.LineNumber, record.Column, record.OriginalValue);
        }

        private string GenerateVerificationKey(string filePath, int lineNumber, int column, string text)
        {
            string relativePath = GetRelativePath(filePath).Replace("\\", "/");
            string hash = LocalizationUtils.HashString(text).ToString();
            return string.Format("{0}:{1}:{2}:{3}", relativePath, lineNumber, column, hash);
        }

        private string GetRelativePath(string filePath)
        {
            if (string.IsNullOrEmpty(filePath)) return string.Empty;
            return LocalizationUtils.GetRelativePath(filePath, _projectPath);
        }

        /// <summary>
        /// Gets the current configuration
        /// </summary>
        /// <returns>The ProjectTextExtractorConfig instance</returns>
        public ProjectTextExtractorConfig GetConfig()
        {
            return _config;
        }

        /// <summary>
        /// Safely gets the instance if it exists or returns null without throwing an exception
        /// </summary>
        /// <returns>The current instance or null if not initialized</returns>
        public static ProjectTextExtractorConfigManager GetInstanceSafe()
        {
            return _instance;
        }

        /// <summary>
        /// Safely gets the configuration. If the instance doesn't exist and a project path is provided, 
        /// it will automatically initialize the instance.
        /// </summary>
        /// <param name="projectPath">Optional project path to initialize with if the instance doesn't exist</param>
        /// <returns>The configuration or null if not initialized and no project path provided</returns>
        public static ProjectTextExtractorConfig GetConfigSafe(string projectPath = null)
        {
            if (_instance == null && !string.IsNullOrEmpty(projectPath))
            {
                try
                {
                    System.Diagnostics.Debug.WriteLine($"Auto-initializing ProjectTextExtractorConfigManager with path: {projectPath}");
                    EnsureInitialized(projectPath, true); // Load subconfigs including skip patterns
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Error auto-initializing ProjectTextExtractorConfigManager: {ex.Message}");
                    return null;
                }
            }

            return _instance?.GetConfig();
        }

        /// <summary>
        /// Ensures that the ProjectTextExtractorConfigManager is initialized with the given project path.
        /// If it's already initialized with a different path, it will switch to the new path.
        /// </summary>
        /// <param name="projectPath">The project path to ensure</param>
        /// <param name="needLoadSubConfig">Whether to load subconfigs</param>
        /// <returns>The manager instance</returns>
        public static ProjectTextExtractorConfigManager EnsureInitialized(string projectPath, bool needLoadSubConfig = false)
        {
            if (string.IsNullOrEmpty(projectPath))
            {
                throw new ArgumentNullException(nameof(projectPath));
            }

            // If not initialized at all, initialize
            if (_instance == null)
            {
                return Initialize(projectPath, needLoadSubConfig);
            }

            // If initialized with a different path, switch
            if (_instance._projectPath != projectPath)
            {
                return SwitchProject(projectPath, false, needLoadSubConfig);
            }

            // Already initialized with this path
            return _instance;
        }

        /// <summary>
        /// Saves the current configuration to disk
        /// </summary>
        public void SaveConfig()
        {
            try
            {
                string json = JsonConvert.SerializeObject(_config, Formatting.Indented);
                File.WriteAllText(_configPath, json);
            }
            catch (Exception ex)
            {
                Console.WriteLine("Error saving config: {0}", ex.Message);
            }
        }

        public void AddExtractedTexts(IEnumerable<ReplacementRecord> replacements)
        {
            AddExtractedTextsWithoutSaving(replacements);
            SaveConfig();
        }

        /// <summary>
        /// Updates or adds a UITextInfo to the config without saving to disk
        /// </summary>
        public bool UpdateUIText(string key, UITextInfo textInfo)
        {
            if (_config.UITexts == null)
            {
                _config.UITexts = new Dictionary<string, UITextInfo>();
            }

            bool isNew = !_config.UITexts.ContainsKey(key);
            _config.UITexts[key] = textInfo;
            _config.LastExtraction = DateTime.UtcNow;

            return isNew;
        }

        /// <summary>
        /// Updates or adds an ExtractedTextInfo to the config without saving to disk
        /// </summary>
        public bool UpdateExtractedText(string key, ExtractedTextInfo textInfo)
        {
            if (_config.ExtractedTexts == null)
            {
                _config.ExtractedTexts = new Dictionary<string, ExtractedTextInfo>();
            }

            bool isNew = !_config.ExtractedTexts.ContainsKey(key);
            _config.ExtractedTexts[key] = textInfo;
            _config.LastExtraction = DateTime.UtcNow;

            return isNew;
        }

        /// <summary>
        /// Updates only the status of a UITextInfo in the config without saving to disk
        /// </summary>
        public bool UpdateUITextStatus(string key, TextStatus status)
        {
            if (_config.UITexts == null || !_config.UITexts.ContainsKey(key))
            {
                return false;
            }

            var textInfo = _config.UITexts[key];
            if (textInfo.Status != status)
            {
                textInfo.Status = status;
                textInfo.LastUpdated = DateTime.UtcNow;
                _config.LastExtraction = DateTime.UtcNow;
                return true;
            }

            return false;
        }

        /// <summary>
        /// Updates only the status of an ExtractedTextInfo in the config without saving to disk
        /// </summary>
        public bool UpdateExtractedTextStatus(string key, TextStatus status)
        {
            if (_config.ExtractedTexts == null || !_config.ExtractedTexts.ContainsKey(key))
            {
                return false;
            }

            var textInfo = _config.ExtractedTexts[key];
            if (textInfo.Status != status)
            {
                textInfo.Status = status;
                textInfo.LastUpdated = DateTime.UtcNow;
                _config.LastExtraction = DateTime.UtcNow;
                return true;
            }

            return false;
        }

        public bool SetUpdatedColumn(string key, int column)
        {
            if (_config.ExtractedTexts == null || !_config.ExtractedTexts.ContainsKey(key))
            {
                return false;
            }

            var textInfo = _config.ExtractedTexts[key];
            if (textInfo.Column != column)
            {
                textInfo.UpdatedColumn = column;
                textInfo.LastUpdated = DateTime.UtcNow;
                _config.LastExtraction = DateTime.UtcNow;
                return true;
            }

            return false;
        }

        public bool SetUpdatedLineNumber(string key, int lineNumber)
        {
            if (_config.ExtractedTexts == null || !_config.ExtractedTexts.ContainsKey(key))
            {
                return false;
            }
            var textInfo = _config.ExtractedTexts[key];
            if (textInfo.LineNumber != lineNumber)
            {
                textInfo.UpdatedLineNumber = lineNumber;
                textInfo.LastUpdated = DateTime.UtcNow;
                _config.LastExtraction = DateTime.UtcNow;
                return true;
            }

            return false;
        }

        /// <summary>
        /// Gets the status of a UITextInfo from the config
        /// </summary>
        public TextStatus GetUITextStatus(string key)
        {
            if (_config.UITexts != null && _config.UITexts.TryGetValue(key, out var textInfo))
            {
                return textInfo.Status;
            }

            return TextStatus.Open;
        }

        /// <summary>
        /// Gets the status of an ExtractedTextInfo from the config
        /// </summary>
        public TextStatus GetExtractedTextStatus(string key)
        {
            if (_config.ExtractedTexts != null && _config.ExtractedTexts.TryGetValue(key, out var textInfo))
            {
                return textInfo.Status;
            }

            return TextStatus.Open;
        }

        /// <summary>
        /// Gets the ExtractedTextInfo from the config
        /// </summary>
        public ExtractedTextInfo GetExtractedText(string key)
        {
            if (_config.ExtractedTexts != null && _config.ExtractedTexts.TryGetValue(key, out var textInfo))
            {
                return textInfo;
            }

            return null;
        }

        /// <summary>
        /// Adds extracted texts to the config without saving to disk
        /// </summary>
        public void AddExtractedTextsWithoutSaving(IEnumerable<ReplacementRecord> replacements)
        {
            foreach (ReplacementRecord replacement in replacements)
            {
                ExtractedTextInfo extractedText = new ExtractedTextInfo
                {
                    FilePath = LocalizationUtils.GetRelativePath(replacement.FilePath, _projectPath),
                    LineNumber = replacement.LineNumber,
                    Column = replacement.Column,
                    OriginalText = replacement.OriginalValue,
                    Property = replacement.Property,
                    GroupId = replacement.GroupId,
                    TextHash = LocalizationUtils.HashString(replacement.OriginalValue).ToString(),
                    Source = replacement.Source,
                    LastUpdated = DateTime.UtcNow,
                    Notes = new List<string>(replacement.Notes ?? new List<string>()),
                    NeedsTranslation = replacement.NeedsTranslation,
                    Status = replacement.NeedsTranslation ? TextStatus.Open : TextStatus.Rejected,
                    IsDependencyProperty = replacement.IsDependencyProperty,
                    XamlControlType = replacement.ControlType,
                    LangTextID = LocalizationUtils.GenerateXmlExportId(replacement.OriginalValue)
                };

                string key = GenerateVerificationKey(replacement);
                _config.ExtractedTexts[key] = extractedText;
            }

            _config.LastExtraction = DateTime.UtcNow;
        }

        /// <summary>
        /// Updates the notes for a UI text in the configuration
        /// </summary>
        /// <param name="uiTextKey">The key for the UI text</param>
        /// <param name="notes">The notes to save</param>
        /// <returns>True if the UI text was found and updated, otherwise false</returns>
        public bool UpdateUITextNotes(string uiTextKey, string notes)
        {
            if (string.IsNullOrEmpty(uiTextKey) || _config.UITexts == null)
                return false;

            if (_config.UITexts.TryGetValue(uiTextKey, out var uiText))
            {
                uiText.Notes = notes;
                return true;
            }

            return false;
        }

    }

    public static class PathHelpers
    {
        /// <summary>
        /// Returns a relative path from one directory (or file) to another.
        /// </summary>
        public static string GetRelativePath(string basePath, string targetPath)
        {
            if (string.IsNullOrEmpty(basePath)) throw new ArgumentNullException(nameof(basePath));
            if (string.IsNullOrEmpty(targetPath)) throw new ArgumentNullException(nameof(targetPath));

            // Ensure both paths end with a directory separator if they are directories
            basePath = EnsureTrailingSeparator(basePath);
            targetPath = EnsureTrailingSeparator(targetPath);

            var baseUri = new Uri(basePath, UriKind.Absolute);
            var targetUri = new Uri(targetPath, UriKind.Absolute);

            // Make relative
            Uri relativeUri = baseUri.MakeRelativeUri(targetUri);
            string relativePath = Uri.UnescapeDataString(relativeUri.ToString());

            // Replace forward slashes with backslashes
            return relativePath.Replace('/', '\\');
        }

        private static string EnsureTrailingSeparator(string path)
        {
            // If path is not a drive root (e.g. "C:\"), 
            // and doesn't already have a trailing separator, add one.
            if (!Path.HasExtension(path) &&
                !path.EndsWith(Path.DirectorySeparatorChar.ToString(), StringComparison.Ordinal) &&
                !path.EndsWith(Path.AltDirectorySeparatorChar.ToString(), StringComparison.Ordinal))
            {
                path += Path.DirectorySeparatorChar;
            }
            return path;
        }
    }
}