/*
 * Copyright 2021-present Alcon. All rights reserved.
 * Alcon proprietary/confidential.
 * Use is subject to license terms.
 */

using NUnit.Framework;
using System;
using System.IO;
using System.Linq;

namespace LocalizationLib.Tests;

[TestFixture]
public class XamlTextToCodeBehindProcessorTests
{
    private string _tempFolder;

    [SetUp]
    public void SetUp()
    {
        // Create a unique temporary folder for the tests
        _tempFolder = Path.Combine(Path.GetTempPath(), "XamlTextToCodeBehindProcessorTests_" + Guid.NewGuid());
        Directory.CreateDirectory(_tempFolder);
    }

    [TearDown]
    public void TearDown()
    {
        // Clean up the temporary folder after tests
        if (Directory.Exists(_tempFolder))
        {
            Directory.Delete(_tempFolder, true);
        }
    }

    [Test]
    public void ProcessFile_NoNonDependencyProperties_NoChanges()
    {
        // Arrange
        string xamlContent = @"<UserControl xmlns=""http://schemas.microsoft.com/winfx/2006/xaml/presentation""
    xmlns:x=""http://schemas.microsoft.com/winfx/2006/xaml"">
    <Grid>
        <Button Content=""Click Me"" />
        <TextBlock Text=""Hello World"" />
    </Grid>
</UserControl>";

        string codeBehindContent = @"using System.Windows.Controls;

namespace TestNamespace
{
    public partial class TestControl : UserControl
    {
        public TestControl()
        {
            InitializeComponent();
        }
    }
}";

        string xamlPath = Path.Combine(_tempFolder, "TestControl.xaml");
        string codeBehindPath = Path.Combine(_tempFolder, "TestControl.xaml.cs");
        File.WriteAllText(xamlPath, xamlContent);
        File.WriteAllText(codeBehindPath, codeBehindContent);

        var processor = new XamlTextToCodeBehindProcessor();

        // Act
        var result = processor.ProcessFile(xamlPath);

        // Assert
        Assert.IsFalse(result.Changed);
        Assert.IsEmpty(result.Replacements);
        Assert.AreEqual(xamlContent, File.ReadAllText(xamlPath));
        Assert.AreEqual(codeBehindContent, File.ReadAllText(codeBehindPath));
    }

    [Test]
    public void ProcessFile_NonDependencyProperty_MovesToCodeBehind()
    {
        // Arrange
        string xamlContent = @"<UserControl xmlns=""http://schemas.microsoft.com/winfx/2006/xaml/presentation""
    xmlns:x=""http://schemas.microsoft.com/winfx/2006/xaml""
    xmlns:UiWidgetMeasurement=""clr-namespace:Alcon.UiWidgetMeasurement;assembly=Alcon.UiWidgetMeasurement"">
    <Grid>
        <UiWidgetMeasurement:RefractionControl 
            Title=""PreOp Phakic Refraction""
            DataContext=""{Binding PreOp.NonPhakicEyeData.PhakicPrescription}"" />
    </Grid>
</UserControl>";

        string codeBehindContent = @"using System.Windows.Controls;

namespace TestNamespace
{
    public partial class TestControl : UserControl
    {
        public TestControl()
        {
            InitializeComponent();
        }
    }
}";

        string xamlPath = Path.Combine(_tempFolder, "TestControl.xaml");
        string codeBehindPath = Path.Combine(_tempFolder, "TestControl.xaml.cs");
        File.WriteAllText(xamlPath, xamlContent);
        File.WriteAllText(codeBehindPath, codeBehindContent);

        var processor = new XamlTextToCodeBehindProcessor();

        // Act
        var result = processor.ProcessFile(xamlPath);

        // Assert
        Assert.IsTrue(result.Changed);
        Assert.AreEqual(1, result.Replacements.Count);
        Assert.IsFalse(result.Replacements[0].IsDependencyProperty);
        Assert.AreEqual("Title", result.Replacements[0].Property);
        Assert.AreEqual("PreOp Phakic Refraction", result.Replacements[0].OriginalValue);

        // Verify XAML changes
        var updatedXaml = File.ReadAllText(xamlPath);
        Assert.That(updatedXaml, Does.Not.Contain("Title=\"PreOp Phakic Refraction\""));
        Assert.That(updatedXaml, Does.Contain("x:Name="));

        // Verify code-behind changes
        var updatedCodeBehind = File.ReadAllText(codeBehindPath);
        Assert.That(updatedCodeBehind, Does.Contain("autoGen_RefractionControl_"));
        Assert.That(updatedCodeBehind, Does.Contain(".Title = \"PreOp Phakic Refraction\";"));
    }

    [Test]
    public void ProcessFile_MultipleNonDependencyProperties_MovesAllToCodeBehind()
    {
        // Arrange
        string xamlContent = @"<UserControl xmlns=""http://schemas.microsoft.com/winfx/2006/xaml/presentation""
    xmlns:x=""http://schemas.microsoft.com/winfx/2006/xaml""
    xmlns:UiWidgetMeasurement=""clr-namespace:Alcon.UiWidgetMeasurement;assembly=Alcon.UiWidgetMeasurement"">
    <Grid>
        <UiWidgetMeasurement:RefractionControl 
            Title=""PreOp Phakic Refraction""
            Header=""Custom Text""
            DataContext=""{Binding PreOp.NonPhakicEyeData.PhakicPrescription}"" />
    </Grid>
</UserControl>";

        string codeBehindContent = @"using System.Windows.Controls;

namespace TestNamespace
{
    public partial class TestControl : UserControl
    {
        public TestControl()
        {
            InitializeComponent();
        }
    }
}";

        string xamlPath = Path.Combine(_tempFolder, "TestControl.xaml");
        string codeBehindPath = Path.Combine(_tempFolder, "TestControl.xaml.cs");
        File.WriteAllText(xamlPath, xamlContent);
        File.WriteAllText(codeBehindPath, codeBehindContent);

        var processor = new XamlTextToCodeBehindProcessor();

        // Act
        var result = processor.ProcessFile(xamlPath);

        // Assert
        Assert.IsTrue(result.Changed);
        Assert.AreEqual(2, result.Replacements.Count);
        Assert.IsTrue(result.Replacements.All(r => !r.IsDependencyProperty));

        // Verify XAML changes
        var updatedXaml = File.ReadAllText(xamlPath);
        Assert.That(updatedXaml, Does.Not.Contain("Title=\"PreOp Phakic Refraction\""));
        Assert.That(updatedXaml, Does.Not.Contain("Header=\"Custom Text\""));
        Assert.That(updatedXaml, Does.Contain("x:Name="));

        // Verify code-behind changes
        var updatedCodeBehind = File.ReadAllText(codeBehindPath);
        Assert.That(updatedCodeBehind, Does.Contain("autoGen_RefractionControl_"));
        Assert.That(updatedCodeBehind, Does.Contain(".Title = \"PreOp Phakic Refraction\";"));
        Assert.That(updatedCodeBehind, Does.Contain(".Header = \"Custom Text\";"));
    }

    [Test]
    public void ProcessFile_ExistingElementName_PreservesName()
    {
        // Arrange
        string xamlContent = @"<UserControl xmlns=""http://schemas.microsoft.com/winfx/2006/xaml/presentation""
    xmlns:x=""http://schemas.microsoft.com/winfx/2006/xaml""
    xmlns:UiWidgetMeasurement=""clr-namespace:Alcon.UiWidgetMeasurement;assembly=Alcon.UiWidgetMeasurement"">
    <Grid>
        <UiWidgetMeasurement:RefractionControl 
            x:Name=""myRefractionControl""
            Title=""PreOp Phakic Refraction""
            DataContext=""{Binding PreOp.NonPhakicEyeData.PhakicPrescription}"" />
    </Grid>
</UserControl>";

        string codeBehindContent = @"using System.Windows.Controls;

namespace TestNamespace
{
    public partial class TestControl : UserControl
    {
        public TestControl()
        {
            InitializeComponent();
        }
    }
}";

        string xamlPath = Path.Combine(_tempFolder, "TestControl.xaml");
        string codeBehindPath = Path.Combine(_tempFolder, "TestControl.xaml.cs");
        File.WriteAllText(xamlPath, xamlContent);
        File.WriteAllText(codeBehindPath, codeBehindContent);

        var processor = new XamlTextToCodeBehindProcessor();

        // Act
        var result = processor.ProcessFile(xamlPath);

        // Assert
        Assert.IsTrue(result.Changed);
        Assert.AreEqual(1, result.Replacements.Count);

        // Verify XAML changes
        var updatedXaml = File.ReadAllText(xamlPath);
        Assert.That(updatedXaml, Does.Not.Contain("Title=\"PreOp Phakic Refraction\""));
        Assert.That(updatedXaml, Does.Contain("x:Name=\"myRefractionControl\""));

        // Verify code-behind changes
        var updatedCodeBehind = File.ReadAllText(codeBehindPath);
        Assert.That(updatedCodeBehind, Does.Contain("myRefractionControl.Title = \"PreOp Phakic Refraction\";"));
    }

    [Test]
    public void ProcessFile_NoCodeBehindFile_ThrowsException()
    {
        // Arrange
        string xamlContent = @"<UserControl xmlns=""http://schemas.microsoft.com/winfx/2006/xaml/presentation""
    xmlns:x=""http://schemas.microsoft.com/winfx/2006/xaml""
    xmlns:UiWidgetMeasurement=""clr-namespace:Alcon.UiWidgetMeasurement;assembly=Alcon.UiWidgetMeasurement"">
    <Grid>
        <UiWidgetMeasurement:RefractionControl 
            Title=""PreOp Phakic Refraction"" />
    </Grid>
</UserControl>";

        string xamlPath = Path.Combine(_tempFolder, "TestControl.xaml");
        File.WriteAllText(xamlPath, xamlContent);

        var processor = new XamlTextToCodeBehindProcessor();

        // Act & Assert
        var ex = Assert.Throws<FileNotFoundException>(() => processor.ProcessFile(xamlPath));
        Assert.That(ex.Message, Does.Contain("Code-behind file not found"));
    }

    [Test]
    public void ProcessFile_NoInitializeComponent_ThrowsException()
    {
        // Arrange
        string xamlContent = @"<UserControl xmlns=""http://schemas.microsoft.com/winfx/2006/xaml/presentation""
    xmlns:x=""http://schemas.microsoft.com/winfx/2006/xaml""
    xmlns:UiWidgetMeasurement=""clr-namespace:Alcon.UiWidgetMeasurement;assembly=Alcon.UiWidgetMeasurement"">
    <Grid>
        <UiWidgetMeasurement:RefractionControl 
            Title=""PreOp Phakic Refraction"" />
    </Grid>
</UserControl>";

        string codeBehindContent = @"using System.Windows.Controls;

namespace TestNamespace
{
    public partial class TestControl : UserControl
    {
        public TestControl()
        {
            // No InitializeComponent call
        }
    }
}";

        string xamlPath = Path.Combine(_tempFolder, "TestControl.xaml");
        string codeBehindPath = Path.Combine(_tempFolder, "TestControl.xaml.cs");
        File.WriteAllText(xamlPath, xamlContent);
        File.WriteAllText(codeBehindPath, codeBehindContent);

        var processor = new XamlTextToCodeBehindProcessor();

        // Act & Assert
        var ex = Assert.Throws<InvalidOperationException>(() => processor.ProcessFile(xamlPath));
        Assert.That(ex.Message, Does.Contain("InitializeComponent method not found"));
    }

    [Ignore("This requirement is ignored for now")]
    public void ProcessFile_PreservesXamlFormatting()
    {
        // Arrange
        string xamlContent = @"<UserControl xmlns=""http://schemas.microsoft.com/winfx/2006/xaml/presentation""
    xmlns:x=""http://schemas.microsoft.com/winfx/2006/xaml""
    xmlns:UiWidgetMeasurement=""clr-namespace:Alcon.UiWidgetMeasurement;assembly=Alcon.UiWidgetMeasurement"">
    <Grid>
        <UiWidgetMeasurement:RefractionControl 
            Title=""PreOp Phakic Refraction""
            DataContext=""{Binding PreOp.NonPhakicEyeData.PhakicPrescription}"" />
    </Grid>
</UserControl>";

        string codeBehindContent = @"using System.Windows.Controls;

namespace TestNamespace
{
    public partial class TestControl : UserControl
    {
        public TestControl()
        {
            InitializeComponent();
        }
    }
}";

        string xamlPath = Path.Combine(_tempFolder, "TestControl.xaml");
        string codeBehindPath = Path.Combine(_tempFolder, "TestControl.xaml.cs");
        File.WriteAllText(xamlPath, xamlContent);
        File.WriteAllText(codeBehindPath, codeBehindContent);

        var processor = new XamlTextToCodeBehindProcessor();

        // Act
        var result = processor.ProcessFile(xamlPath);

        // Assert
        Assert.IsTrue(result.Changed);

        // Verify XAML formatting is preserved
        var updatedXaml = File.ReadAllText(xamlPath);
        Assert.That(updatedXaml, Does.Contain("    xmlns:x=\"http://schemas.microsoft.com/winfx/2006/xaml\""));
        Assert.That(updatedXaml, Does.Contain("    xmlns:UiWidgetMeasurement=\"clr-namespace:Alcon.UiWidgetMeasurement;assembly=Alcon.UiWidgetMeasurement\">"));
        Assert.That(updatedXaml, Does.Contain("    <Grid>"));
        Assert.That(updatedXaml, Does.Contain("        <UiWidgetMeasurement:RefractionControl"));
    }

    [Test]
    public void ProcessFile_RootElementProperty_MovesToCodeBehind()
    {
        // Arrange
        string xamlContent = @"<Window x:Class=""TestNamespace.TestWindow""
    xmlns=""http://schemas.microsoft.com/winfx/2006/xaml/presentation""
    xmlns:x=""http://schemas.microsoft.com/winfx/2006/xaml""
    Title=""Test Window Title""
    WindowStyle=""None"">
    <Grid>
        <Button Content=""Click Me"" />
    </Grid>
</Window>";

        string codeBehindContent = @"using System.Windows;

namespace TestNamespace
{
    public partial class TestWindow : Window
    {
        public TestWindow()
        {
            InitializeComponent();
        }
    }
}";

        string xamlPath = Path.Combine(_tempFolder, "TestWindow.xaml");
        string codeBehindPath = Path.Combine(_tempFolder, "TestWindow.xaml.cs");
        File.WriteAllText(xamlPath, xamlContent);
        File.WriteAllText(codeBehindPath, codeBehindContent);

        var processor = new XamlTextToCodeBehindProcessor();

        // Act
        var result = processor.ProcessFile(xamlPath);

        // Assert
        Assert.IsTrue(result.Changed);
        Assert.AreEqual(1, result.Replacements.Count);
        Assert.IsTrue(result.Replacements[0].IsRootElement);
        Assert.AreEqual("Title", result.Replacements[0].Property);
        Assert.AreEqual("Test Window Title", result.Replacements[0].OriginalValue);

        // Verify XAML changes
        var updatedXaml = File.ReadAllText(xamlPath);
        Assert.That(updatedXaml, Does.Not.Contain("Title=\"Test Window Title\""));

        // Verify code-behind changes
        var updatedCodeBehind = File.ReadAllText(codeBehindPath);
        Assert.That(updatedCodeBehind, Does.Contain("Title = \"Test Window Title\";"));
    }

    [Test]
    public void ProcessFile_MultipleRootElementProperties_MovesAllToCodeBehind()
    {
        // Arrange
        string xamlContent = @"<Window x:Class=""TestNamespace.TestWindow""
    xmlns=""http://schemas.microsoft.com/winfx/2006/xaml/presentation""
    xmlns:x=""http://schemas.microsoft.com/winfx/2006/xaml""
    Title=""Test Window Title""
    WindowStyle=""None""
    ResizeMode=""NoResize"">
    <Grid>
        <Button Content=""Click Me"" />
    </Grid>
</Window>";

        string codeBehindContent = @"using System.Windows;

namespace TestNamespace
{
    public partial class TestWindow : Window
    {
        public TestWindow()
        {
            InitializeComponent();
        }
    }
}";

        string xamlPath = Path.Combine(_tempFolder, "TestWindow.xaml");
        string codeBehindPath = Path.Combine(_tempFolder, "TestWindow.xaml.cs");
        File.WriteAllText(xamlPath, xamlContent);
        File.WriteAllText(codeBehindPath, codeBehindContent);

        var processor = new XamlTextToCodeBehindProcessor();

        // Act
        var result = processor.ProcessFile(xamlPath);

        // Assert
        Assert.IsTrue(result.Changed);
        Assert.AreEqual(1, result.Replacements.Count);
        Assert.IsTrue(result.Replacements.All(r => r.IsRootElement));

        // Verify XAML changes
        var updatedXaml = File.ReadAllText(xamlPath);
        Assert.That(updatedXaml, Does.Not.Contain("Title=\"Test Window Title\""));

        // Verify code-behind changes
        var updatedCodeBehind = File.ReadAllText(codeBehindPath);
        Assert.That(updatedCodeBehind, Does.Contain("Title = \"Test Window Title\";"));
    }
} 