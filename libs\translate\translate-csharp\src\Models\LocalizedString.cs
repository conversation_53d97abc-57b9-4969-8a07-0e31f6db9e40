﻿using System.Collections.Generic;
using System.Xml.Serialization;

namespace Alcon.Interop.Translate.Models;

public class LocalizedString
{
    [XmlAttribute(AttributeName = "id")]
    public string Id { get; set; }

    [XmlAttribute]
    public string Text { get; set; }

    [XmlAttribute]
    public string EnglishText { get; set; }

    [XmlAttribute(AttributeName = "fontclass")]
    public string FontClass { get; set; }

    [XmlIgnore]
    public uint? Width { get; set; }
    [XmlAttribute(AttributeName = "width")]
    public uint WidthSerializable
    {
        get => Width.Value;
        set => Width = value;
    }

    [XmlIgnore]
    public uint? Height { get; set; }
    [XmlAttribute(AttributeName = "height")]
    public uint HeightSerializable
    {
        get => Height.Value;
        set => Height = value;
    }

    [XmlIgnore]
    public bool? CanChangeFont { get; set; }
    [XmlAttribute(AttributeName = "CanChangeFont")]
    public bool CanChangeFontSerializable
    {
        get => CanChangeFont.Value;
        set => CanChangeFont = value;
    }

    [XmlIgnore]
    public bool? Wrap { get; set; }
    [XmlAttribute(AttributeName = "wrap")]
    public bool WrapSerializable
    {
        get => Wrap.Value;
        set => Wrap = value;
    }

    [XmlIgnore]
    public bool IsValid => !string.IsNullOrWhiteSpace(EnglishText) &&
                           !string.IsNullOrWhiteSpace(FontClass) &&
                           Width.HasValue &&
                           Height.HasValue &&
                           CanChangeFont.HasValue &&
                           Wrap.HasValue;

    public bool ShouldSerializeWidthSerializable() => Width.HasValue;
    public bool ShouldSerializeHeightSerializable() => Height.HasValue;
    public bool ShouldSerializeCanChangeFontSerializable() => CanChangeFont.HasValue;
    public bool ShouldSerializeWrapSerializable() => Wrap.HasValue;
}

public class LocalizedStringGroup
{
    [XmlAttribute(AttributeName = "id")]
    public string Name { get; set; }
    [XmlElement("LocalizedString")]
    public List<LocalizedString> Children { get; } = new();
}
