{"Version": 1, "WorkspaceRootPath": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-csharp\\tests\\unit\\build\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{6EDDFEA2-94B0-37C8-B2B4-A6C2FA8142DD}|translate_unit_tests.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-csharp\\tests\\unit\\src\\translatortests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{259ABDC9-F845-31CD-9735-B69A45B30E0A}|translate_csharp\\translate_csharp.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-csharp\\src\\translator.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{6EDDFEA2-94B0-37C8-B2B4-A6C2FA8142DD}|translate_unit_tests.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-csharp\\tests\\unit\\src\\localizeddescriptionattributetests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{259ABDC9-F845-31CD-9735-B69A45B30E0A}|translate_csharp\\translate_csharp.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-csharp\\src\\attributes\\localizeddescriptionattribute.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{259ABDC9-F845-31CD-9735-B69A45B30E0A}|translate_csharp\\translate_csharp.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-csharp\\src\\converters\\translateconverter.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{259ABDC9-F845-31CD-9735-B69A45B30E0A}|translate_csharp\\translate_csharp.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-csharp\\cmakelists.txt||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{6EDDFEA2-94B0-37C8-B2B4-A6C2FA8142DD}|translate_unit_tests.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-csharp\\tests\\unit\\cmakelists.txt||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{259ABDC9-F845-31CD-9735-B69A45B30E0A}|translate_csharp\\translate_csharp.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-csharp\\src\\helper\\translatemetadata.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\AppData\\Local\\SourceServer\\2b7926876cc0be507c49f67f0fd5fadd226b4e7cbf3c1bbb06fc29eeb268a49f\\src\\NUnitFramework\\framework\\Assert.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 1, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{e1b7d1f8-9b3c-49b1-8f4f-bfc63a88835d}"}, {"$type": "Document", "DocumentIndex": 0, "Title": "TranslatorTests.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-csharp\\tests\\unit\\src\\TranslatorTests.cs", "RelativeDocumentMoniker": "..\\src\\TranslatorTests.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-csharp\\tests\\unit\\src\\TranslatorTests.cs", "RelativeToolTip": "..\\src\\TranslatorTests.cs", "ViewState": "AgIAADYAAAAAAAAAAAAgwEwAAAAMAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-01-24T09:02:22.478Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "LocalizedDescriptionAttributeTests.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-csharp\\tests\\unit\\src\\LocalizedDescriptionAttributeTests.cs", "RelativeDocumentMoniker": "..\\src\\LocalizedDescriptionAttributeTests.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-csharp\\tests\\unit\\src\\LocalizedDescriptionAttributeTests.cs", "RelativeToolTip": "..\\src\\LocalizedDescriptionAttributeTests.cs", "ViewState": "AgIAAC4AAAAAAAAAAAAkwDcAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-14T09:30:37.745Z"}, {"$type": "Document", "DocumentIndex": 3, "Title": "LocalizedDescriptionAttribute.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-csharp\\src\\Attributes\\LocalizedDescriptionAttribute.cs", "RelativeDocumentMoniker": "..\\..\\..\\src\\Attributes\\LocalizedDescriptionAttribute.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-csharp\\src\\Attributes\\LocalizedDescriptionAttribute.cs", "RelativeToolTip": "..\\..\\..\\src\\Attributes\\LocalizedDescriptionAttribute.cs", "ViewState": "AgIAADUAAAAAAAAAAAAwwBkAAAArAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-14T09:26:39.472Z"}, {"$type": "Document", "DocumentIndex": 4, "Title": "TranslateConverter.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-csharp\\src\\Converters\\TranslateConverter.cs", "RelativeDocumentMoniker": "..\\..\\..\\src\\Converters\\TranslateConverter.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-csharp\\src\\Converters\\TranslateConverter.cs", "RelativeToolTip": "..\\..\\..\\src\\Converters\\TranslateConverter.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAwAAAAzAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-14T09:57:12.999Z"}, {"$type": "Document", "DocumentIndex": 5, "Title": "CMakeLists.txt", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-csharp\\CMakeLists.txt", "RelativeDocumentMoniker": "..\\..\\..\\CMakeLists.txt", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-csharp\\CMakeLists.txt", "RelativeToolTip": "..\\..\\..\\CMakeLists.txt", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003109|", "WhenOpened": "2025-04-14T09:25:13.013Z"}, {"$type": "Document", "DocumentIndex": 1, "Title": "Translator.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-csharp\\src\\Translator.cs", "RelativeDocumentMoniker": "..\\..\\..\\src\\Translator.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-csharp\\src\\Translator.cs", "RelativeToolTip": "..\\..\\..\\src\\Translator.cs", "ViewState": "AgIAAJ8AAAAAAAAAAADwv7cAAAAEAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-03-25T09:39:47.469Z"}, {"$type": "Document", "DocumentIndex": 7, "Title": "TranslateMetadata.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-csharp\\src\\Helper\\TranslateMetadata.cs", "RelativeDocumentMoniker": "..\\..\\..\\src\\Helper\\TranslateMetadata.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-csharp\\src\\Helper\\TranslateMetadata.cs", "RelativeToolTip": "..\\..\\..\\src\\Helper\\TranslateMetadata.cs", "ViewState": "AgIAABUAAAAAAAAAAAAmwCwAAAAEAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-01-24T09:58:30.392Z"}, {"$type": "Document", "DocumentIndex": 6, "Title": "CMakeLists.txt", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-csharp\\tests\\unit\\CMakeLists.txt", "RelativeDocumentMoniker": "..\\CMakeLists.txt", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-csharp\\tests\\unit\\CMakeLists.txt", "RelativeToolTip": "..\\CMakeLists.txt", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003109|", "WhenOpened": "2025-03-18T03:50:00.659Z"}, {"$type": "Document", "DocumentIndex": 8, "Title": "Assert.cs", "DocumentMoniker": "C:\\Users\\<USER>\\AppData\\Local\\SourceServer\\2b7926876cc0be507c49f67f0fd5fadd226b4e7cbf3c1bbb06fc29eeb268a49f\\src\\NUnitFramework\\framework\\Assert.cs", "ToolTip": "C:\\Users\\<USER>\\AppData\\Local\\SourceServer\\2b7926876cc0be507c49f67f0fd5fadd226b4e7cbf3c1bbb06fc29eeb268a49f\\src\\NUnitFramework\\framework\\Assert.cs", "ViewState": "AgIAAIIBAAAAAAAAAADwv5QBAAAQAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-01-24T09:50:11.927Z"}]}]}]}