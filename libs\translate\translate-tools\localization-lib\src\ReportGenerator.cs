/*
 * Copyright 2021-present Alcon. All rights reserved.
 * Alcon proprietary/confidential.
 * Use is subject to license terms.
 */

using System;
using System.Collections.Generic;
using System.IO;

namespace LocalizationLib;

/// <summary>
/// Generates a report file summarizing the changes (or findings) made during extraction.
/// In find-only mode, the report lists only the cases where a translation text is missing.
/// </summary>
public class ReportGenerator
{
    #region PUBLIC METHODS
    /// <summary>
    /// Generates the final report.
    /// </summary>
    /// <param name="reportFile">Path to the report file.</param>
    /// <param name="fileResults">List of per-file processing results.</param>
    /// <param name="findOnly">If true, report only findings of missing translation texts.</param>
    public void GenerateReport(string reportFile, List<FileProcessingResult> fileResults, bool findOnly = true)
    {
        using var sw = new StreamWriter(reportFile);
        sw.WriteLine("=== Localization Tool Report ===");
        sw.WriteLine($"Date: {DateTime.Now}");
        if (findOnly)
        {
            sw.WriteLine("Mode: FindOnly (No files modified; only reporting cases where translation text is missing)");
        }
        else
        {
            sw.WriteLine("Mode: Replace (Files modified if necessary)");
        }
        sw.WriteLine($"Files Processed: {fileResults.Count}");
        sw.WriteLine();

        Console.WriteLine("[INFO] ---- Final Report ----");
        int totalFindings = 0;

        // In find-only mode, we only report on the findings.
        foreach (var result in fileResults)
        {
            // Only output a file if there are replacement findings.
            if (result.Replacements.Count == 0)
                continue;

            string fileName = Path.GetFileName(result.FilePath);
            if (findOnly)
            {
                sw.WriteLine($"File: {fileName}");
                Console.WriteLine($"File: {fileName}");
                foreach (var rep in result.Replacements)
                {
                    // In find-only mode, we simply report that translation text is missing.
                    
                    sw.WriteLine($"{result.FilePath}({rep.LineNumber},{rep.Column}): Missing translation for \"{rep.OriginalValue}\" (Property: {rep.Property}, found at Line {rep.LineNumber}, Column {rep.Column})");
                    Console.WriteLine($"{result.FilePath}({rep.LineNumber},{rep.Column}): Missing translation for \"{rep.OriginalValue}\" (Property: {rep.Property}, found at Line {rep.LineNumber}, Column {rep.Column})");
                    foreach (var note in rep.Notes)
                    {
                        sw.WriteLine($"   [Note] {note}");
                        Console.WriteLine($"   [Note] {note}");
                    }
                    totalFindings++;
                }
            }
            else
            {
                // In replace mode, report as before.
                string status = result.Changed ? "MODIFIED" : "NO CHANGE";
                sw.WriteLine($"File: {fileName}  [{status}]");
                Console.WriteLine($"File: {fileName}  [{status}]");

                foreach (var rep in result.Replacements)
                {
                    sw.WriteLine($"   - {rep.Property}: \"{rep.OriginalValue}\" => \"{rep.NewKey}\" (Line {rep.LineNumber}, Column {rep.Column})");
                    Console.WriteLine($"   - {rep.Property}: '{rep.OriginalValue}' => '{rep.NewKey}' (Line {rep.LineNumber}, Column {rep.Column})");
                    foreach (var note in rep.Notes)
                    {
                        sw.WriteLine($"   [Note] {note}");
                        Console.WriteLine($"   [Note] {note}");
                    }
                    totalFindings++;
                }
                foreach (string note in result.Notes)
                {
                    sw.WriteLine($"   [Note] {note}");
                    Console.WriteLine($"   [Note] {note}");
                }
            }
            sw.WriteLine();
            Console.WriteLine();
        }

        if (findOnly)
        {
            sw.WriteLine($"Total findings (missing translation texts): {totalFindings}");
            Console.WriteLine($"[INFO] Total findings (missing translation texts): {totalFindings}");
        }
        else
        {
            sw.WriteLine($"Total replacements: {totalFindings}");
            Console.WriteLine($"[INFO] Total replacements: {totalFindings}");
        }
        sw.Flush();

        Console.WriteLine($"[INFO] Report saved to {reportFile}");
    }
    
    #endregion
}
