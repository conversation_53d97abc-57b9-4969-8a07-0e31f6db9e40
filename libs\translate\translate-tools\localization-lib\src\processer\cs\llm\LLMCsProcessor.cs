/*
 * Copyright 2021-present Alcon. All rights reserved.
 * Alcon proprietary/confidential.
 * Use is subject to license terms.
 */

using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;

namespace LocalizationLib;

public class LLMCsProcessor : ICsProcessor
{
    private readonly ILlmClient _llmClient;

    // Optional: read from config, or set default chunk size
    private readonly int _chunkSize = 300;
    private readonly bool _useChunkApproach = false;

    public List<ReplacementRecord> AllReplacementRecords { get; } = new List<ReplacementRecord>();

    public LLMCsProcessor(ILlmClient llmClient, LLMConfig config = null)
    {
        _llmClient = llmClient;

        // If config has fields for chunk approach:
        if (config?.localLlmProviderConfig != null)
        {
            // Suppose we store a chunkSize in config, or a 'useChunking' bool
            // e.g.:
            if (config.localLlmProviderConfig.chunkSize > 0)
            { 
                _chunkSize = config.localLlmProviderConfig.chunkSize; 
            }

            if (config.localLlmProviderConfig.useChunking) 
            { 
                _useChunkApproach = true; 
            }
        }
    }

    /// <summary>
    /// 1) Reads the file content
    /// 2) Possibly chunk the file (if _useChunkApproach) or skip if small
    /// 3) Merge partial results
    /// 4) Fix lineNumber/column references by matching lineText to real lines
    /// </summary>
    public FileProcessingResult ProcessCsFile(string csPath, string groupId)
    {
        string originalContent = File.ReadAllText(csPath);
        var fileName = Path.GetFileName(csPath);

        // chunk logic
        var lines = originalContent.Split('\n');
        List<ReplacementRecord> discovered;
        if (_useChunkApproach && lines.Length > _chunkSize * 1.5)
        {
            discovered = ProcessFileInChunks(lines, fileName, csPath, groupId);
        }
        else
        {
            discovered = ProcessFileSingleChunk(lines, fileName, csPath, groupId);
        }

        if (discovered.Count == 0)
        {
            Console.WriteLine($"No user-facing text found in {csPath}.");
            return new FileProcessingResult { FilePath = csPath };
        }

        // fix line/col
        CsProcessorHelpers.FixLineAndColumn(discovered, lines);

        // store globally
        AllReplacementRecords.AddRange(discovered);

        var fileResult = new FileProcessingResult
        {
            FilePath = csPath,
            Changed = false
        };
        fileResult.Replacements.AddRange(discovered);

        return fileResult;
    }

    private List<ReplacementRecord> ProcessFileSingleChunk(
        string[] lines,
        string fileName,
        string filePath,
        string groupId)
    {
        Console.WriteLine($"[INFO] Processing file {fileName} as single chunk. lines={lines.Length}");

        // build line-indexed code
        var lineIndexedCode = CsProcessorHelpers.BuildLineIndexedCode(lines);

        // call ILlmClient
        var recs = _llmClient.DiscoverAndProposeReplacements(lineIndexedCode, groupId, fileName, filePath);
        Console.WriteLine($"[INFO] Found {recs.Count} items in single-chunk for {fileName}");
        return recs;
    }

    private List<ReplacementRecord> ProcessFileInChunks(
        string[] lines,
        string fileName,
        string filePath,
        string groupId)
    {
        Console.WriteLine($"[INFO] Processing file {fileName} in chunk mode. lines={lines.Length}, chunkSize={_chunkSize}");

        var finalRecords = new List<ReplacementRecord>();
        int total = lines.Length;
        int chunkIndex = 0;

        for (int start = 0; start < total; start += _chunkSize)
        {
            int end = Math.Min(start + _chunkSize, total);
            var chunkLines = lines.Skip(start).Take(end - start).ToArray();

            // build line-indexed code for chunk
            var lineIndexedChunk = CsProcessorHelpers.BuildLineIndexedCode(chunkLines);

            Console.WriteLine($"[INFO]   => Processing chunk #{chunkIndex} from line {start + 1} to {end} ...");
            var partial = _llmClient.DiscoverAndProposeReplacements(
                lineIndexedChunk,
                groupId,
                $"{fileName}-chunk{chunkIndex}",
                filePath
            );
            Console.WriteLine($"[INFO]   => Found {partial.Count} user-facing items in chunk #{chunkIndex}");

            finalRecords.AddRange(partial);
            chunkIndex++;
        }

        Console.WriteLine($"[INFO] Merged total {finalRecords.Count} items from all chunks for {fileName}.");
        return finalRecords;
    }
}
