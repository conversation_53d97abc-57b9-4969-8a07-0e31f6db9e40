# Language File Merger Tool

## Overview
The **Language File Merger Tool** is a commant-line tool that is able to update an existing language file with a new one by merging its content.

## Features
- **Automatic merge for XML files, as long as the element/attribute is missing at all from the base XML file**
- **Maintains the uniqueness of the XML elements if they have "name" or "id" attribute assigned**

## Installation
Perequisites:
- .NET 6.0+ installed 
- CMake for build

### Build Process - with CMake
```sh
# Go into the project directory
cd translate-tools/localization-filemerger

# Create the build directory
mkdir build && cd build

# Generate build files with CMake
cmake ..

# Build the project
cmake --build .
```

## Usage
Run the tool with one of the following commands:
```sh
localization_filemerger.exe -i <BaseFileLanguage> <UpdatedLanguageFile> -o <OutputFile>
localization_filemerger.exe -i <BaseFileLanguage> <UpdatedLanguageFile>
localization_filemerger.exe -o <OutputFile> -i <BaseFileLanguage> <UpdatedLanguageFile>
localization_filemerger.exe -o <OutputFile> <BaseFileLanguage> <UpdatedLanguageFile>
localization_filemerger.exe -o <OutputFile> <BaseFileLanguage> <UpdatedLanguageFile> -f
```

### Parameters
| Short | Long           | Required | Description |
|-------|----------------|----------|-------------|
| `-i`  | `--input`      | ✅ Yes   | Path to the XML base language file and the updated language file.
| `-o`  | `--output`     | ❌ No    | Path to the XML output file. Default value: merged + <BaseFileLanguage_extension>.
| `-f`  | `--forceMerge` | ❌ No    | Bool value that forces to merge the differences from the updated file into the base file if it set to true

## How it works
### 1. Validates file extensions and extracts it	
- The tool can be extended to work for other file types.

### 2. Processes line numbers for <BaseFileLanguage> and <UpdatedLanguageFile>
- Creates a dictionary<string, int> that maps every element from XML file with the corresponding line number.
This is used for warning messages.

### 3. Merges `<UpdatedLanguageFile>` into the `<BaseFileLanguage>`
- Checkes recursively every element from the `<UpdatedLanguageFile>` to see if it is present in the `<BaseFileLanguage>`
- The element is found by its unique identifier ("name" or "id"). If the unique identifier isn't presented, the element is found after its name.
- If the element is missing at all, it is added as new element.
- If the element is present, the tool checks for its attributes. 
	- If the attribute is missing, it will be added.
	- If the attribute's value is different and forceMerge is not set, the user is informed only with an warning.
	Example warning message:
	`[WARNING] Merge couldn't be done automatically! Different attribute value:
  		(FacilityWidget_ro.xml, 12): weight="NonRegular"
  		(FacilityWidget_ro-upd.xml,4): weight="Regular"`
	- If the forceMerge option is selected the merge will be done with the following message
	`[WARNING] Changed attribute value:
  (FacilityWidget_ro.xml, 5): Text="?Practice?" into Text="Practica" from (FacilityWidget_ro-upd.xml,18)"`
	Example info messages:
	1. `[INFO] Added new element : <group id="CapellaWidget.NewGroup">.`
	2. `[INFO] [INFO] Updated element on line 16: <fontclass name="font15" /> to include attribute face="Roboto"..`
	
### 4. Saves the changes into the <OutputFile>