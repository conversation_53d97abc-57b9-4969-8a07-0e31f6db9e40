<UserControl x:Class="TRICS.Planner.UI.Widget.Measurement.KeratometryControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:GeneralConverter="clr-namespace:TRICS.Planner.UI.General.Converter;assembly=TRICSPlannerUIGeneral"
             xmlns:GeneralWidget="clr-namespace:TRICS.Planner.UI.General.Widget;assembly=TRICSPlannerUIGeneral"
             mc:Ignorable="d"
        xmlns:trConv="clr-namespace:Alcon.Interop.Translate;assembly=translate_csharp">

    <UserControl.Resources>
        <GeneralConverter:BoolToVisibilityConverter x:Key="BoolToVisibilityConverter" />
        <GeneralConverter:BoolToInvisibilityConverter x:Key="BoolToInvisibilityConverter" />
        <GeneralConverter:InverseBooleanConverter x:Key="InverseBooleanConverter" />
        <GeneralConverter:AngleToStrConverter x:Key="AngleToStrConverter" />
            <trConv:TranslateConverter x:Key="TranslateConverter"/>
        </UserControl.Resources>

    <!-- CONTENT -->
    <Grid VerticalAlignment="Top"
          x:Name="MainPanel">
        <Grid.RowDefinitions>
            <RowDefinition Height="40" />
            <RowDefinition Height="55" />
        </Grid.RowDefinitions>

        <!-- Title -->
        <TextBlock Grid.Row="0" Style="{DynamicResource EyeEditorSubsectionTitleBlockStyle}"
                   x:Name="KeratometryTitle"
                   Text="Keratometry" />

        <!-- Open Keratometry Viewer Button -->
        <Grid Grid.Row="0"
              HorizontalAlignment="Right"
              Margin="0,0,10,0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition x:Name="CollapsibleIconColumn" />
            </Grid.ColumnDefinitions>

            <Button Grid.Row="0" Style="{StaticResource HighlightedFlatButton}"
                    Width="{Binding ElementName=IconContent, Path=Width}" VerticalAlignment="Center"
                    Background="Transparent"
                    x:Name="theEye" x:FieldModifier="public"
                    Click="DoShowKeratometry">
                <Grid Margin="4,0"
                      x:Name="IconContent">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <!-- Label -->
                    <TextBlock Grid.Column="0" Style="{StaticResource TabelHighlightedTextBlockStyle}"
                               Margin="0,0,8,0"
                               Text="{Binding Converter={StaticResource TranslateConverter}, ConverterParameter='id:-1619344833|en:Show Reflections'}" FontSize="13" FontWeight="Bold" />

                    <!-- Icon -->
                    <Path Grid.Column="1" Style="{StaticResource VPPathStyle}" Fill="{DynamicResource PreOpActiveTextColor}"
                          Width="20" Height="12"
                          Data="{DynamicResource EyePath}" RenderTransformOrigin="0.5,0.5" Stretch="Fill" />
                </Grid>

            </Button>
        </Grid>

        <!-- Label + Textbox instances -->
        <Grid Grid.Row="1"
              VerticalAlignment="Center">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="1*" />
                <ColumnDefinition Width="1*" />
                <ColumnDefinition Width="1*" />
                <ColumnDefinition Width="1*" />
                <ColumnDefinition Width="1*"
                                  x:Name="CollapsibleUnitsColumn" />
            </Grid.ColumnDefinitions>

            <!-- R1 / K1 -->
            <Grid Grid.Column="0">
                <Grid.RowDefinitions>
                    <RowDefinition Height="25" />
                    <RowDefinition Height="30" />
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="1*" />
                    <ColumnDefinition Width="70" />
                    <ColumnDefinition Width="2.2*" />
                </Grid.ColumnDefinitions>

                <!-- Label -->
                <TextBlock Grid.Row="0" Grid.Column="1" Style="{StaticResource TabelNormalTextBlockStyle}"
                           x:Name="R1Label" x:FieldModifier="public"
                           Text="R1"
                           Visibility="{Binding IsChecked, ElementName=MeridianMmRadio, Converter={StaticResource BoolToVisibilityConverter}}" />
                <TextBlock Grid.Row="0" Grid.Column="1" Style="{StaticResource TabelNormalTextBlockStyle}"
                           x:Name="K1Label" x:FieldModifier="public"
                           Text="K1"
                           Visibility="{Binding IsChecked, ElementName=MeridianMmRadio, Converter={StaticResource BoolToInvisibilityConverter}}" />

                <!-- Textbox -->
                <Grid Grid.Row="1" Grid.Column="1"
                      Name="TextBlockGrid_R1K1">
                    <Border Width="65" Height="30" HorizontalAlignment="Center" VerticalAlignment="Center"
                            BorderBrush="{DynamicResource EditBoxDisabledBorderColor}" BorderThickness="1"
                            Background="Transparent" SnapsToDevicePixels="True">
                        <TextBlock Style="{StaticResource TabelNormalTextBlockStyle}"
                                   x:Name="R1TextBlock" x:FieldModifier="public"
                                   Text="{Binding Path=R1, StringFormat=N2, TargetNullValue='n/a'}" FontSize="15" FontWeight="Thin"
                                   Visibility="{Binding IsChecked, ElementName=MeridianMmRadio, Converter={StaticResource BoolToVisibilityConverter}}" />
                    </Border>
                    <Border Width="65" Height="30" HorizontalAlignment="Center" VerticalAlignment="Center"
                            BorderBrush="{DynamicResource EditBoxDisabledBorderColor}" BorderThickness="1"
                            Background="Transparent" SnapsToDevicePixels="True">

                        <TextBlock Style="{StaticResource TabelNormalTextBlockStyle}"
                                   x:Name="K1TextBlock" x:FieldModifier="public"
                                   Text="{Binding Path=K1, StringFormat=N2, TargetNullValue='n/a'}" FontSize="15" FontWeight="Thin"
                                   Visibility="{Binding IsChecked, ElementName=MeridianMmRadio, Converter={StaticResource BoolToInvisibilityConverter}}" />
                    </Border>
                </Grid>
                <Grid Grid.Row="1" Grid.Column="1"
                      Name="EditBoxGrid_R1K1">
                    <GeneralWidget:ValidatedParamEditBox Width="65" HorizontalAlignment="Center" TxtAlignment="Center"
                                                         x:Name="R1EditBox" x:FieldModifier="public"
                                                         Format="N2" HintPath="Flat.Radius.HintMm" ValuePath="R1"
                                                         Visibility="{Binding IsChecked, ElementName=MeridianMmRadio, Converter={StaticResource BoolToVisibilityConverter}}"
                                                         AutomationProperties.AutomationId="Measurement::R1" />
                    <GeneralWidget:ValidatedParamEditBox Width="65" HorizontalAlignment="Center" TxtAlignment="Center"
                                                         x:Name="K1EditBox" x:FieldModifier="public"
                                                         Format="N2" HintPath="Flat.Radius.HintDpt" ValuePath="K1"
                                                         Visibility="{Binding IsChecked, ElementName=MeridianMmRadio, Converter={StaticResource BoolToInvisibilityConverter}}"
                                                         AutomationProperties.AutomationId="Measurement::K1" />
                </Grid>

                <!-- Units -->
                <TextBlock Grid.Row="1" Grid.Column="2" Style="{StaticResource AttachedUnitsTextBlockStyle}"
                           x:Name="R1Units"
                           Text="mm"
                           Visibility="{Binding IsChecked, ElementName=MeridianMmRadio, Converter={StaticResource BoolToVisibilityConverter}}" />
                <TextBlock Grid.Row="1" Grid.Column="2" Style="{StaticResource AttachedUnitsTextBlockStyle}"
                           x:Name="K1Units"
                           Text="D"
                           Visibility="{Binding IsChecked, ElementName=MeridianMmRadio, Converter={StaticResource BoolToInvisibilityConverter}}" />


            </Grid>

            <!-- R2 / K2 -->
            <Grid Grid.Column="1">
                <Grid.RowDefinitions>
                    <RowDefinition Height="25" />
                    <RowDefinition Height="30" />
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="1*" />
                    <ColumnDefinition Width="70" />
                    <ColumnDefinition Width="2.2*" />
                </Grid.ColumnDefinitions>

                <!-- Label -->
                <TextBlock Grid.Row="0" Grid.Column="1" Style="{StaticResource TabelNormalTextBlockStyle}"
                           x:Name="R2Label" x:FieldModifier="public"
                           Text="R2"
                           Visibility="{Binding IsChecked, ElementName=MeridianMmRadio, Converter={StaticResource BoolToVisibilityConverter}}" />
                <TextBlock Grid.Row="0" Grid.Column="1" Style="{StaticResource TabelNormalTextBlockStyle}"
                           x:Name="K2Label" x:FieldModifier="public"
                           Text="K2"
                           Visibility="{Binding IsChecked, ElementName=MeridianMmRadio, Converter={StaticResource BoolToInvisibilityConverter}}" />

                <!-- Textbox -->
                <Grid Grid.Row="1" Grid.Column="1"
                      Name="TextBlockGrid_R2K2">
                    <Border Width="65" Height="30" HorizontalAlignment="Center" VerticalAlignment="Center"
                            BorderBrush="{DynamicResource EditBoxDisabledBorderColor}" BorderThickness="1"
                            Background="Transparent" SnapsToDevicePixels="True">
                        <TextBlock Style="{StaticResource TabelNormalTextBlockStyle}"
                                   x:Name="R2TextBlock" x:FieldModifier="public"
                                   Text="{Binding Path=R2, StringFormat=N2, TargetNullValue='n/a'}" FontSize="15" FontWeight="Thin"
                                   Visibility="{Binding IsChecked, ElementName=MeridianMmRadio, Converter={StaticResource BoolToVisibilityConverter}}" />
                    </Border>
                    <Border Width="65" Height="30" HorizontalAlignment="Center" VerticalAlignment="Center"
                            BorderBrush="{DynamicResource EditBoxDisabledBorderColor}" BorderThickness="1"
                            Background="Transparent" SnapsToDevicePixels="True">

                        <TextBlock Style="{StaticResource TabelNormalTextBlockStyle}"
                                   x:Name="K2TextBlock" x:FieldModifier="public"
                                   Text="{Binding Path=K2, StringFormat=N2, TargetNullValue='n/a'}" FontSize="15" FontWeight="Thin"
                                   Visibility="{Binding IsChecked, ElementName=MeridianMmRadio, Converter={StaticResource BoolToInvisibilityConverter}}" />
                    </Border>
                </Grid>
                <Grid Grid.Row="1" Grid.Column="1"
                      Name="EditBoxGrid_R2K2">
                    <GeneralWidget:ValidatedParamEditBox Width="65" HorizontalAlignment="Center" TxtAlignment="Center"
                                                         x:Name="R2EditBox" x:FieldModifier="public"
                                                         Format="N2" HintPath="Steep.Radius.HintMm" ValuePath="R2"
                                                         Visibility="{Binding IsChecked, ElementName=MeridianMmRadio, Converter={StaticResource BoolToVisibilityConverter}}"
                                                         AutomationProperties.AutomationId="Measurement::R2" />
                    <GeneralWidget:ValidatedParamEditBox Width="65" HorizontalAlignment="Center" TxtAlignment="Center"
                                                         x:Name="K2EditBox" x:FieldModifier="public"
                                                         Format="N2" HintPath="Steep.Radius.HintDpt" ValuePath="K2"
                                                         Visibility="{Binding IsChecked, ElementName=MeridianMmRadio, Converter={StaticResource BoolToInvisibilityConverter}}"
                                                         AutomationProperties.AutomationId="Measurement::K2" />
                </Grid>

                <!-- Units -->
                <TextBlock Grid.Row="1" Grid.Column="2" Style="{StaticResource AttachedUnitsTextBlockStyle}"
                           x:Name="R2Units"
                           Text="mm"
                           Visibility="{Binding IsChecked, ElementName=MeridianMmRadio, Converter={StaticResource BoolToVisibilityConverter}}" />
                <TextBlock Grid.Row="1" Grid.Column="2" Style="{StaticResource AttachedUnitsTextBlockStyle}"
                           x:Name="K2Units"
                           Text="D"
                           Visibility="{Binding IsChecked, ElementName=MeridianMmRadio, Converter={StaticResource BoolToInvisibilityConverter}}" />
            </Grid>


            <!-- Axis -->
            <Grid Grid.Column="2">
                <Grid.RowDefinitions>
                    <RowDefinition Height="25" />
                    <RowDefinition Height="30" />
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="1*" />
                    <ColumnDefinition Width="70" />
                    <ColumnDefinition Width="2.2*" />
                </Grid.ColumnDefinitions>

                <!-- Label -->
                <TextBlock Grid.Row="0" Grid.Column="1" Style="{StaticResource TabelNormalTextBlockStyle}"
                           x:Name="FlatLabel"
                           Text="{Binding Converter={StaticResource TranslateConverter}, ConverterParameter='id:-216513581|en:Flat'}"
                           Visibility="{Binding IsCylinderSignMinus, Converter={StaticResource BoolToVisibilityConverter}}" />
                <TextBlock Grid.Row="0" Grid.Column="1" Style="{StaticResource TabelNormalTextBlockStyle}"
                           x:Name="SteepLabel"
                           Text="{Binding Converter={StaticResource TranslateConverter}, ConverterParameter='id:1890261173|en:Steep'}"
                           Visibility="{Binding IsCylinderSignMinus, Converter={StaticResource BoolToInvisibilityConverter}}" />

                <!-- Textbox -->
                <Grid Grid.Row="1" Grid.Column="1"
                      Name="TextBlockGrid_Axis">
                    <Border Width="65" Height="30" HorizontalAlignment="Center" VerticalAlignment="Center"
                            BorderBrush="{DynamicResource EditBoxDisabledBorderColor}" BorderThickness="1"
                            Background="Transparent" SnapsToDevicePixels="True">
                        <TextBlock Style="{StaticResource TabelNormalTextBlockStyle}"
                                   x:Name="AxisTextBlock" x:FieldModifier="public"
                                   FontSize="15" FontWeight="Thin" >
                            <TextBlock.Text>
                                <MultiBinding StringFormat="N0" Mode="OneWay" Converter="{StaticResource AngleToStrConverter}" ConverterParameter="n/a">
                                    <Binding Path="NormalizedAstigmatismAxis"/>
                                    <Binding Path="Steep.Axis.Max" />
                                    <Binding Path="Steep.Axis.Unit" />
                                </MultiBinding>
                            </TextBlock.Text>
                        </TextBlock>
                    </Border>
                </Grid>
                <Grid Grid.Row="1" Grid.Column="1"
                      Name="EditBoxGrid_Axis">
                    <GeneralWidget:ValidatedParamEditBox Width="65" HorizontalAlignment="Center" TxtAlignment="Center"
                                                         x:Name="AxisEditBox" x:FieldModifier="public"
                                                         Format="N0" HintPath="Steep.Axis.Hint" ValuePath="NormalizedAstigmatismAxis"
                                                         InputParamType="Angle" MaxValuePath="Steep.Axis.Max" UnitPath="Steep.Axis.Unit"
                                                         AutomationProperties.AutomationId="Measurement::Axis" />
                </Grid>

                <!-- Units -->
                <TextBlock Grid.Row="1" Grid.Column="2" Style="{StaticResource AttachedUnitsTextBlockStyle}"
                           Text="°" />
            </Grid>

            <!-- Cylinder -->
            <Grid Grid.Column="3">
                <Grid.RowDefinitions>
                    <RowDefinition Height="25" />
                    <RowDefinition Height="30" />
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="1*" />
                    <ColumnDefinition Width="70" />
                    <ColumnDefinition Width="2.2*" />
                </Grid.ColumnDefinitions>

                <!-- Label -->
                <TextBlock Grid.Row="0" Grid.Column="1" Style="{StaticResource TabelNormalTextBlockStyle}"
                           Text="Cyl" />

                <!-- Textbox -->
                <Border Grid.Row="1" Grid.Column="1"
                        Width="65" Height="30"
                        BorderBrush="{DynamicResource EditBoxDisabledBorderColor}" BorderThickness="1"
                        Background="Transparent" SnapsToDevicePixels="True">

                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                        <TextBlock Style="{StaticResource TabelNormalTextBlockStyle}"
                                   Text="+" FontSize="15" FontWeight="Thin"
                                   Visibility="{Binding IsCylinderSignMinus, Converter={StaticResource BoolToInvisibilityConverter}}" />
                        <TextBlock Style="{StaticResource TabelNormalTextBlockStyle}"
                                   Text="-" FontSize="15" FontWeight="Thin"
                                   Visibility="{Binding IsCylinderSignMinus, Converter={StaticResource BoolToVisibilityConverter}}" />
                        <TextBlock Style="{StaticResource TabelNormalTextBlockStyle}"
                                   x:Name="CylTxtBlock"
                                   Text="{Binding CylinderPower, Mode=OneWay, StringFormat=N2, TargetNullValue='n/a'}" FontSize="15"
                                   FontWeight="Thin" />
                    </StackPanel>
                </Border>

                <!-- Old position -->
                <!-- Units -->
                <TextBlock Grid.Row="1" Grid.Column="2" Style="{StaticResource AttachedUnitsTextBlockStyle}"
                           Text="D" />
            </Grid>


            <!-- Units D/mm radio button -->
            <Grid Grid.Column="4">
                <Grid.RowDefinitions>
                    <RowDefinition Height="25" />
                    <RowDefinition Height="30" />
                </Grid.RowDefinitions>

                <TextBlock Grid.Row="0" Style="{StaticResource TabelNormalTextBlockStyle}"
                           Text="{Binding Converter={StaticResource TranslateConverter}, ConverterParameter='id:1891933781|en:Units'}" />

                <Grid Grid.Row="1"
                      HorizontalAlignment="Center" VerticalAlignment="Center">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="1*" />
                        <ColumnDefinition Width="1*" />
                    </Grid.ColumnDefinitions>

                    <RadioButton Grid.Column="0" Style="{StaticResource VPLeftRadioButtonStyle}"
                                 Width="35" Height="25" HorizontalAlignment="Right" VerticalAlignment="Top"
                                 x:Name="MeridianDptRadio"
                                 Content="D"
                                 IsChecked="{Binding Path=IsCurvatureMmUnitUsed, Converter={StaticResource InverseBooleanConverter}, Mode=TwoWay}" />
                    <RadioButton Grid.Column="1" Style="{StaticResource VPRightRadioButtonStyle}"
                                 Width="35" Height="25" HorizontalAlignment="Left" VerticalAlignment="Top"
                                 x:Name="MeridianMmRadio"
                                 Content="mm"
                                 IsChecked="{Binding Path=IsCurvatureMmUnitUsed, Mode=TwoWay}" />
                </Grid>
            </Grid>
        </Grid>
    </Grid>
</UserControl>
