/*
 * Copyright 2021-present Alcon. All rights reserved.
 * Alcon proprietary/confidential.
 * Use is subject to license terms.
 */

using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using System.Security.Cryptography;
using System.Linq;

namespace LocalizationLib;

/// <summary>
/// Amazon Bedrock LLM client that implements ILlmClient interface.
///
/// This implementation provides enterprise-grade integration with Amazon Bedrock using REST API calls
/// and AWS Signature Version 4 authentication, eliminating the need for external AWS SDK dependencies.
///
/// Features:
/// - Multi-model support: Claude 3 (Sonnet/Haiku), Amazon Titan, Meta Llama, Mistral
/// - Intelligent rate limiting with exponential backoff and jitter
/// - Comprehensive error handling and retry logic
/// - Flexible authentication (configuration file or environment variables)
/// - Optional debug logging and metrics collection
/// - Production-ready reliability and performance
///
/// Supported Models:
/// - anthropic.claude-3-sonnet-20240229-v1:0 (recommended for complex analysis)
/// - anthropic.claude-3-haiku-20240307-v1:0 (fast, cost-effective)
/// - amazon.titan-text-express-v1 (general purpose)
/// - meta.llama2-70b-chat-v1 (high-quality analysis)
/// - mistral.mistral-7b-instruct-v0:2 (code analysis)
///
/// Authentication:
/// Requires AWS credentials with bedrock:InvokeModel permission.
/// Credentials can be provided via configuration file or environment variables.
///
/// Rate Limiting:
/// Implements intelligent retry logic with exponential backoff (1s → 2s → 4s → 8s → 16s → 30s)
/// and jitter to prevent thundering herd effects. Respects AWS Retry-After headers.
///
/// Error Handling:
/// - 403 Forbidden: Check IAM permissions and model access
/// - 429 TooManyRequests: Automatic retry with backoff
/// - Network errors: Configurable retry with exponential backoff
/// - Authentication errors: Immediate failure (no retry)
///
/// Usage:
/// Configure via LLMConfig with bedrockProviderConfig section containing region, modelId,
/// and authentication credentials. Enable debug logging for troubleshooting.
/// </summary>
public class BedrockLlmClient : BaseLlmClient
{
    public BedrockLlmClient(LLMConfig cfg) : base(cfg)
    {
        if (_cfg.bedrockProviderConfig == null)
        {
            throw new InvalidOperationException("No bedrockProviderConfig found in LLMConfig.");
        }

        if (string.IsNullOrEmpty(_cfg.bedrockProviderConfig.region) ||
            string.IsNullOrEmpty(_cfg.bedrockProviderConfig.modelId))
        {
            throw new InvalidOperationException("region or modelId is missing in bedrockProviderConfig.");
        }

        // Validate credentials configuration
        ValidateCredentialsConfiguration();
    }

    #region BaseLlmClient Implementation

    protected override HttpClient CreateHttpClient()
    {
        return new HttpClient
        {
            Timeout = TimeSpan.FromSeconds(_cfg.bedrockProviderConfig.timeoutSeconds)
        };
    }

    protected override async Task<string> CallLlmForDiscovery(string systemPrompt, string userPrompt)
    {
        return await CallBedrockRestApi(systemPrompt, userPrompt);
    }

    protected override async Task<string> CallLlmForVerification(string systemPrompt, string userPrompt)
    {
        return await CallBedrockVerificationWithToolUse(systemPrompt, userPrompt);
    }

    protected override List<ReplacementRecord> ParseDiscoveryResponse(string rawResponse, string groupId, string filePath)
    {
        return ExtractRecordsFromResponse(rawResponse, groupId, filePath);
    }

    protected override TranslationVerificationResponse ParseVerificationResponse(string rawResponse)
    {
        try
        {
            var response = JsonSerializer.Deserialize<BedrockVerificationResponse>(rawResponse);
            return new TranslationVerificationResponse
            {
                needsTranslation = response?.needsTranslation ?? false,
                reason = response?.reason ?? "Failed to parse response",
                context = response?.context ?? ""
            };
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"Failed to parse Bedrock verification response: {ex.Message}", ex);
        }
    }

    protected override string GetDiscoverySystemPrompt()
    {
        return LlmPrompts.BedrockCsDiscoverySystemPrompt;
    }

    protected override string GetCsVerificationSystemPrompt()
    {
        return LlmPrompts.BedrockCsVerificationSystemPrompt;
    }

    protected override string GetXamlVerificationSystemPrompt()
    {
        return LlmPrompts.BedrockXamlVerificationSystemPrompt;
    }

    protected override string GetCppVerificationSystemPrompt()
    {
        return LlmPrompts.BedrockCppVerificationSystemPrompt;
    }

    protected override string BuildDiscoveryUserPrompt(string lineIndexedCode, string fileName)
    {
        var userPrompt = _cfg.promptTemplate?.Replace("{{fileName}}", fileName) ??
                        "Analyze this C# code and identify user-facing strings that need translation:";
        return userPrompt + $"\n\n{lineIndexedCode}";
    }

    #endregion



    private async Task<string> CallBedrockVerificationWithToolUse(string systemPrompt, string userPrompt)
    {
        return await ExecuteWithRetry(
            async () =>
            {
                // Create the verification tool use request body
                string requestBody = CreateVerificationToolUseRequestBody(systemPrompt, userPrompt);

                // Create the REST API request
                var request = CreateBedrockHttpRequest(requestBody);

                // Optional debug logging
                if (IsDebugLoggingEnabled())
                {
                    Console.WriteLine($"[DEBUG] Bedrock Tool Use Request: {request.RequestUri}");
                    Console.WriteLine($"[DEBUG] Model: {_cfg.bedrockProviderConfig.modelId}");
                    Console.WriteLine($"[DEBUG] Request Body Length: {requestBody.Length} characters");
                }

                // Send the request
                var response = await _httpClient.SendAsync(request);

                // Handle rate limiting specifically
                if (response.StatusCode == System.Net.HttpStatusCode.TooManyRequests)
                {
                    string errorBody = await response.Content.ReadAsStringAsync();
                    throw new HttpRequestException($"Rate limited: {errorBody}");
                }

                if (!response.IsSuccessStatusCode)
                {
                    string errorBody = await response.Content.ReadAsStringAsync();
                    if (IsDebugLoggingEnabled())
                    {
                        Console.WriteLine($"[DEBUG] Error Response: {response.StatusCode} - {errorBody}");
                    }
                    throw new HttpRequestException($"Bedrock API error: {response.StatusCode} - {errorBody}");
                }

                // Parse the tool use response
                string responseBody = await response.Content.ReadAsStringAsync();
                return ParseBedrockToolUseResponse(responseBody);
            },
            _cfg.bedrockProviderConfig.maxRetries,
            (ex, attempt) => ShouldRetryBedrockError(ex, attempt),
            (attempt) => CalculateBedrockBackoff(attempt)
        );
    }

    private async Task<string> CallBedrockRestApi(string systemPrompt, string userPrompt)
    {
        return await ExecuteWithRetry(
            async () =>
            {
                // Create the request body based on model type
                string requestBody = CreateRequestBody(systemPrompt, userPrompt);

                // Create the REST API request
                var request = CreateBedrockHttpRequest(requestBody);

                // Optional debug logging (can be enabled via configuration)
                if (IsDebugLoggingEnabled())
                {
                    Console.WriteLine($"[DEBUG] Bedrock Request: {request.RequestUri}");
                    Console.WriteLine($"[DEBUG] Model: {_cfg.bedrockProviderConfig.modelId}");
                    Console.WriteLine($"[DEBUG] Request Body Length: {requestBody.Length} characters");
                }

                // Send the request
                var response = await _httpClient.SendAsync(request);

                // Optional debug logging for response
                if (IsDebugLoggingEnabled())
                {
                    Console.WriteLine($"[DEBUG] Response Status: {response.StatusCode}");
                    if (response.Headers.Contains("X-Amzn-Bedrock-Input-Token-Count"))
                    {
                        Console.WriteLine($"[DEBUG] Input Tokens: {response.Headers.GetValues("X-Amzn-Bedrock-Input-Token-Count").FirstOrDefault()}");
                    }
                    if (response.Headers.Contains("X-Amzn-Bedrock-Output-Token-Count"))
                    {
                        Console.WriteLine($"[DEBUG] Output Tokens: {response.Headers.GetValues("X-Amzn-Bedrock-Output-Token-Count").FirstOrDefault()}");
                    }
                }

                // Handle rate limiting specifically
                if (response.StatusCode == System.Net.HttpStatusCode.TooManyRequests)
                {
                    string errorBody = await response.Content.ReadAsStringAsync();
                    throw new HttpRequestException($"Rate limited: {errorBody}");
                }

                if (!response.IsSuccessStatusCode)
                {
                    string errorBody = await response.Content.ReadAsStringAsync();
                    if (IsDebugLoggingEnabled())
                    {
                        Console.WriteLine($"[DEBUG] Error Response: {response.StatusCode} - {errorBody}");
                    }
                    throw new HttpRequestException($"Bedrock API error: {response.StatusCode} - {errorBody}");
                }

                // Parse the response
                string responseBody = await response.Content.ReadAsStringAsync();
                return ParseBedrockResponse(responseBody);
            },
            _cfg.bedrockProviderConfig.maxRetries,
            (ex, attempt) => ShouldRetryBedrockError(ex, attempt),
            (attempt) => CalculateBedrockBackoff(attempt)
        );
    }

    private HttpRequestMessage CreateBedrockHttpRequest(string requestBody)
    {
        var modelId = _cfg.bedrockProviderConfig.modelId;
        var region = _cfg.bedrockProviderConfig.region;

        // Create the Bedrock endpoint URL
        string endpoint = $"https://bedrock-runtime.{region}.amazonaws.com/model/{modelId}/invoke";

        var request = new HttpRequestMessage(HttpMethod.Post, endpoint);
        request.Content = new StringContent(requestBody, Encoding.UTF8, "application/json");

        // Add required headers for Claude models
        request.Headers.Add("Accept", "application/json");

        // Claude models require specific content type and anthropic version
        if (modelId.Contains("claude"))
        {
            request.Content.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue("application/json");
            request.Headers.Add("anthropic-version", "bedrock-2023-05-31");
        }

        // Add AWS Signature Version 4 authentication
        SignRequest(request, requestBody);

        return request;
    }

    private string CreateRequestBody(string systemPrompt, string userPrompt)
    {
        var modelId = _cfg.bedrockProviderConfig.modelId;

        // Enhance the user prompt with strict JSON schema requirements
        string enhancedUserPrompt = EnhancePromptWithJsonSchema(userPrompt);

        if (modelId.Contains("claude"))
        {
            // Claude models use Anthropic format
            return CreateClaudeRequestBody(systemPrompt, enhancedUserPrompt);
        }
        else if (modelId.Contains("titan"))
        {
            // Amazon Titan models
            return CreateTitanRequestBody(systemPrompt, enhancedUserPrompt);
        }
        else if (modelId.Contains("llama") || modelId.Contains("mistral"))
        {
            // Meta Llama or Mistral models
            return CreateLlamaRequestBody(systemPrompt, enhancedUserPrompt);
        }
        else
        {
            // Default to Claude format for unknown models
            return CreateClaudeRequestBody(systemPrompt, enhancedUserPrompt);
        }
    }

    /// <summary>
    /// Enhances the user prompt with strict JSON schema requirements.
    /// </summary>
    /// <param name="userPrompt">The original user prompt</param>
    /// <returns>Enhanced prompt with JSON schema enforcement</returns>
    private string EnhancePromptWithJsonSchema(string userPrompt)
    {
        return userPrompt + @"

CRITICAL: Your response must be ONLY valid JSON in this exact format:
{
  ""records"": [
    {
      ""lineNumber"": 123,
      ""column"": 45,
      ""originalValue"": ""exact string value"",
      ""reason"": ""brief explanation"",
      ""lineText"": ""complete line content""
    }
  ]
}

REQUIREMENTS:
- Return ONLY the JSON object, no markdown formatting
- Do NOT wrap in ```json or ``` blocks
- Do NOT include any text before or after the JSON
- If no translatable strings found, return: {""records"": []}
- Ensure all JSON is properly escaped and valid
- lineNumber must be an integer (0-based index)
- column must be an integer (0-based position)";
    }

    private string CreateClaudeRequestBody(string systemPrompt, string userPrompt)
    {
        // Use tool use for structured JSON output (similar to LocalLlmClient's JSON schema)
        var requestBody = new
        {
            anthropic_version = "bedrock-2023-05-31",
            max_tokens = _cfg.bedrockProviderConfig.maxTokens,
            temperature = _cfg.bedrockProviderConfig.temperature,
            system = systemPrompt,
            messages = new[]
            {
                new { role = "user", content = userPrompt }
            },
            tools = new[]
            {
                new
                {
                    name = "extract_translatable_strings",
                    description = "Extract translatable strings from code and return structured data",
                    input_schema = new
                    {
                        type = "object",
                        properties = new
                        {
                            records = new
                            {
                                type = "array",
                                items = new
                                {
                                    type = "object",
                                    properties = new
                                    {
                                        lineNumber = new { type = "integer", description = "The line index from the snippet" },
                                        column = new { type = "integer", description = "The exact column index in that line where the string literal starts" },
                                        originalValue = new { type = "string", description = "The user-facing string literal discovered" },
                                        reason = new { type = "string", description = "A brief explanation of why it is user-facing text" },
                                        lineText = new { type = "string", description = "The entire line from the snippet containing that string" }
                                    },
                                    required = new[] { "lineNumber", "column", "originalValue", "reason", "lineText" }
                                }
                            }
                        },
                        required = new[] { "records" }
                    }
                }
            },
            tool_choice = new { type = "tool", name = "extract_translatable_strings" }
        };

        return JsonSerializer.Serialize(requestBody);
    }

    private string CreateVerificationToolUseRequestBody(string systemPrompt, string userPrompt)
    {
        var requestBody = new
        {
            anthropic_version = "bedrock-2023-05-31",
            max_tokens = _cfg.bedrockProviderConfig.maxTokens,
            temperature = _cfg.bedrockProviderConfig.temperature,
            system = systemPrompt,
            messages = new[]
            {
                new { role = "user", content = userPrompt }
            },
            tools = new[]
            {
                new
                {
                    name = "verify_translation_needed",
                    description = "Verify if a text needs translation and provide reasoning",
                    input_schema = new
                    {
                        type = "object",
                        properties = new
                        {
                            needsTranslation = new { type = "boolean", description = "Whether the text needs to be translated" },
                            reason = new { type = "string", description = "Detailed explanation of why the text needs or doesn't need translation" },
                            context = new { type = "string", description = "Additional context about where and how the text is used" }
                        },
                        required = new[] { "needsTranslation", "reason", "context" }
                    }
                }
            },
            tool_choice = new { type = "tool", name = "verify_translation_needed" }
        };

        return JsonSerializer.Serialize(requestBody);
    }

    private string CreateTitanRequestBody(string systemPrompt, string userPrompt)
    {
        var requestBody = new
        {
            inputText = $"{systemPrompt}\n\nHuman: {userPrompt}\n\nAssistant:",
            textGenerationConfig = new
            {
                maxTokenCount = _cfg.bedrockProviderConfig.maxTokens,
                temperature = _cfg.bedrockProviderConfig.temperature,
                topP = 0.9,
                stopSequences = new[] { "Human:" }
            }
        };

        return JsonSerializer.Serialize(requestBody);
    }

    private string CreateLlamaRequestBody(string systemPrompt, string userPrompt)
    {
        var requestBody = new
        {
            prompt = $"<s>[INST] <<SYS>>\n{systemPrompt}\n<</SYS>>\n\n{userPrompt} [/INST]",
            max_gen_len = _cfg.bedrockProviderConfig.maxTokens,
            temperature = _cfg.bedrockProviderConfig.temperature,
            top_p = 0.9
        };

        return JsonSerializer.Serialize(requestBody);
    }

    private string ParseBedrockResponse(string responseBody)
    {
        var modelId = _cfg.bedrockProviderConfig.modelId;

        if (modelId.Contains("claude"))
        {
            return ParseClaudeResponse(responseBody);
        }
        else if (modelId.Contains("titan"))
        {
            return ParseTitanResponse(responseBody);
        }
        else if (modelId.Contains("llama") || modelId.Contains("mistral"))
        {
            return ParseLlamaResponse(responseBody);
        }
        else
        {
            // Default to Claude format
            return ParseClaudeResponse(responseBody);
        }
    }

    private string ParseBedrockToolUseResponse(string responseBody)
    {
        try
        {
            var response = JsonSerializer.Deserialize<ClaudeToolResponse>(responseBody);

            // Find the tool use content
            if (response?.content != null)
            {
                foreach (var content in response.content)
                {
                    if (content.type == "tool_use" &&
                        (content.name == "verify_translation_needed" || content.name == "extract_translatable_strings"))
                    {
                        // Return the tool input as JSON string
                        return JsonSerializer.Serialize(content.input);
                    }
                }
            }

            // If no tool use found, return empty result
            return "{}";
        }
        catch (Exception ex)
        {
            if (IsDebugLoggingEnabled())
            {
                Console.WriteLine($"[DEBUG] Failed to parse tool use response: {ex.Message}");
                Console.WriteLine($"[DEBUG] Raw response: {responseBody}");
            }
            throw new InvalidOperationException($"Failed to parse Bedrock tool use response: {ex.Message}", ex);
        }
    }

    private void SignRequest(HttpRequestMessage request, string requestBody)
    {
        var accessKey = GetAccessKey();
        var secretKey = GetSecretKey();
        var sessionToken = GetSessionToken();
        var region = _cfg.bedrockProviderConfig.region;

        var service = "bedrock";
        var algorithm = "AWS4-HMAC-SHA256";
        var now = DateTime.UtcNow;
        var dateStamp = now.ToString("yyyyMMdd");
        var timeStamp = now.ToString("yyyyMMddTHHmmssZ");

        // Create canonical request - URL encode the path properly
        var path = request.RequestUri.AbsolutePath;
        var canonicalUri = path.Replace(":", "%3A");
        var canonicalQueryString = "";
        var canonicalHeaders = $"host:{request.RequestUri.Host}\nx-amz-date:{timeStamp}\n";
        var signedHeaders = "host;x-amz-date";

        if (!string.IsNullOrEmpty(sessionToken))
        {
            canonicalHeaders += $"x-amz-security-token:{sessionToken}\n";
            signedHeaders += ";x-amz-security-token";
            request.Headers.Add("X-Amz-Security-Token", sessionToken);
        }

        var payloadHash = ComputeSha256Hash(requestBody);
        var canonicalRequest = $"POST\n{canonicalUri}\n{canonicalQueryString}\n{canonicalHeaders}\n{signedHeaders}\n{payloadHash}";

        // Create string to sign
        var credentialScope = $"{dateStamp}/{region}/{service}/aws4_request";
        var stringToSign = $"{algorithm}\n{timeStamp}\n{credentialScope}\n{ComputeSha256Hash(canonicalRequest)}";

        // Calculate signature
        var signingKey = GetSignatureKey(secretKey, dateStamp, region, service);
        var signature = ComputeHmacSha256(stringToSign, signingKey);

        // Create authorization header
        var authorizationHeader = $"{algorithm} Credential={accessKey}/{credentialScope}, SignedHeaders={signedHeaders}, Signature={signature}";

        // Add headers to request
        request.Headers.Add("X-Amz-Date", timeStamp);
        request.Headers.TryAddWithoutValidation("Authorization", authorizationHeader);
    }

    private string ComputeSha256Hash(string input)
    {
        using (var sha256 = SHA256.Create())
        {
            var bytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(input));
            return BitConverter.ToString(bytes).Replace("-", "").ToLowerInvariant();
        }
    }

    private string ComputeHmacSha256(string input, byte[] key)
    {
        using (var hmac = new HMACSHA256(key))
        {
            var bytes = hmac.ComputeHash(Encoding.UTF8.GetBytes(input));
            return BitConverter.ToString(bytes).Replace("-", "").ToLowerInvariant();
        }
    }

    private byte[] GetSignatureKey(string key, string dateStamp, string regionName, string serviceName)
    {
        var kDate = ComputeHmacSha256Bytes(dateStamp, Encoding.UTF8.GetBytes("AWS4" + key));
        var kRegion = ComputeHmacSha256Bytes(regionName, kDate);
        var kService = ComputeHmacSha256Bytes(serviceName, kRegion);
        var kSigning = ComputeHmacSha256Bytes("aws4_request", kService);
        return kSigning;
    }

    private byte[] ComputeHmacSha256Bytes(string input, byte[] key)
    {
        using (var hmac = new HMACSHA256(key))
        {
            return hmac.ComputeHash(Encoding.UTF8.GetBytes(input));
        }
    }

    private string ParseClaudeResponse(string responseBody)
    {
        try
        {
            var response = JsonSerializer.Deserialize<ClaudeToolResponse>(responseBody);

            // Check if this is a tool use response
            if (response?.content != null)
            {
                foreach (var content in response.content)
                {
                    if (content.type == "tool_use" && content.name == "extract_translatable_strings")
                    {
                        // Return the tool input as JSON string
                        return JsonSerializer.Serialize(content.input);
                    }
                }
            }

            // Fallback to regular text response
            var textContent = response?.content?.FirstOrDefault(c => c.type == "text");
            return textContent?.text ?? "";
        }
        catch
        {
            return responseBody; // Return raw response if parsing fails
        }
    }

    private string ParseTitanResponse(string responseBody)
    {
        try
        {
            var response = JsonSerializer.Deserialize<TitanResponse>(responseBody);
            return response?.results?[0]?.outputText ?? "";
        }
        catch
        {
            return responseBody; // Return raw response if parsing fails
        }
    }

    private string ParseLlamaResponse(string responseBody)
    {
        try
        {
            var response = JsonSerializer.Deserialize<LlamaResponse>(responseBody);
            return response?.generation ?? "";
        }
        catch
        {
            return responseBody; // Return raw response if parsing fails
        }
    }

    private List<ReplacementRecord> ExtractRecordsFromResponse(string rawResponse, string groupId, string filePath)
    {
        try
        {
            // Try to extract JSON from response (handle cases where LLM adds extra text)
            string jsonContent = ExtractJsonFromResponse(rawResponse);

            if (IsDebugLoggingEnabled())
            {
                Console.WriteLine($"[DEBUG] Extracted JSON: {jsonContent}");
            }

            var response = JsonSerializer.Deserialize<BedrockDiscoveryResponse>(jsonContent);
            var records = new List<ReplacementRecord>();

            if (response?.records != null)
            {
                foreach (var record in response.records)
                {
                    // Validate record data
                    if (string.IsNullOrEmpty(record.originalValue))
                    {
                        if (IsDebugLoggingEnabled())
                        {
                            Console.WriteLine($"[DEBUG] Skipping record with empty originalValue at line {record.lineNumber}");
                        }
                        continue;
                    }

                    records.Add(new ReplacementRecord
                    {
                        LineNumber = record.lineNumber,
                        Column = record.column,
                        OriginalValue = record.originalValue,
                        NewKey = $"en:{record.originalValue}",
                        Property = "(string literal)",
                        LineContent = record.lineText ?? "",
                        GroupId = groupId,
                        FilePath = filePath,
                        Notes = new List<string> { record.reason ?? "LLM analysis" }
                    });
                }
            }

            if (IsDebugLoggingEnabled())
            {
                Console.WriteLine($"[DEBUG] Extracted {records.Count} valid records from LLM response");
            }

            return records;
        }
        catch (JsonException ex)
        {
            Console.WriteLine($"[ERROR] JSON parsing failed: {ex.Message}");
            if (IsDebugLoggingEnabled())
            {
                Console.WriteLine($"[DEBUG] Raw response: {rawResponse}");
                Console.WriteLine($"[DEBUG] Attempted JSON extraction: {ExtractJsonFromResponse(rawResponse)}");
            }
            return new List<ReplacementRecord>();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"[ERROR] Unexpected error parsing LLM response: {ex.Message}");
            if (IsDebugLoggingEnabled())
            {
                Console.WriteLine($"[DEBUG] Raw response: {rawResponse}");
            }
            return new List<ReplacementRecord>();
        }
    }

    private string ExtractJsonFromResponse(string response)
    {
        if (string.IsNullOrWhiteSpace(response))
        {
            return "{\"records\": []}";
        }

        string cleanedResponse = response.Trim();

        // Handle various markdown code block formats
        if (cleanedResponse.StartsWith("```json", StringComparison.OrdinalIgnoreCase))
        {
            cleanedResponse = cleanedResponse.Substring(7);
        }
        else if (cleanedResponse.StartsWith("```", StringComparison.OrdinalIgnoreCase))
        {
            cleanedResponse = cleanedResponse.Substring(3);
        }

        if (cleanedResponse.EndsWith("```"))
        {
            cleanedResponse = cleanedResponse.Substring(0, cleanedResponse.Length - 3);
        }

        cleanedResponse = cleanedResponse.Trim();

        // Remove common prefixes that LLMs might add
        string[] prefixesToRemove = {
            "Here is the JSON response:",
            "Here's the JSON:",
            "JSON response:",
            "Response:",
            "Result:",
            "Output:"
        };

        foreach (string prefix in prefixesToRemove)
        {
            if (cleanedResponse.StartsWith(prefix, StringComparison.OrdinalIgnoreCase))
            {
                cleanedResponse = cleanedResponse.Substring(prefix.Length).Trim();
                break;
            }
        }

        // Find JSON content between braces - handle nested objects
        int startIndex = cleanedResponse.IndexOf('{');
        if (startIndex < 0)
        {
            // No JSON found, return empty records
            return "{\"records\": []}";
        }

        // Find matching closing brace
        int braceCount = 0;
        int endIndex = -1;
        for (int i = startIndex; i < cleanedResponse.Length; i++)
        {
            if (cleanedResponse[i] == '{')
                braceCount++;
            else if (cleanedResponse[i] == '}')
            {
                braceCount--;
                if (braceCount == 0)
                {
                    endIndex = i;
                    break;
                }
            }
        }

        if (endIndex > startIndex)
        {
            string jsonCandidate = cleanedResponse.Substring(startIndex, endIndex - startIndex + 1);

            // Validate that it's actually JSON by trying to parse it
            try
            {
                using (JsonDocument.Parse(jsonCandidate))
                {
                    return jsonCandidate;
                }
            }
            catch
            {
                // If parsing fails, return empty records
                if (IsDebugLoggingEnabled())
                {
                    Console.WriteLine($"[DEBUG] Failed to parse extracted JSON: {jsonCandidate}");
                }
                return "{\"records\": []}";
            }
        }

        // If no valid JSON found, return empty records
        return "{\"records\": []}";
    }

    private void ValidateCredentialsConfiguration()
    {
        var configAccessKey = _cfg.bedrockProviderConfig.accessKey;
        var configSecretKey = _cfg.bedrockProviderConfig.secretKey;
        var configSessionToken = _cfg.bedrockProviderConfig.sessionToken;

        // Check for invalid credential combinations in config
        if (!string.IsNullOrEmpty(configAccessKey) && string.IsNullOrEmpty(configSecretKey))
        {
            throw new InvalidOperationException("AWS credentials are required for Bedrock REST API access. If accessKey is provided, secretKey must also be provided.");
        }

        if (string.IsNullOrEmpty(configAccessKey) && !string.IsNullOrEmpty(configSecretKey))
        {
            throw new InvalidOperationException("AWS credentials are required for Bedrock REST API access. If secretKey is provided, accessKey must also be provided.");
        }

        // Special case: if sessionToken is provided but credentials are empty, fail immediately
        if (!string.IsNullOrEmpty(configSessionToken) && (string.IsNullOrEmpty(configAccessKey) || string.IsNullOrEmpty(configSecretKey)))
        {
            throw new InvalidOperationException("AWS credentials are required for Bedrock REST API access");
        }

        // If both config credentials are explicitly empty strings, fail (don't fall back to environment)
        if (configAccessKey == "" && configSecretKey == "")
        {
            throw new InvalidOperationException("AWS credentials are required for Bedrock REST API access. Provide them in configuration (accessKey/secretKey) or set environment variables (AWS_ACCESS_KEY_ID/AWS_SECRET_ACCESS_KEY).");
        }

        // Check if we have valid credentials (either from config or environment)
        var accessKey = GetAccessKey();
        var secretKey = GetSecretKey();

        if (string.IsNullOrEmpty(accessKey) || string.IsNullOrEmpty(secretKey))
        {
            throw new InvalidOperationException("AWS credentials are required for Bedrock REST API access. Provide them in configuration (accessKey/secretKey) or set environment variables (AWS_ACCESS_KEY_ID/AWS_SECRET_ACCESS_KEY).");
        }
    }

    private string GetAccessKey()
    {
        return !string.IsNullOrEmpty(_cfg.bedrockProviderConfig.accessKey)
            ? _cfg.bedrockProviderConfig.accessKey
            : Environment.GetEnvironmentVariable("AWS_ACCESS_KEY_ID");
    }

    private string GetSecretKey()
    {
        return !string.IsNullOrEmpty(_cfg.bedrockProviderConfig.secretKey)
            ? _cfg.bedrockProviderConfig.secretKey
            : Environment.GetEnvironmentVariable("AWS_SECRET_ACCESS_KEY");
    }

    private string GetSessionToken()
    {
        return !string.IsNullOrEmpty(_cfg.bedrockProviderConfig.sessionToken)
            ? _cfg.bedrockProviderConfig.sessionToken
            : Environment.GetEnvironmentVariable("AWS_SESSION_TOKEN");
    }

    private string GetRegion()
    {
        return !string.IsNullOrEmpty(_cfg.bedrockProviderConfig.region)
            ? _cfg.bedrockProviderConfig.region
            : Environment.GetEnvironmentVariable("AWS_DEFAULT_REGION") ?? "us-east-1";
    }

    /// <summary>
    /// Calculates exponential backoff delay with a maximum cap.
    /// </summary>
    /// <param name="attempt">The current attempt number (1-based)</param>
    /// <returns>Delay in milliseconds</returns>


    /// <summary>
    /// Calculates dynamic backoff delay based on error type and context.
    /// </summary>
    /// <param name="attempt">The current attempt number (1-based)</param>
    /// <param name="errorBody">The error response body</param>
    /// <param name="retryAfterSeconds">Retry-After header value if present</param>
    /// <returns>Delay in milliseconds</returns>
    private int CalculateDynamicBackoff(int attempt, string errorBody, int retryAfterSeconds)
    {
        // If Retry-After is specified, use it
        if (retryAfterSeconds > 0)
        {
            return retryAfterSeconds * 1000;
        }

        // Determine rate limit type and adjust accordingly
        string rateLimitType = GetRateLimitType(errorBody);

        switch (rateLimitType)
        {
            case "token_quota":
                // Token quota exceeded - longer wait times
                return Math.Min(60000, (int)Math.Pow(3, attempt) * 2000); // 6s, 18s, 54s, 60s max

            case "request_rate":
                // Request rate exceeded - shorter wait times
                return Math.Min(15000, (int)Math.Pow(2, attempt) * 1000); // 2s, 4s, 8s, 15s max

            case "concurrent_requests":
                // Too many concurrent requests - medium wait times
                return Math.Min(30000, (int)Math.Pow(2, attempt) * 1500); // 3s, 6s, 12s, 24s, 30s max

            default:
                // Unknown rate limit type - use standard exponential backoff
                return CalculateExponentialBackoff(attempt);
        }
    }

    /// <summary>
    /// Determines the type of rate limiting from error message.
    /// </summary>
    /// <param name="errorBody">The error response body</param>
    /// <returns>Rate limit type identifier</returns>
    private string GetRateLimitType(string errorBody)
    {
        if (string.IsNullOrEmpty(errorBody))
            return "unknown";

        string lowerError = errorBody.ToLowerInvariant();

        if (lowerError.Contains("too many tokens") || lowerError.Contains("token quota") || lowerError.Contains("quota exceeded"))
            return "token_quota";

        if (lowerError.Contains("request rate") || lowerError.Contains("requests per") || lowerError.Contains("rate limit"))
            return "request_rate";

        if (lowerError.Contains("concurrent") || lowerError.Contains("simultaneous"))
            return "concurrent_requests";

        return "unknown";
    }

    /// <summary>
    /// Extracts retry-after seconds from response headers.
    /// </summary>
    /// <param name="response">The HTTP response</param>
    /// <returns>Retry-after seconds, or 0 if not specified</returns>
    private int GetRetryAfterSeconds(HttpResponseMessage response)
    {
        if (response.Headers.RetryAfter?.Delta.HasValue == true)
        {
            return (int)response.Headers.RetryAfter.Delta.Value.TotalSeconds;
        }

        if (response.Headers.RetryAfter?.Date.HasValue == true)
        {
            var retryAfter = response.Headers.RetryAfter.Date.Value - DateTimeOffset.UtcNow;
            return Math.Max(0, (int)retryAfter.TotalSeconds);
        }

        return 0;
    }

    /// <summary>
    /// Determines if an exception is retryable.
    /// </summary>
    /// <param name="ex">The exception to check</param>
    /// <returns>True if the exception is retryable</returns>
    private bool ShouldRetryBedrockError(Exception ex, int attempt)
    {
        if (ex is HttpRequestException httpEx)
        {
            // Rate limiting should be retried
            if (httpEx.Message.Contains("Rate limited"))
                return true;

            // Other HTTP errors should be retried
            return true;
        }

        // Use base class logic for other exceptions
        return IsRetryableException(ex);
    }

    private int CalculateBedrockBackoff(int attempt)
    {
        // For rate limiting, use more aggressive backoff
        int baseDelay = (int)Math.Pow(2, attempt) * 1000; // 2s, 4s, 8s, 16s, 32s
        int maxDelay = 60000; // Cap at 60 seconds for Bedrock
        int delay = Math.Min(baseDelay, maxDelay);

        // Add jitter to prevent thundering herd
        int jitter = new Random().Next(0, Math.Min(2000, delay / 4));
        return delay + jitter;
    }

    /// <summary>
    /// Checks if debug logging is enabled.
    /// </summary>
    /// <returns>True if debug logging should be performed</returns>
    private bool IsDebugLoggingEnabled()
    {
        return _cfg.bedrockProviderConfig.enableDebugLogging ||
               Environment.GetEnvironmentVariable("BEDROCK_DEBUG_LOGGING")?.ToLowerInvariant() == "true";
    }

    /// <summary>
    /// Checks if metrics collection is enabled.
    /// </summary>
    /// <returns>True if metrics should be collected</returns>
    private bool IsMetricsEnabled()
    {
        return _cfg.bedrockProviderConfig.enableMetrics;
    }


}
