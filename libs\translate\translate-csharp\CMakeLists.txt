cmake_minimum_required(VERSION 3.22.0)
include_guard(GLOBAL)
project(translate_csharp)

include("${CMAKE_CURRENT_SOURCE_DIR}/../../../cmake/Globals.cmake")

set(TARGET ${PROJECT_NAME})

set(${PROJECT_NAME}_SOURCES 
    "${CMAKE_CURRENT_SOURCE_DIR}/src/Translator.cs"
    "${CMAKE_CURRENT_SOURCE_DIR}/src/Helper/LanguageHelper.cs"
    "${CMAKE_CURRENT_SOURCE_DIR}/src/Helper/Serializer.cs"
    "${CMAKE_CURRENT_SOURCE_DIR}/src/Helper/TranslateMetadata.cs"
    "${CMAKE_CURRENT_SOURCE_DIR}/src/Helper/TranslateUtils.cs"
    "${CMAKE_CURRENT_SOURCE_DIR}/src/Models/LanguageModel.cs"
    "${CMAKE_CURRENT_SOURCE_DIR}/src/Models/LanguageTranslation.cs"
    "${CMAKE_CURRENT_SOURCE_DIR}/src/Models/LocalizedString.cs"  
    "${CMAKE_CURRENT_SOURCE_DIR}/src/Attributes/LocalizedDescriptionAttribute.cs"  
)

include_platform_cmakelists()

# Generate the nuspec template
if(NOT NO_NUGET_FOR_${TARGET}) 
    include("${ROOT_DIR}/cmake/SetupNugetPrologue.cmake")

    file(TO_NATIVE_PATH "${OUTPUT_DIR}/*translate_csharp*" NUSPEC_CSHARP_FILES_SRC_DIR)
    file(TO_NATIVE_PATH "lib/${ROOT_DOTNET_TARGET_FRAMEWORK}" NUSPEC_CSHARP_FILES_DST_DIR)
  
    set(NUGET_SRC_DST_FILES 
        "${NUSPEC_CSHARP_FILES_SRC_DIR}" "${NUSPEC_CSHARP_FILES_DST_DIR}"
    )
    set(NUGET_DESCRIPTION "C# Translate library")
    set(NUGET_DEPENDENCIES 
    "        <group targetFramework=\"${ROOT_DOTNET_TARGET_FRAMEWORK}\">
            </group>"
    )

    include("${ROOT_DIR}/cmake/SetupNugetEpilogue.cmake")
endif()