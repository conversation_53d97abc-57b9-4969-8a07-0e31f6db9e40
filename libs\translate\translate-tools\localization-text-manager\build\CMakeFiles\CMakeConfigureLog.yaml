
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineSystem.cmake:205 (message)"
      - "CMakeLists.txt:2 (project)"
    message: |
      The system is: Windows - 10.0.26100 - AMD64
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCSharpCompiler.cmake:23 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:2 (project)"
    message: |
      Compiling the CSharp compiler identification source file "CMakeCSharpCompilerId.cs" succeeded.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      0
      MSBuild version 17.11.9+a69bbaaf5 for .NET Framework
      Build started 5/17/2025 10:36:51 AM.
      
      Project "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-text-manager\\build\\CMakeFiles\\4.0.0-rc4\\CompilerIdCSharp\\CompilerIdCSharp.csproj" on node 1 (default targets).
      PrepareForBuild:
        Creating directory "obj\\x64\\Debug\\".
      CoreCompile:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Current\\Bin\\Roslyn\\csc.exe /noconfig /nowarn:1701,1702 /fullpaths /nostdlib+ /platform:anycpu32bitpreferred /define:Platformx64;PlatformToolsetv143 /highentropyva+ /reference:"C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\mscorlib.dll" /reference:"C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Core.dll" /debug+ /out:obj\\x64\\Debug\\CompilerIdCSharp.exe /subsystemversion:6.00 /target:exe /utf8output /langversion:7.3 CMakeCSharpCompilerId.cs "obj\\x64\\Debug\\.NETFramework,Version=v4.7.2.AssemblyAttributes.cs"
        CompilerServer: server - server processed compilation - CompilerIdCSharp
      CopyFilesToOutputDirectory:
        Copying file from "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-text-manager\\build\\CMakeFiles\\4.0.0-rc4\\CompilerIdCSharp\\obj\\x64\\Debug\\CompilerIdCSharp.exe" to "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-text-manager\\build\\CMakeFiles\\4.0.0-rc4\\CompilerIdCSharp\\CompilerIdCSharp.exe".
        CompilerIdCSharp -> D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-text-manager\\build\\CMakeFiles\\4.0.0-rc4\\CompilerIdCSharp\\CompilerIdCSharp.exe
        Copying file from "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-text-manager\\build\\CMakeFiles\\4.0.0-rc4\\CompilerIdCSharp\\obj\\x64\\Debug\\CompilerIdCSharp.pdb" to "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-text-manager\\build\\CMakeFiles\\4.0.0-rc4\\CompilerIdCSharp\\CompilerIdCSharp.pdb".
      PostBuildEvent:
        if not "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Current\\Bin\\Roslyn"=="" if exist "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Current\\Bin\\Roslyn\\csc.exe" set _CSC=C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Current\\Bin\\Roslyn
        if exist "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Current\\Bin\\amd64\\csc.exe" set _CSC=C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Current\\Bin\\amd64
        if "%_CSC%"=="" exit -1
        @echo CMAKE_CSharp_COMPILER=%_CSC%\\csc.exe
        CMAKE_CSharp_COMPILER=C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Current\\Bin\\Roslyn\\csc.exe
      Done Building Project "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-text-manager\\build\\CMakeFiles\\4.0.0-rc4\\CompilerIdCSharp\\CompilerIdCSharp.csproj" (default targets).
      
      Build succeeded.
          0 Warning(s)
          0 Error(s)
      
      Time Elapsed 00:00:00.40
      
      
      Compilation of the CSharp compiler identification source "CMakeCSharpCompilerId.cs" produced "CompilerIdCSharp.csproj"
      
      Compilation of the CSharp compiler identification source "CMakeCSharpCompilerId.cs" produced "CompilerIdCSharp.exe"
      
      Compilation of the CSharp compiler identification source "CMakeCSharpCompilerId.cs" produced "CompilerIdCSharp.pdb"
      
      The CSharp compiler identification could not be found in:
        D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-text-manager/build/CMakeFiles/4.0.0-rc4/CompilerIdCSharp/CompilerIdCSharp.csproj
      
      The CSharp compiler identification is Microsoft Visual Studio, found in:
        D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-text-manager/build/CMakeFiles/4.0.0-rc4/CompilerIdCSharp/CompilerIdCSharp.exe
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeTestCSharpCompiler.cmake:34 (try_compile)"
      - "CMakeLists.txt:2 (project)"
    checks:
      - "Check for working C# compiler: C:/Program Files/Microsoft Visual Studio/2022/Professional/MSBuild/Current/Bin/Roslyn/csc.exe"
    directories:
      source: "D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-text-manager/build/CMakeFiles/CMakeScratch/TryCompile-nnejeo"
      binary: "D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-text-manager/build/CMakeFiles/CMakeScratch/TryCompile-nnejeo"
    cmakeVariables:
      CMAKE_CSharp_FLAGS: "/platform:x64 /define:TRACE"
      CMAKE_CSharp_FLAGS_DEBUG: "/debug:full /optimize- /warn:3 /errorreport:prompt /define:DEBUG"
      CMAKE_EXE_LINKER_FLAGS: ""
    buildResult:
      variable: "CMAKE_CSharp_COMPILER_WORKS"
      cached: true
      stdout: |
        Change Dir: 'D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-text-manager/build/CMakeFiles/CMakeScratch/TryCompile-nnejeo'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_d9a56.csproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.11.9+a69bbaaf5 for .NET Framework
        Build started 5/17/2025 10:36:52 AM.
        
        Project "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-text-manager\\build\\CMakeFiles\\CMakeScratch\\TryCompile-nnejeo\\cmTC_d9a56.csproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-text-manager\\build\\CMakeFiles\\CMakeScratch\\TryCompile-nnejeo\\Debug\\".
          Creating directory "obj\\x64\\Debug\\".
        CoreCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Current\\Bin\\Roslyn\\csc.exe /noconfig /nowarn:1701,1702 /fullpaths /nostdlib+ /platform:x64 /errorreport:prompt /warn:3 /define:TRACE;DEBUG /highentropyva+ /reference:"C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\mscorlib.dll" /reference:"C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Core.dll" /debug+ /debug:full /optimize- /out:obj\\x64\\Debug\\cmTC_d9a56.exe /subsystemversion:6.00 /target:exe /utf8output /langversion:7.3 D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-text-manager\\build\\CMakeFiles\\CMakeScratch\\TryCompile-nnejeo\\testCSharpCompiler.cs "obj\\x64\\Debug\\.NETFramework,Version=v4.7.2.AssemblyAttributes.cs"
          CompilerServer: server - server processed compilation - cmTC_d9a56
        CopyFilesToOutputDirectory:
          Copying file from "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-text-manager\\build\\CMakeFiles\\CMakeScratch\\TryCompile-nnejeo\\obj\\x64\\Debug\\cmTC_d9a56.exe" to "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-text-manager\\build\\CMakeFiles\\CMakeScratch\\TryCompile-nnejeo\\Debug\\cmTC_d9a56.exe".
          cmTC_d9a56 -> D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-text-manager\\build\\CMakeFiles\\CMakeScratch\\TryCompile-nnejeo\\Debug\\cmTC_d9a56.exe
          Copying file from "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-text-manager\\build\\CMakeFiles\\CMakeScratch\\TryCompile-nnejeo\\obj\\x64\\Debug\\cmTC_d9a56.pdb" to "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-text-manager\\build\\CMakeFiles\\CMakeScratch\\TryCompile-nnejeo\\Debug\\cmTC_d9a56.pdb".
        Done Building Project "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-text-manager\\build\\CMakeFiles\\CMakeScratch\\TryCompile-nnejeo\\cmTC_d9a56.csproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:00.18
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "D:/kibisoft/workarea/git/interop/libs/translate/translate-csharp/CMakeLists.txt:3 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      0
      MSBuild version 17.11.9+a69bbaaf5 for .NET Framework
      Build started 5/17/2025 10:36:53 AM.
      
      Project "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-text-manager\\build\\CMakeFiles\\4.0.0-rc4\\CompilerIdC\\CompilerIdC.vcxproj" on node 1 (default targets).
      PrepareForBuild:
        Creating directory "Debug\\".
        Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
        Creating directory "Debug\\CompilerIdC.tlog\\".
      InitializeBuildStatus:
        Creating "Debug\\CompilerIdC.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
        Touching "Debug\\CompilerIdC.tlog\\unsuccessfulbuild".
      ClCompile:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Tools\\MSVC\\14.41.34120\\bin\\HostX64\\x64\\CL.exe /c /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc143.pdb" /external:W0 /Gd /TC /FC /errorReport:queue CMakeCCompilerId.c
        CMakeCCompilerId.c
      Link:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Tools\\MSVC\\14.41.34120\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdC.exe" /INCREMENTAL:NO /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdC.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdC.lib" /MACHINE:X64 Debug\\CMakeCCompilerId.obj
        CompilerIdC.vcxproj -> D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-text-manager\\build\\CMakeFiles\\4.0.0-rc4\\CompilerIdC\\CompilerIdC.exe
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_C_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_C_COMPILER=C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Tools\\MSVC\\14.41.34120\\bin\\Hostx64\\x64\\cl.exe
      FinalizeBuildStatus:
        Deleting file "Debug\\CompilerIdC.tlog\\unsuccessfulbuild".
        Touching "Debug\\CompilerIdC.tlog\\CompilerIdC.lastbuildstate".
      Done Building Project "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-text-manager\\build\\CMakeFiles\\4.0.0-rc4\\CompilerIdC\\CompilerIdC.vcxproj" (default targets).
      
      Build succeeded.
          0 Warning(s)
          0 Error(s)
      
      Time Elapsed 00:00:00.65
      
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CompilerIdC.exe"
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CompilerIdC.vcxproj"
      
      The C compiler identification is MSVC, found in:
        D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-text-manager/build/CMakeFiles/4.0.0-rc4/CompilerIdC/CompilerIdC.exe
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "D:/kibisoft/workarea/git/interop/libs/translate/translate-csharp/CMakeLists.txt:3 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      0
      MSBuild version 17.11.9+a69bbaaf5 for .NET Framework
      Build started 5/17/2025 10:36:53 AM.
      
      Project "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-text-manager\\build\\CMakeFiles\\4.0.0-rc4\\CompilerIdCXX\\CompilerIdCXX.vcxproj" on node 1 (default targets).
      PrepareForBuild:
        Creating directory "Debug\\".
        Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
        Creating directory "Debug\\CompilerIdCXX.tlog\\".
      InitializeBuildStatus:
        Creating "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
        Touching "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild".
      ClCompile:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Tools\\MSVC\\14.41.34120\\bin\\HostX64\\x64\\CL.exe /c /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc143.pdb" /external:W0 /Gd /TP /FC /errorReport:queue CMakeCXXCompilerId.cpp
        CMakeCXXCompilerId.cpp
      Link:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Tools\\MSVC\\14.41.34120\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdCXX.exe" /INCREMENTAL:NO /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdCXX.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdCXX.lib" /MACHINE:X64 Debug\\CMakeCXXCompilerId.obj
        CompilerIdCXX.vcxproj -> D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-text-manager\\build\\CMakeFiles\\4.0.0-rc4\\CompilerIdCXX\\CompilerIdCXX.exe
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_CXX_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_CXX_COMPILER=C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Tools\\MSVC\\14.41.34120\\bin\\Hostx64\\x64\\cl.exe
      FinalizeBuildStatus:
        Deleting file "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild".
        Touching "Debug\\CompilerIdCXX.tlog\\CompilerIdCXX.lastbuildstate".
      Done Building Project "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-text-manager\\build\\CMakeFiles\\4.0.0-rc4\\CompilerIdCXX\\CompilerIdCXX.vcxproj" (default targets).
      
      Build succeeded.
          0 Warning(s)
          0 Error(s)
      
      Time Elapsed 00:00:00.44
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.exe"
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.vcxproj"
      
      The CXX compiler identification is MSVC, found in:
        D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-text-manager/build/CMakeFiles/4.0.0-rc4/CompilerIdCXX/CompilerIdCXX.exe
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:83 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "D:/kibisoft/workarea/git/interop/libs/translate/translate-csharp/CMakeLists.txt:3 (project)"
    checks:
      - "Detecting C compiler ABI info"
    directories:
      source: "D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-text-manager/build/CMakeFiles/CMakeScratch/TryCompile-v5f2nf"
      binary: "D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-text-manager/build/CMakeFiles/CMakeScratch/TryCompile-v5f2nf"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_POSITION_INDEPENDENT_CODE: "ON"
    buildResult:
      variable: "CMAKE_C_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-text-manager/build/CMakeFiles/CMakeScratch/TryCompile-v5f2nf'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_438f8.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.11.9+a69bbaaf5 for .NET Framework
        Build started 5/17/2025 10:36:54 AM.
        
        Project "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-text-manager\\build\\CMakeFiles\\CMakeScratch\\TryCompile-v5f2nf\\cmTC_438f8.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_438f8.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-text-manager\\build\\CMakeFiles\\CMakeScratch\\TryCompile-v5f2nf\\Debug\\".
          Creating directory "cmTC_438f8.dir\\Debug\\cmTC_438f8.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_438f8.dir\\Debug\\cmTC_438f8.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_438f8.dir\\Debug\\cmTC_438f8.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Tools\\MSVC\\14.41.34120\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_438f8.dir\\Debug\\\\" /Fd"cmTC_438f8.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-4.0\\Modules\\CMakeCCompilerABI.c"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.41.34120 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_438f8.dir\\Debug\\\\" /Fd"cmTC_438f8.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-4.0\\Modules\\CMakeCCompilerABI.c"
          CMakeCCompilerABI.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Tools\\MSVC\\14.41.34120\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-text-manager\\build\\CMakeFiles\\CMakeScratch\\TryCompile-v5f2nf\\Debug\\cmTC_438f8.exe" /INCREMENTAL /ILK:"cmTC_438f8.dir\\Debug\\cmTC_438f8.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-text-manager/build/CMakeFiles/CMakeScratch/TryCompile-v5f2nf/Debug/cmTC_438f8.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-text-manager/build/CMakeFiles/CMakeScratch/TryCompile-v5f2nf/Debug/cmTC_438f8.lib" /MACHINE:X64 cmTC_438f8.dir\\Debug\\CMakeCCompilerABI.obj
          cmTC_438f8.vcxproj -> D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-text-manager\\build\\CMakeFiles\\CMakeScratch\\TryCompile-v5f2nf\\Debug\\cmTC_438f8.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_438f8.dir\\Debug\\cmTC_438f8.tlog\\unsuccessfulbuild".
          Touching "cmTC_438f8.dir\\Debug\\cmTC_438f8.tlog\\cmTC_438f8.lastbuildstate".
        Done Building Project "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-text-manager\\build\\CMakeFiles\\CMakeScratch\\TryCompile-v5f2nf\\cmTC_438f8.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:00.40
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:227 (message)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "D:/kibisoft/workarea/git/interop/libs/translate/translate-csharp/CMakeLists.txt:3 (project)"
    message: |
      Parsed C implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|link\\.exe|lld-link(\\.exe)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|link\\.exe|lld-link(\\.exe)?))("|,| |$)]
        linker tool for 'C': C:/Program Files/Microsoft Visual Studio/2022/Professional/VC/Tools/MSVC/14.41.34120/bin/HostX64/x64/link.exe
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/Internal/CMakeDetermineLinkerId.cmake:36 (message)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:264 (cmake_determine_linker_id)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "D:/kibisoft/workarea/git/interop/libs/translate/translate-csharp/CMakeLists.txt:3 (project)"
    message: |
      Running the C compiler's linker: "C:/Program Files/Microsoft Visual Studio/2022/Professional/VC/Tools/MSVC/14.41.34120/bin/HostX64/x64/link.exe" "-v"
      Microsoft (R) Incremental Linker Version 14.41.34120.0
      Copyright (C) Microsoft Corporation.  All rights reserved.
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:83 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "D:/kibisoft/workarea/git/interop/libs/translate/translate-csharp/CMakeLists.txt:3 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-text-manager/build/CMakeFiles/CMakeScratch/TryCompile-2x2ks9"
      binary: "D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-text-manager/build/CMakeFiles/CMakeScratch/TryCompile-2x2ks9"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_CXX_SCAN_FOR_MODULES: "OFF"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_POSITION_INDEPENDENT_CODE: "ON"
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-text-manager/build/CMakeFiles/CMakeScratch/TryCompile-2x2ks9'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_d67a6.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.11.9+a69bbaaf5 for .NET Framework
        Build started 5/17/2025 10:36:55 AM.
        
        Project "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-text-manager\\build\\CMakeFiles\\CMakeScratch\\TryCompile-2x2ks9\\cmTC_d67a6.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_d67a6.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-text-manager\\build\\CMakeFiles\\CMakeScratch\\TryCompile-2x2ks9\\Debug\\".
          Creating directory "cmTC_d67a6.dir\\Debug\\cmTC_d67a6.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_d67a6.dir\\Debug\\cmTC_d67a6.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_d67a6.dir\\Debug\\cmTC_d67a6.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Tools\\MSVC\\14.41.34120\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /std:c++17 /Fo"cmTC_d67a6.dir\\Debug\\\\" /Fd"cmTC_d67a6.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-4.0\\Modules\\CMakeCXXCompilerABI.cpp"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.41.34120 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /std:c++17 /Fo"cmTC_d67a6.dir\\Debug\\\\" /Fd"cmTC_d67a6.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-4.0\\Modules\\CMakeCXXCompilerABI.cpp"
          CMakeCXXCompilerABI.cpp
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\VC\\Tools\\MSVC\\14.41.34120\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-text-manager\\build\\CMakeFiles\\CMakeScratch\\TryCompile-2x2ks9\\Debug\\cmTC_d67a6.exe" /INCREMENTAL /ILK:"cmTC_d67a6.dir\\Debug\\cmTC_d67a6.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-text-manager/build/CMakeFiles/CMakeScratch/TryCompile-2x2ks9/Debug/cmTC_d67a6.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-text-manager/build/CMakeFiles/CMakeScratch/TryCompile-2x2ks9/Debug/cmTC_d67a6.lib" /MACHINE:X64 cmTC_d67a6.dir\\Debug\\CMakeCXXCompilerABI.obj
          cmTC_d67a6.vcxproj -> D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-text-manager\\build\\CMakeFiles\\CMakeScratch\\TryCompile-2x2ks9\\Debug\\cmTC_d67a6.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_d67a6.dir\\Debug\\cmTC_d67a6.tlog\\unsuccessfulbuild".
          Touching "cmTC_d67a6.dir\\Debug\\cmTC_d67a6.tlog\\cmTC_d67a6.lastbuildstate".
        Done Building Project "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-text-manager\\build\\CMakeFiles\\CMakeScratch\\TryCompile-2x2ks9\\cmTC_d67a6.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:00.42
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:227 (message)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "D:/kibisoft/workarea/git/interop/libs/translate/translate-csharp/CMakeLists.txt:3 (project)"
    message: |
      Parsed CXX implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|link\\.exe|lld-link(\\.exe)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|link\\.exe|lld-link(\\.exe)?))("|,| |$)]
        linker tool for 'CXX': C:/Program Files/Microsoft Visual Studio/2022/Professional/VC/Tools/MSVC/14.41.34120/bin/HostX64/x64/link.exe
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/Internal/CMakeDetermineLinkerId.cmake:36 (message)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake:264 (cmake_determine_linker_id)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "D:/kibisoft/workarea/git/interop/libs/translate/translate-csharp/CMakeLists.txt:3 (project)"
    message: |
      Running the CXX compiler's linker: "C:/Program Files/Microsoft Visual Studio/2022/Professional/VC/Tools/MSVC/14.41.34120/bin/HostX64/x64/link.exe" "-v"
      Microsoft (R) Incremental Linker Version 14.41.34120.0
      Copyright (C) Microsoft Corporation.  All rights reserved.
...
