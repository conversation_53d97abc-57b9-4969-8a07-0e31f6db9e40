/*
 * Copyright 2021-present Alcon. All rights reserved.
 * Alcon proprietary/confidential.
 * Use is subject to license terms.
 */

using System;
using System.Linq;

namespace LocalizationLib;

/// <summary>
/// Factory class for creating LLM clients based on configuration.
/// Supports multiple LLM providers including Local LLM and Amazon Bedrock.
/// </summary>
public static class LlmClientFactory
{
    /// <summary>
    /// Creates an appropriate LLM client based on the configuration.
    /// </summary>
    /// <param name="config">The LLM configuration</param>
    /// <returns>An ILlmClient implementation</returns>
    /// <exception cref="ArgumentNullException">Thrown when config is null</exception>
    /// <exception cref="InvalidOperationException">Thrown when provider is not supported or configuration is invalid</exception>
    public static ILlmClient CreateLlmClient(LLMConfig config)
    {
        if (config == null)
        {
            throw new ArgumentNullException(nameof(config));
        }

        if (string.IsNullOrEmpty(config.llmProvider))
        {
            throw new InvalidOperationException("llmProvider is not specified in configuration.");
        }

        return config.llmProvider.ToLowerInvariant() switch
        {
            "localllmprovider" or "local" => CreateLocalLlmClient(config),
            "bedrockprovider" or "bedrock" => CreateBedrockLlmClient(config),
            _ => throw new InvalidOperationException($"Unsupported LLM provider: {config.llmProvider}. Supported providers are: LocalLlmProvider, BedrockProvider")
        };
    }

    /// <summary>
    /// Creates a Local LLM client.
    /// </summary>
    /// <param name="config">The LLM configuration</param>
    /// <returns>A LocalLlmClient instance</returns>
    /// <exception cref="InvalidOperationException">Thrown when local LLM configuration is missing or invalid</exception>
    private static ILlmClient CreateLocalLlmClient(LLMConfig config)
    {
        if (config.localLlmProviderConfig == null)
        {
            throw new InvalidOperationException("localLlmProviderConfig is required for LocalLlmProvider but was not found in configuration.");
        }

        ValidateLocalLlmConfig(config.localLlmProviderConfig);
        
        return new LocalLlmClient(config);
    }

    /// <summary>
    /// Creates a Bedrock LLM client.
    /// </summary>
    /// <param name="config">The LLM configuration</param>
    /// <returns>A BedrockLlmClient instance</returns>
    /// <exception cref="InvalidOperationException">Thrown when Bedrock configuration is missing or invalid</exception>
    private static ILlmClient CreateBedrockLlmClient(LLMConfig config)
    {
        if (config.bedrockProviderConfig == null)
        {
            throw new InvalidOperationException("bedrockProviderConfig is required for BedrockProvider but was not found in configuration.");
        }

        ValidateBedrockConfig(config.bedrockProviderConfig);
        
        return new BedrockLlmClient(config);
    }

    /// <summary>
    /// Validates the Local LLM configuration.
    /// </summary>
    /// <param name="config">The local LLM configuration to validate</param>
    /// <exception cref="InvalidOperationException">Thrown when configuration is invalid</exception>
    private static void ValidateLocalLlmConfig(LocalLlmProviderConfig config)
    {
        if (string.IsNullOrEmpty(config.serviceUrl))
        {
            throw new InvalidOperationException("serviceUrl is required for LocalLlmProvider.");
        }

        if (string.IsNullOrEmpty(config.modelId))
        {
            throw new InvalidOperationException("modelId is required for LocalLlmProvider.");
        }

        if (config.timeoutSeconds <= 0)
        {
            throw new InvalidOperationException("timeoutSeconds must be greater than 0.");
        }

        if (config.maxRetries < 0)
        {
            throw new InvalidOperationException("maxRetries must be greater than or equal to 0.");
        }

        if (config.chunkSize <= 0)
        {
            throw new InvalidOperationException("chunkSize must be greater than 0.");
        }

        // Validate URL format
        if (!Uri.TryCreate(config.serviceUrl, UriKind.Absolute, out var uri) || 
            (uri.Scheme != "http" && uri.Scheme != "https"))
        {
            throw new InvalidOperationException("serviceUrl must be a valid HTTP or HTTPS URL.");
        }
    }

    /// <summary>
    /// Validates the Bedrock configuration.
    /// </summary>
    /// <param name="config">The Bedrock configuration to validate</param>
    /// <exception cref="InvalidOperationException">Thrown when configuration is invalid</exception>
    private static void ValidateBedrockConfig(BedrockProviderConfig config)
    {
        if (string.IsNullOrEmpty(config.region))
        {
            throw new InvalidOperationException("region is required for BedrockProvider.");
        }

        if (string.IsNullOrEmpty(config.modelId))
        {
            throw new InvalidOperationException("modelId is required for BedrockProvider.");
        }

        if (config.timeoutSeconds <= 0)
        {
            throw new InvalidOperationException("timeoutSeconds must be greater than 0.");
        }

        if (config.maxRetries < 0)
        {
            throw new InvalidOperationException("maxRetries must be greater than or equal to 0.");
        }

        if (config.chunkSize <= 0)
        {
            throw new InvalidOperationException("chunkSize must be greater than 0.");
        }

        if (config.maxTokens <= 0)
        {
            throw new InvalidOperationException("maxTokens must be greater than 0.");
        }

        // Validate temperature range
        if (config.temperature < 0.0 || config.temperature > 1.0)
        {
            throw new InvalidOperationException("temperature must be between 0.0 and 1.0.");
        }

        // Validate region format (basic check)
        if (!IsValidAwsRegion(config.region))
        {
            throw new InvalidOperationException($"Invalid AWS region format: {config.region}. Expected format like 'us-east-1'. Ensure Bedrock is available in the specified region.");
        }

        // Validate model ID format
        if (!IsValidModelId(config.modelId))
        {
            throw new InvalidOperationException($"Invalid model ID format: {config.modelId}. Expected format like 'anthropic.claude-3-sonnet-20240229-v1:0'. Ensure the model is supported and accessible.");
        }

        // Validate credentials configuration - REST API requires explicit credentials
        ValidateBedrockCredentials(config);
    }

    /// <summary>
    /// Validates Bedrock credentials configuration.
    /// For REST API access, credentials can be provided in config or environment variables.
    /// </summary>
    /// <param name="config">The Bedrock configuration</param>
    /// <exception cref="InvalidOperationException">Thrown when credentials configuration is invalid</exception>
    private static void ValidateBedrockCredentials(BedrockProviderConfig config)
    {
        bool hasConfigAccessKey = !string.IsNullOrEmpty(config.accessKey);
        bool hasConfigSecretKey = !string.IsNullOrEmpty(config.secretKey);
        bool hasConfigSessionToken = !string.IsNullOrEmpty(config.sessionToken);

        // Check environment variables as fallback
        bool hasEnvAccessKey = !string.IsNullOrEmpty(Environment.GetEnvironmentVariable("AWS_ACCESS_KEY_ID"));
        bool hasEnvSecretKey = !string.IsNullOrEmpty(Environment.GetEnvironmentVariable("AWS_SECRET_ACCESS_KEY"));

        // Either config or environment variables must provide credentials
        bool hasAccessKey = hasConfigAccessKey || hasEnvAccessKey;
        bool hasSecretKey = hasConfigSecretKey || hasEnvSecretKey;

        if (!hasAccessKey || !hasSecretKey)
        {
            throw new InvalidOperationException("AWS credentials are required for Bedrock REST API access. Provide them in configuration (accessKey/secretKey) or set environment variables (AWS_ACCESS_KEY_ID/AWS_SECRET_ACCESS_KEY).");
        }

        // Session token validation (only applies if provided in config)
        if (hasConfigSessionToken && (!hasConfigAccessKey || !hasConfigSecretKey))
        {
            throw new InvalidOperationException("AWS credentials are required for Bedrock REST API access");
        }
    }

    /// <summary>
    /// Validates AWS region format.
    /// </summary>
    /// <param name="region">The region to validate</param>
    /// <returns>True if the region format is valid</returns>
    private static bool IsValidAwsRegion(string region)
    {
        if (string.IsNullOrEmpty(region))
            return false;

        // Basic validation for AWS region format (e.g., us-east-1, eu-west-1)
        // This is a simple check and doesn't validate if the region actually exists
        var parts = region.Split('-');
        return parts.Length >= 3 &&
               parts[0].Length >= 2 &&
               parts[1].Length >= 4 &&
               parts[2].Length >= 1;
    }

    /// <summary>
    /// Validates Bedrock model ID format.
    /// </summary>
    /// <param name="modelId">The model ID to validate</param>
    /// <returns>True if the model ID format is valid</returns>
    private static bool IsValidModelId(string modelId)
    {
        if (string.IsNullOrEmpty(modelId))
            return false;

        // Check for known model ID patterns
        var knownProviders = new[] { "anthropic.", "amazon.", "meta.", "mistral.", "ai21.", "cohere." };
        return knownProviders.Any(provider => modelId.StartsWith(provider, StringComparison.OrdinalIgnoreCase));
    }

    /// <summary>
    /// Gets a list of supported LLM providers.
    /// </summary>
    /// <returns>Array of supported provider names</returns>
    public static string[] GetSupportedProviders()
    {
        return new[] { "LocalLlmProvider", "BedrockProvider" };
    }

    /// <summary>
    /// Checks if a provider is supported.
    /// </summary>
    /// <param name="providerName">The provider name to check</param>
    /// <returns>True if the provider is supported</returns>
    public static bool IsProviderSupported(string providerName)
    {
        if (string.IsNullOrEmpty(providerName))
            return false;

        return providerName.ToLowerInvariant() switch
        {
            "localllmprovider" or "local" => true,
            "bedrockprovider" or "bedrock" => true,
            _ => false
        };
    }
}
