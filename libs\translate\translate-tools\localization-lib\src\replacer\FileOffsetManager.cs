/*
 * Copyright 2021-present Alcon. All rights reserved.
 * Alcon proprietary/confidential.
 * Use is subject to license terms.
 */

using System.Collections.Generic;
using System.Linq;

namespace LocalizationLib;

/// <summary>
/// Keeps track of line/column offsets in a file
/// </summary>
public class FileOffsetManager
{
    private Dictionary<string, List<KeyValuePair<int, int>>> _fileOffsets = new Dictionary<string, List<KeyValuePair<int, int>>>(); // maps filePath with a list of offsets per specific lineNumber
    private Dictionary<string, LineOffset> _lineOffsets = new Dictionary<string, LineOffset>(); // maps filePath with LineOffsetManager

    /// <summary>
    /// Adds column offset per filePath - lineNumber
    /// </summary>
    /// <param name="filePath"></param>
    /// <param name="lineIndex"></param>
    /// <param name="columnOffset"></param>
    public void AddColumnOffset(string filePath, int lineIndex, int columnOffset)
    {
        if (_lineOffsets.ContainsKey(filePath))
        {
            if (_lineOffsets[filePath].ContainsKey(lineIndex))
            {
                _lineOffsets[filePath].Add(lineIndex, _lineOffsets[filePath].Get(lineIndex) + columnOffset);
            }
            else
            {
                _lineOffsets[filePath].Add(lineIndex, columnOffset);
            }
        }
        else
        {
            LineOffset lineOffsetManager = new LineOffset();
            lineOffsetManager.Add(lineIndex, columnOffset);
            _lineOffsets.Add(filePath, lineOffsetManager);
        }
    }

    /// <summary>
    /// Adds line offset per file
    /// </summary>
    /// <param name="filePath"></param>
    /// <param name="lineNumber"></param>
    /// <param name="v"></param>
    public void AddLineOffset(string filePath, int lineNumber, int v)
    {
        if (_fileOffsets.ContainsKey(filePath))
        {
            _fileOffsets[filePath].Add(new KeyValuePair<int, int>(lineNumber, v));
        }
        else
        {
            List<KeyValuePair<int, int>> list = [new KeyValuePair<int, int>(lineNumber, v)];
            _fileOffsets.Add(filePath, list);
        }
    }

    /// <summary>
    /// Gets the column offset per line in a file
    /// </summary>
    /// <param name="filePath"></param>
    /// <param name="lineNumber"></param>
    /// <returns></returns>
    public int ColumnOffset(string filePath, int lineNumber)
    {
        if (_lineOffsets.TryGetValue(filePath, out var lineOffset))
        {
            return lineOffset.Get(lineNumber);
        }
        return 0;
    }

    /// <summary>
    /// Gets the line offset per file, considering the current lineNumber
    /// </summary>
    /// <param name="filePath"></param>
    /// <param name="lineNumber"></param>
    /// <returns>The offset for the specified lineNumber inside file</returns>
    public int LineOffset(string filePath, int lineNumber)
    {
        int offset = 0;
        if (_fileOffsets.ContainsKey(filePath))
        {
            foreach (var lineOffset in _fileOffsets[filePath])
            {
                if (lineOffset.Key < lineNumber)
                {
                    offset += lineOffset.Value;
                }
            }
        }
        return offset;
    }

    /// <summary>
    /// Computes column offset for originalText on line
    /// </summary>
    /// <param name="filePath"></param>
    /// <param name="lineText"></param>
    /// <param name="originalText"></param>
    /// <param name="lineNumber"></param>
    /// <param name="columnIndex"></param>
    /// <param name="idx"></param>
    public void ComputeColumnOffset(string filePath, string lineText, string originalText, int lineNumber, int columnIndex, int idx)
    {
        int columnOffset = lineText.IndexOf(originalText) - columnIndex;
        int i = idx;
        while (columnOffset < 0 && (i = lineText.IndexOf(originalText, i)) != -1)
        {
            columnOffset = i - columnIndex;
            i += originalText.Length;
        }
        AddColumnOffset(filePath, lineNumber, columnOffset);
    }
}

public class LineOffset
{
    private Dictionary<int, int> _lineOffsets = new Dictionary<int, int>();

    public void Add(int key, int value)
    {
        _lineOffsets[key] = value;
    }
    public int Get(int key)
    {
        return _lineOffsets.TryGetValue(key, out var value) ? value : 0;
    }

    public bool ContainsKey(int key)
    {
        return _lineOffsets.ContainsKey(key);
    }

    public override string ToString()
    {
        return string.Join(", ", _lineOffsets.Select(kvp => $"{kvp.Key}: {kvp.Value}"));
    }
}