/*
 * Copyright 2021-present Alcon. All rights reserved.
 * Alcon proprietary/confidential.
 * Use is subject to license terms.
 */

using NUnit.Framework;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;

namespace LocalizationLib.Tests;

[TestFixture]
public class ConsolidatedCppProcessorTests
{
    private ConsolidatedCppProcessor _processor;
    private string _tempFilePath;
    private MockLlmClient _mockLlmClient;

    [SetUp]
    public void Setup()
    {
        _mockLlmClient = new MockLlmClient();
        _tempFilePath = Path.GetTempFileName();
        var tempDir = Path.GetDirectoryName(_tempFilePath);

        // Initialize the config manager for caching
        ProjectTextExtractorConfigManager.Initialize(tempDir);

        // Initialize processor with project path
        _processor = new ConsolidatedCppProcessor(projectPath: tempDir, llmClient: _mockLlmClient);
    }

    [TearDown]
    public void TearDown()
    {
        if (File.Exists(_tempFilePath))
        {
            File.Delete(_tempFilePath);
        }

        // Cleanup config manager
        ProjectTextExtractorConfigManager.Reset();
        
        // Clean up the config file if it exists
        var configPath = Path.Combine(Path.GetDirectoryName(_tempFilePath), "ProjectTextExtractorConfig.json");
        if (File.Exists(configPath))
        {
            File.Delete(configPath);
        }
    }

    [Test]
    public void ProcessCppFile_WithoutLlmClient_ReturnsAllStrings()
    {
        // Arrange
        var tempDir = Path.GetDirectoryName(_tempFilePath);
        var processor = new ConsolidatedCppProcessor(projectPath: tempDir); // No LLM client but with project path
        var content = @"
            const char* str1 = ""Hello"";
            const wchar_t* str2 = L""World"";
        ";
        File.WriteAllText(_tempFilePath, content);

        // Act
        var result = processor.ProcessCppFile(_tempFilePath, "test-group");

        // Assert
        Assert.That(result.Replacements.Count, Is.EqualTo(2), "Should return all strings without LLM verification");
        Assert.That(result.Replacements.Select(r => r.OriginalValue), Is.EquivalentTo(new[]
        {
            @"""Hello""",
            @"L""World"""
        }));
    }

    [Test]
    public void ProcessCppFile_WithLlmClient_ReturnsOnlyVerifiedStrings()
    {
        // Arrange
        var content = @"
            const char* str1 = ""Hello"";
            const wchar_t* str2 = L""World"";
            const char* str3 = ""LOG_DEBUG"";
        ";
        File.WriteAllText(_tempFilePath, content);

        // Configure mock LLM client
        _mockLlmClient.SetupVerification(@"""Hello""", true, "User-facing greeting");
        _mockLlmClient.SetupVerification(@"L""World""", true, "User-facing text");
        _mockLlmClient.SetupVerification(@"""LOG_DEBUG""", false, "Logging macro");

        // Act
        var result = _processor.ProcessCppFile(_tempFilePath, "test-group");

        // Assert
        Assert.That(result.Replacements.Count, Is.EqualTo(2), "Should only return strings that need translation");
        Assert.That(result.Replacements.Select(r => r.OriginalValue), Is.EquivalentTo(new[]
        {
            @"""Hello""",
            @"L""World"""
        }));

        // Verify LLM verification details are added to notes
        var helloRecord = result.Replacements.First(r => r.OriginalValue == @"""Hello""");
        Assert.That(helloRecord.Notes.Any(n => n.Contains("LLM Verification: Needs Translation")), Is.True);
        Assert.That(helloRecord.Notes.Any(n => n.Contains("LLM Reason: User-facing greeting")), Is.True);
    }

    [Test]
    public void ProcessCppFile_WithLlmClient_HandlesStringTypes()
    {
        // Arrange
        var content = @"
            const char* str1 = ""Basic string"";
            const wchar_t* str2 = L""Wide string"";
            const char8_t* str3 = u8""UTF-8 string"";
            const char16_t* str4 = u""UTF-16 string"";
            const char32_t* str5 = U""UTF-32 string"";
        ";
        File.WriteAllText(_tempFilePath, content);

        // Configure mock LLM client to verify all strings
        foreach (var str in new[] { @"""Basic string""", @"L""Wide string""", @"u8""UTF-8 string""", @"u""UTF-16 string""", @"U""UTF-32 string""" })
        {
            _mockLlmClient.SetupVerification(str, true, "User-facing text");
        }

        // Act
        var result = _processor.ProcessCppFile(_tempFilePath, "test-group");

        // Assert
        Assert.That(result.Replacements.Count, Is.EqualTo(5), "Should handle all string types");
        var types = result.Replacements.Select(r => _processor.GetStringType(r)).ToList();
        Assert.That(types, Is.EquivalentTo(new[]
        {
            "char*",
            "wchar_t*",
            "char8_t*",
            "char16_t*",
            "char32_t*"
        }));
    }

    [Test]
    public void ProcessCppFile_WithLlmClient_HandlesRawStrings()
    {
        // Arrange
        var content = @"
            const char* raw1 = R""(Raw string)"";
            const char* raw2 = R""delim(Raw string with delimiter)delim"";
        ";
        File.WriteAllText(_tempFilePath, content);

        // Configure mock LLM client
        _mockLlmClient.SetupVerification(@"R""(Raw string)""", true, "User-facing raw string");
        _mockLlmClient.SetupVerification(@"R""delim(Raw string with delimiter)delim""", true, "User-facing raw string with delimiter");

        // Act
        var result = _processor.ProcessCppFile(_tempFilePath, "test-group");

        // Assert
        Assert.That(result.Replacements.Count, Is.EqualTo(2), "Should handle raw strings");
        Assert.That(result.Replacements.All(r => _processor.GetStringType(r) == "char*"), Is.True);
    }

    [Test]
    public void ProcessCppFile_WithLlmClient_HandlesContext()
    {
        // Arrange
        var content = @"
            // User message
            const char* str1 = ""Hello"";
            
            // Logging message
            LOG_INFO(""Debug info"");
            
            // UI label
            const wchar_t* str2 = L""Click me"";
        ";
        File.WriteAllText(_tempFilePath, content);

        // Configure mock LLM client
        _mockLlmClient.SetupVerification(@"""Hello""", true, "User-facing message in comment context");
        _mockLlmClient.SetupVerification(@"""Debug info""", false, "Logging message");
        _mockLlmClient.SetupVerification(@"L""Click me""", true, "UI label");

        // Act
        var result = _processor.ProcessCppFile(_tempFilePath, "test-group");

        // Assert
        Assert.That(result.Replacements.Count, Is.EqualTo(2), "Should consider context in verification");
        Assert.That(result.Replacements.Select(r => r.OriginalValue), Is.EquivalentTo(new[]
        {
            @"""Hello""",
            @"L""Click me"""
        }));
    }

    [Test]
    public void ProcessCppFile_ValidatesStringLocation()
    {
        // Arrange
        var content = @"
            const char* str1 = ""Hello"";
            const char* str2 = ""World"";  // Different column position
        ";
        File.WriteAllText(_tempFilePath, content);

        // Configure mock LLM client
        _mockLlmClient.SetupVerification(@"""Hello""", true, "User-facing text");
        _mockLlmClient.SetupVerification(@"""World""", true, "User-facing text");

        // Act
        var result = _processor.ProcessCppFile(_tempFilePath, "test-group");

        // Assert
        Assert.That(result.Replacements.Count, Is.EqualTo(2), "Should validate and return both strings");
        var helloRecord = result.Replacements.First(r => r.OriginalValue == @"""Hello""");
        var worldRecord = result.Replacements.First(r => r.OriginalValue == @"""World""");
        
        // Verify column positions are adjusted to opening quotes
        Assert.That(helloRecord.Column, Is.EqualTo(32), "Column should point to opening quote");
        Assert.That(worldRecord.Column, Is.EqualTo(32), "Column should point to opening quote");
    }

    /// <summary>
    /// Validates that multi-line strings are not processed correctly,
    /// Currently this is not supported by the processor.
    /// </summary>
    public void ProcessCppFile_ValidatesMultiLineStringLocation()
    {
        // Arrange
        var content = @"
            const char* multi = ""Line 1
            Line 2
            Line 3"";
        ";
        File.WriteAllText(_tempFilePath, content);

        // Configure mock LLM client
        _mockLlmClient.SetupVerification(@"""Line 1\nLine 2\nLine 3""", true, "Multi-line user text");

        // Act
        var result = _processor.ProcessCppFile(_tempFilePath, "test-group");

        // Assert
        Assert.That(result.Replacements.Count, Is.EqualTo(1), "Should validate and return multi-line string");
        var record = result.Replacements.First();
        Assert.That(record.LineContent, Does.Contain("Line 1"), "LineContent should include all lines");
        Assert.That(record.LineContent, Does.Contain("Line 2"), "LineContent should include all lines");
        Assert.That(record.LineContent, Does.Contain("Line 3"), "LineContent should include all lines");
        Assert.That(record.Column, Is.EqualTo(25), "Column should point to opening quote");
    }

    [Test]
    public void ProcessCppFile_HandlesEscapedCharacters()
    {
        // Arrange
        var content = @"
            const char* str1 = ""Hello\nWorld"";
            const char* str2 = ""Path\\to\\file"";
        ";
        File.WriteAllText(_tempFilePath, content);

        // Configure mock LLM client
        _mockLlmClient.SetupVerification(@"""Hello\nWorld""", true, "User text with newline");
        _mockLlmClient.SetupVerification(@"""Path\\to\\file""", true, "User text with backslashes");

        // Act
        var result = _processor.ProcessCppFile(_tempFilePath, "test-group");

        // Assert
        Assert.That(result.Replacements.Count, Is.EqualTo(2), "Should handle escaped characters");
        Assert.That(result.Replacements.All(r => r.Column == 32), "All columns should point to opening quotes");
    }

    [Test]
    public void ProcessCppFile_UsesCachedVerificationResults()
    {
        // Arrange
        var content = @"
            const char* str1 = ""Hello"";
            const char* str2 = ""Hello"";  // Same string, should use cache
        ";
        File.WriteAllText(_tempFilePath, content);

        // Configure mock LLM client for first occurrence
        _mockLlmClient.SetupVerification(@"""Hello""", true, "User-facing text");

        // Act
        var result = _processor.ProcessCppFile(_tempFilePath, "test-group");

        // Assert
        Assert.That(result.Replacements.Count, Is.EqualTo(2), "Should return both strings");
        Assert.That(result.Replacements.All(r => r.Notes.Any(n => n.Contains("LLM Verification (Cached)"))), 
            Is.False, "First occurrence should not be cached");
        
        // Process again to test caching
        var result2 = _processor.ProcessCppFile(_tempFilePath, "test-group");
        Assert.That(result2.Replacements.All(r => r.Notes.Any(n => n.Contains("LLM Verification (Cached)"))), 
            Is.True, "Second occurrence should use cache");
    }

    [Test]
    public void ProcessCppFile_HandlesFileContextChunking()
    {
        // Arrange
        var content = new StringBuilder();
        // Add 200 lines of context
        for (int i = 0; i < 100; i++)
        {
            content.AppendLine($"// Context line {i}");
        }
        content.AppendLine(@"const char* str = ""Hello"";");
        for (int i = 0; i < 100; i++)
        {
            content.AppendLine($"// Context line {i + 100}");
        }
        File.WriteAllText(_tempFilePath, content.ToString());

        // Configure mock LLM client
        _mockLlmClient.SetupVerification(@"""Hello""", true, "User-facing text");

        // Act
        var result = _processor.ProcessCppFile(_tempFilePath, "test-group");

        // Assert
        Assert.That(result.Replacements.Count, Is.EqualTo(1), "Should process string with chunked context");
        var record = result.Replacements.First();
        Assert.That(record.Notes.Any(n => n.Contains("LLM Context")), Is.True, "Should include context in notes");
    }

    [Test]
    public void ProcessCppFile_HandlesInvalidLocations()
    {
        // Arrange
        var content = @"
            // Invalid line number
            const char* str1 = ""Hello"";
        ";
        File.WriteAllText(_tempFilePath, content);

        // Create a record with invalid line number
        var invalidRecord = new ReplacementRecord
        {
            FilePath = _tempFilePath,
            LineNumber = 999,  // Invalid line number
            Column = 1,
            OriginalValue = @"""Hello"""
        };

        // Act & Assert
        Assert.That(() => _processor.ProcessCppFile(_tempFilePath, "test-group"),
            Throws.Nothing, "Should handle invalid line numbers gracefully");
    }

    [Test]
    public void ProcessCppFile_HandlesFileAccessErrors()
    {
        // Arrange
        var nonExistentFile = Path.Combine(Path.GetTempPath(), "non_existent.cpp");
        
        // Act & Assert
        var ex = Assert.Throws<FileNotFoundException>(() => _processor.ProcessCppFile(nonExistentFile, "test-group"),
            "Should throw FileNotFoundException for non-existent files");
        Assert.That(ex.Message, Does.Contain("C++ file not found"), "Exception message should indicate file not found");
    }
}

/// <summary>
/// Mock LLM client for testing
/// </summary>
public class MockLlmClient : ILlmClient
{
    private readonly Dictionary<string, TranslationVerificationResult> _verificationResults = new();

    public void SetupVerification(string text, bool needsTranslation, string reason)
    {
        _verificationResults[text] = new TranslationVerificationResult
        {
            NeedsTranslation = needsTranslation,
            Reason = reason,
            Context = "Test context"
        };
    }

    public TranslationVerificationResult VerifyCppTranslationNeeded(
        string text,
        string stringType,
        int lineNumber,
        string lineContent,
        string fileContent,
        string filePath,
        string groupId)
    {
        if (_verificationResults.TryGetValue(text, out var result))
        {
            return new TranslationVerificationResult
            {
                NeedsTranslation = result.NeedsTranslation,
                Reason = result.Reason,
                Context = result.Context,
                LineNumber = lineNumber,
                LineContent = lineContent,
                FilePath = filePath,
                GroupId = groupId
            };
        }

        return new TranslationVerificationResult
        {
            NeedsTranslation = false,
            Reason = "No mock verification result found",
            Context = "Test context",
            LineNumber = lineNumber,
            LineContent = lineContent,
            FilePath = filePath,
            GroupId = groupId
        };
    }

    // Implement other interface methods with default/empty implementations
    public List<ReplacementRecord> DiscoverAndProposeReplacements(string fileContent, string groupId, string fileName, string filePath)
        => new List<ReplacementRecord>();

    public TranslationVerificationResult VerifyTranslationNeeded(string text, int lineNumber, string lineContent, string fileContent, string groupId, string filePath)
        => new TranslationVerificationResult { NeedsTranslation = false, Reason = "Not implemented in mock" };

    public TranslationVerificationResult VerifyXamlTranslationNeeded(string text, string propertyName, int lineNumber, string lineContent, string xamlContent, string filePath, string groupId)
        => new TranslationVerificationResult { NeedsTranslation = false, Reason = "Not implemented in mock" };

    public LLMConfig GetConfig()
        => new LLMConfig
        {
            llmProvider = "MockProvider",
            promptTemplate = "Mock template",
            localLlmProviderConfig = new LocalLlmProviderConfig
            {
                serviceUrl = "mock://localhost",
                modelId = "mock-model",
                temperature = 0.0,
                maxRetries = 1,
                timeoutSeconds = 1,
                chunkSize = 100,
                useChunking = false
            }
        };
} 