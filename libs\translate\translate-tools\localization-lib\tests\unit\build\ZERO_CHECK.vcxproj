﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <PropertyGroup>
    <ResolveNugetPackages>false</ResolveNugetPackages>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{4CCA6B49-B9A4-3195-B68D-768F5CC142CD}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <DisableFastUpToDateCheck>true</DisableFastUpToDateCheck>
    <WindowsTargetPlatformVersion>10.0.22621.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>ZERO_CHECK</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Midl>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PreBuildEvent>
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message>Checking File Globs</Message>
      <Command>setlocal
"C:\Program Files\CMake\bin\cmake.exe" -P D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-lib/tests/unit/build/CMakeFiles/VerifyGlobs.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PreBuildEvent>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Midl>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PreBuildEvent>
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message>Checking File Globs</Message>
      <Command>setlocal
"C:\Program Files\CMake\bin\cmake.exe" -P D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-lib/tests/unit/build/CMakeFiles/VerifyGlobs.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PreBuildEvent>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\CMakeFiles\a1f12246291dcfb468cac735aa77eee1\generate.stamp.rule">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <BuildInParallel Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</BuildInParallel>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Checking Build System</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SD:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-lib/tests/unit -BD:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-lib/tests/unit/build --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-lib/tests/unit/build/localizationlib_unit_tests.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeCSharpInformation.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeGenericSystem.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\FindGit.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Windows-Initialize.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Windows.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\WindowsPaths.cmake;D:\kibisoft\workarea\git\interop\cmake\Globals.cmake;D:\kibisoft\workarea\git\interop\cmake\VsPackageReferences.cmake;D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\CMakeLists.txt;D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\CMakeLists.txt;D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\CMakeFiles\4.0.0-rc4\CMakeCSharpCompiler.cmake;D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\CMakeFiles\4.0.0-rc4\CMakeSystem.cmake;D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\CMakeFiles\cmake.verify_globs;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\CMakeFiles\generate.stamp;D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\localizationlib\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <BuildInParallel Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</BuildInParallel>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Checking Build System</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SD:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-lib/tests/unit -BD:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-lib/tests/unit/build --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-lib/tests/unit/build/localizationlib_unit_tests.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeCSharpInformation.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeGenericSystem.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\FindGit.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Windows-Initialize.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Windows.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\WindowsPaths.cmake;D:\kibisoft\workarea\git\interop\cmake\Globals.cmake;D:\kibisoft\workarea\git\interop\cmake\VsPackageReferences.cmake;D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\CMakeLists.txt;D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\CMakeLists.txt;D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\CMakeFiles\4.0.0-rc4\CMakeCSharpCompiler.cmake;D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\CMakeFiles\4.0.0-rc4\CMakeSystem.cmake;D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\CMakeFiles\cmake.verify_globs;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\CMakeFiles\generate.stamp;D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\localizationlib\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
  </ItemGroup>
  <ItemGroup />
  <ItemGroup>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>