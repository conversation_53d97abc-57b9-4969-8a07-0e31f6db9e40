
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.30/Modules/CMakeDetermineSystem.cmake:205 (message)"
      - "CMakeLists.txt:3 (project)"
    message: |
      The system is: Windows - 10.0.22631 - AMD64
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-3.30/Modules/CMakeDetermineCSharpCompiler.cmake:23 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Compiling the CSharp compiler identification source file "CMakeCSharpCompilerId.cs" succeeded.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      0
      MSBuild version 17.11.9+a69bbaaf5 for .NET Framework
      Build started 3/10/2025 3:00:54 PM.
      
      Project "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-extractor\\build\\CMakeFiles\\3.30.1\\CompilerIdCSharp\\CompilerIdCSharp.csproj" on node 1 (default targets).
      PrepareForBuild:
        Creating directory "obj\\x64\\Debug\\".
      CoreCompile:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Current\\Bin\\Roslyn\\csc.exe /noconfig /nowarn:1701,1702 /fullpaths /nostdlib+ /platform:anycpu32bitpreferred /define:Platformx64;PlatformToolsetv143 /highentropyva+ /reference:"C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\mscorlib.dll" /reference:"C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Core.dll" /debug+ /out:obj\\x64\\Debug\\CompilerIdCSharp.exe /subsystemversion:6.00 /target:exe /utf8output /langversion:7.3 CMakeCSharpCompilerId.cs "obj\\x64\\Debug\\.NETFramework,Version=v4.7.2.AssemblyAttributes.cs"
        CompilerServer: server - server processed compilation - CompilerIdCSharp
      CopyFilesToOutputDirectory:
        Copying file from "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-extractor\\build\\CMakeFiles\\3.30.1\\CompilerIdCSharp\\obj\\x64\\Debug\\CompilerIdCSharp.exe" to "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-extractor\\build\\CMakeFiles\\3.30.1\\CompilerIdCSharp\\CompilerIdCSharp.exe".
        CompilerIdCSharp -> D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-extractor\\build\\CMakeFiles\\3.30.1\\CompilerIdCSharp\\CompilerIdCSharp.exe
        Copying file from "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-extractor\\build\\CMakeFiles\\3.30.1\\CompilerIdCSharp\\obj\\x64\\Debug\\CompilerIdCSharp.pdb" to "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-extractor\\build\\CMakeFiles\\3.30.1\\CompilerIdCSharp\\CompilerIdCSharp.pdb".
      PostBuildEvent:
        if not "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Current\\Bin\\Roslyn"=="" if exist "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Current\\Bin\\Roslyn\\csc.exe" set _CSC=C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Current\\Bin\\Roslyn
        if exist "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Current\\Bin\\amd64\\csc.exe" set _CSC=C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Current\\Bin\\amd64
        if "%_CSC%"=="" exit -1
        @echo CMAKE_CSharp_COMPILER=%_CSC%\\csc.exe
        CMAKE_CSharp_COMPILER=C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Current\\Bin\\Roslyn\\csc.exe
      Done Building Project "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-extractor\\build\\CMakeFiles\\3.30.1\\CompilerIdCSharp\\CompilerIdCSharp.csproj" (default targets).
      
      Build succeeded.
          0 Warning(s)
          0 Error(s)
      
      Time Elapsed 00:00:00.19
      
      
      Compilation of the CSharp compiler identification source "CMakeCSharpCompilerId.cs" produced "CompilerIdCSharp.csproj"
      
      Compilation of the CSharp compiler identification source "CMakeCSharpCompilerId.cs" produced "CompilerIdCSharp.exe"
      
      Compilation of the CSharp compiler identification source "CMakeCSharpCompilerId.cs" produced "CompilerIdCSharp.pdb"
      
      The CSharp compiler identification could not be found in:
        D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-extractor/build/CMakeFiles/3.30.1/CompilerIdCSharp/CompilerIdCSharp.csproj
      
      The CSharp compiler identification is Microsoft Visual Studio, found in:
        D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-extractor/build/CMakeFiles/3.30.1/CompilerIdCSharp/CompilerIdCSharp.exe
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.30/Modules/CMakeTestCSharpCompiler.cmake:34 (try_compile)"
      - "CMakeLists.txt:3 (project)"
    checks:
      - "Check for working C# compiler: C:/Program Files/Microsoft Visual Studio/2022/Professional/MSBuild/Current/Bin/Roslyn/csc.exe"
    directories:
      source: "D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-extractor/build/CMakeFiles/CMakeScratch/TryCompile-rleq2p"
      binary: "D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-extractor/build/CMakeFiles/CMakeScratch/TryCompile-rleq2p"
    cmakeVariables:
      CMAKE_CSharp_FLAGS: "/platform:x64 /define:TRACE"
      CMAKE_CSharp_FLAGS_DEBUG: "/debug:full /optimize- /warn:3 /errorreport:prompt /define:DEBUG"
      CMAKE_EXE_LINKER_FLAGS: ""
    buildResult:
      variable: "CMAKE_CSharp_COMPILER_WORKS"
      cached: true
      stdout: |
        Change Dir: 'D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-extractor/build/CMakeFiles/CMakeScratch/TryCompile-rleq2p'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_82ea0.csproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.11.9+a69bbaaf5 for .NET Framework
        Build started 3/10/2025 3:00:54 PM.
        
        Project "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-extractor\\build\\CMakeFiles\\CMakeScratch\\TryCompile-rleq2p\\cmTC_82ea0.csproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-extractor\\build\\CMakeFiles\\CMakeScratch\\TryCompile-rleq2p\\Debug\\".
          Creating directory "obj\\x64\\Debug\\".
        CoreCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Current\\Bin\\Roslyn\\csc.exe /noconfig /nowarn:1701,1702 /fullpaths /nostdlib+ /platform:x64 /errorreport:prompt /warn:3 /define:TRACE;DEBUG /highentropyva+ /reference:"C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\mscorlib.dll" /reference:"C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Core.dll" /debug+ /debug:full /optimize- /out:obj\\x64\\Debug\\cmTC_82ea0.exe /subsystemversion:6.00 /target:exe /utf8output /langversion:7.3 D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-extractor\\build\\CMakeFiles\\CMakeScratch\\TryCompile-rleq2p\\testCSharpCompiler.cs "obj\\x64\\Debug\\.NETFramework,Version=v4.7.2.AssemblyAttributes.cs"
          CompilerServer: server - server processed compilation - cmTC_82ea0
        CopyFilesToOutputDirectory:
          Copying file from "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-extractor\\build\\CMakeFiles\\CMakeScratch\\TryCompile-rleq2p\\obj\\x64\\Debug\\cmTC_82ea0.exe" to "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-extractor\\build\\CMakeFiles\\CMakeScratch\\TryCompile-rleq2p\\Debug\\cmTC_82ea0.exe".
          cmTC_82ea0 -> D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-extractor\\build\\CMakeFiles\\CMakeScratch\\TryCompile-rleq2p\\Debug\\cmTC_82ea0.exe
          Copying file from "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-extractor\\build\\CMakeFiles\\CMakeScratch\\TryCompile-rleq2p\\obj\\x64\\Debug\\cmTC_82ea0.pdb" to "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-extractor\\build\\CMakeFiles\\CMakeScratch\\TryCompile-rleq2p\\Debug\\cmTC_82ea0.pdb".
        Done Building Project "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-extractor\\build\\CMakeFiles\\CMakeScratch\\TryCompile-rleq2p\\cmTC_82ea0.csproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:00.16
        
      exitCode: 0
...

---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineSystem.cmake:205 (message)"
      - "CMakeLists.txt:3 (project)"
    message: |
      The system is: Windows - 10.0.22631 - AMD64
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCSharpCompiler.cmake:23 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Compiling the CSharp compiler identification source file "CMakeCSharpCompilerId.cs" succeeded.
      Compiler:  
      Build flags: /platform:x64;/define:TRACE
      Id flags:  
      
      The output was:
      0
      MSBuild version 17.11.9+a69bbaaf5 for .NET Framework
      Build started 3/13/2025 2:48:35 PM.
      
      Project "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-extractor\\build\\CMakeFiles\\4.0.0-rc4\\CompilerIdCSharp\\CompilerIdCSharp.csproj" on node 1 (default targets).
      PrepareForBuild:
        Creating directory "obj\\x64\\Debug\\".
      CoreCompile:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Current\\Bin\\Roslyn\\csc.exe /noconfig /nowarn:1701,1702 /fullpaths /nostdlib+ /platform:anycpu32bitpreferred /define:Platformx64;PlatformToolsetv143 /highentropyva+ /reference:"C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\mscorlib.dll" /reference:"C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Core.dll" /debug+ /out:obj\\x64\\Debug\\CompilerIdCSharp.exe /subsystemversion:6.00 /target:exe /utf8output /langversion:7.3 CMakeCSharpCompilerId.cs "obj\\x64\\Debug\\.NETFramework,Version=v4.7.2.AssemblyAttributes.cs"
        CompilerServer: server - server processed compilation - CompilerIdCSharp
      CopyFilesToOutputDirectory:
        Copying file from "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-extractor\\build\\CMakeFiles\\4.0.0-rc4\\CompilerIdCSharp\\obj\\x64\\Debug\\CompilerIdCSharp.exe" to "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-extractor\\build\\CMakeFiles\\4.0.0-rc4\\CompilerIdCSharp\\CompilerIdCSharp.exe".
        CompilerIdCSharp -> D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-extractor\\build\\CMakeFiles\\4.0.0-rc4\\CompilerIdCSharp\\CompilerIdCSharp.exe
        Copying file from "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-extractor\\build\\CMakeFiles\\4.0.0-rc4\\CompilerIdCSharp\\obj\\x64\\Debug\\CompilerIdCSharp.pdb" to "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-extractor\\build\\CMakeFiles\\4.0.0-rc4\\CompilerIdCSharp\\CompilerIdCSharp.pdb".
      PostBuildEvent:
        if not "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Current\\Bin\\Roslyn"=="" if exist "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Current\\Bin\\Roslyn\\csc.exe" set _CSC=C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Current\\Bin\\Roslyn
        if exist "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Current\\Bin\\amd64\\csc.exe" set _CSC=C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Current\\Bin\\amd64
        if "%_CSC%"=="" exit -1
        @echo CMAKE_CSharp_COMPILER=%_CSC%\\csc.exe
        CMAKE_CSharp_COMPILER=C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Current\\Bin\\Roslyn\\csc.exe
      Done Building Project "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-extractor\\build\\CMakeFiles\\4.0.0-rc4\\CompilerIdCSharp\\CompilerIdCSharp.csproj" (default targets).
      
      Build succeeded.
          0 Warning(s)
          0 Error(s)
      
      Time Elapsed 00:00:00.20
      
      
      Compilation of the CSharp compiler identification source "CMakeCSharpCompilerId.cs" produced "CompilerIdCSharp.csproj"
      
      Compilation of the CSharp compiler identification source "CMakeCSharpCompilerId.cs" produced "CompilerIdCSharp.exe"
      
      Compilation of the CSharp compiler identification source "CMakeCSharpCompilerId.cs" produced "CompilerIdCSharp.pdb"
      
      The CSharp compiler identification could not be found in:
        D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-extractor/build/CMakeFiles/4.0.0-rc4/CompilerIdCSharp/CompilerIdCSharp.csproj
      
      The CSharp compiler identification is Microsoft Visual Studio, found in:
        D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-extractor/build/CMakeFiles/4.0.0-rc4/CompilerIdCSharp/CompilerIdCSharp.exe
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeTestCSharpCompiler.cmake:34 (try_compile)"
      - "CMakeLists.txt:3 (project)"
    checks:
      - "Check for working C# compiler: C:/Program Files/Microsoft Visual Studio/2022/Professional/MSBuild/Current/Bin/Roslyn/csc.exe"
    directories:
      source: "D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-extractor/build/CMakeFiles/CMakeScratch/TryCompile-pkjda7"
      binary: "D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-extractor/build/CMakeFiles/CMakeScratch/TryCompile-pkjda7"
    cmakeVariables:
      CMAKE_CSharp_FLAGS: "/platform:x64 /define:TRACE"
      CMAKE_CSharp_FLAGS_DEBUG: "/debug:full /optimize- /warn:3 /errorreport:prompt /define:DEBUG"
      CMAKE_EXE_LINKER_FLAGS: ""
    buildResult:
      variable: "CMAKE_CSharp_COMPILER_WORKS"
      cached: true
      stdout: |
        Change Dir: 'D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-extractor/build/CMakeFiles/CMakeScratch/TryCompile-pkjda7'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_21dd1.csproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.11.9+a69bbaaf5 for .NET Framework
        Build started 3/13/2025 2:48:36 PM.
        
        Project "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-extractor\\build\\CMakeFiles\\CMakeScratch\\TryCompile-pkjda7\\cmTC_21dd1.csproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-extractor\\build\\CMakeFiles\\CMakeScratch\\TryCompile-pkjda7\\Debug\\".
          Creating directory "obj\\x64\\Debug\\".
        CoreCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Current\\Bin\\Roslyn\\csc.exe /noconfig /nowarn:1701,1702 /fullpaths /nostdlib+ /platform:x64 /errorreport:prompt /warn:3 /define:TRACE;DEBUG /highentropyva+ /reference:"C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\mscorlib.dll" /reference:"C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Core.dll" /debug+ /debug:full /optimize- /out:obj\\x64\\Debug\\cmTC_21dd1.exe /subsystemversion:6.00 /target:exe /utf8output /langversion:7.3 D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-extractor\\build\\CMakeFiles\\CMakeScratch\\TryCompile-pkjda7\\testCSharpCompiler.cs "obj\\x64\\Debug\\.NETFramework,Version=v4.7.2.AssemblyAttributes.cs"
          CompilerServer: server - server processed compilation - cmTC_21dd1
        CopyFilesToOutputDirectory:
          Copying file from "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-extractor\\build\\CMakeFiles\\CMakeScratch\\TryCompile-pkjda7\\obj\\x64\\Debug\\cmTC_21dd1.exe" to "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-extractor\\build\\CMakeFiles\\CMakeScratch\\TryCompile-pkjda7\\Debug\\cmTC_21dd1.exe".
          cmTC_21dd1 -> D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-extractor\\build\\CMakeFiles\\CMakeScratch\\TryCompile-pkjda7\\Debug\\cmTC_21dd1.exe
          Copying file from "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-extractor\\build\\CMakeFiles\\CMakeScratch\\TryCompile-pkjda7\\obj\\x64\\Debug\\cmTC_21dd1.pdb" to "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-extractor\\build\\CMakeFiles\\CMakeScratch\\TryCompile-pkjda7\\Debug\\cmTC_21dd1.pdb".
        Done Building Project "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-extractor\\build\\CMakeFiles\\CMakeScratch\\TryCompile-pkjda7\\cmTC_21dd1.csproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:00.19
        
      exitCode: 0
...
