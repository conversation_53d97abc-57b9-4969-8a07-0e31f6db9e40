/*
 * Copyright 2021-present Alcon. All rights reserved.
 * Alcon proprietary/confidential.
 * Use is subject to license terms.
 */

using NUnit.Framework;
using System;
using System.IO;
using System.Linq;

namespace LocalizationLib.Tests;

[TestFixture]
public class RegexCppProcessorTests
{
    private RegexCppProcessor _processor;
    private string _tempFilePath;

    [SetUp]
    public void Setup()
    {
        _processor = new RegexCppProcessor();
        _tempFilePath = Path.GetTempFileName();
    }

    [TearDown]
    public void TearDown()
    {
        if (File.Exists(_tempFilePath))
        {
            File.Delete(_tempFilePath);
        }
    }

    [Test]
    public void ProcessCppFile_WithRegularStringLiterals_FindsAllStrings()
    {
        // Arrange
        var content = @"
            const char* str1 = ""Hello"";
            const wchar_t* str2 = L""World"";
            const char8_t* str3 = u8""UTF-8"";
            const char16_t* str4 = u""UTF-16"";
            const char32_t* str5 = U""UTF-32"";
            std::string str6 = ""std::string"";
        ";
        File.WriteAllText(_tempFilePath, content);

        // Act
        var result = _processor.ProcessCppFile(_tempFilePath, "test-group");

        // Assert
        Assert.That(result.Replacements.Count, Is.EqualTo(6), "Should find all string literals");
        Assert.That(result.Replacements.Select(r => r.OriginalValue), Is.EquivalentTo(new[]
        {
            @"""Hello""",
            @"L""World""",
            @"u8""UTF-8""",
            @"u""UTF-16""",
            @"U""UTF-32""",
            @"""std::string"""
        }));

        // Verify string types
        var types = result.Replacements.Select(r => _processor.GetStringType(r)).ToList();
        Assert.That(types, Is.EquivalentTo(new[]
        {
            "char*",
            "wchar_t*",
            "char8_t*",
            "char16_t*",
            "char32_t*",
            "char*"
        }));
    }

    [Test]
    public void ProcessCppFile_WithRawStringLiterals_FindsAllStrings()
    {
        // Arrange
        var content = @"
            const char* raw1 = R""(Raw string)"";
            const char* raw2 = R""delim(Raw string with delimiter)delim"";
            const char* raw3 = R""(Raw string with (nested) parentheses)"";
        ";
        File.WriteAllText(_tempFilePath, content);

        // Act
        var result = _processor.ProcessCppFile(_tempFilePath, "test-group");

        // Assert
        Assert.That(result.Replacements.Count, Is.EqualTo(3), "Should find all raw string literals");
        Assert.That(result.Replacements.Select(r => r.OriginalValue), Is.EquivalentTo(new[]
        {
            @"R""(Raw string)""",
            @"R""delim(Raw string with delimiter)delim""",
            @"R""(Raw string with (nested) parentheses)"""
        }));

        // Verify all raw strings are marked as char*
        Assert.That(result.Replacements.All(r => _processor.GetStringType(r) == "char*"), Is.True);
    }

    [Test]
    public void ProcessCppFile_WithStringsInComments_TracksContext()
    {
        // Arrange
        var content = @"
            // Comment with ""string in comment""
            const char* str = ""actual string"";
            /* Block comment with ""string in block comment"" */
        ";
        File.WriteAllText(_tempFilePath, content);

        // Act
        var result = _processor.ProcessCppFile(_tempFilePath, "test-group");

        // Assert
        Assert.That(result.Replacements.Count, Is.EqualTo(3), "Should find all strings including those in comments");
        var commentStrings = result.Replacements.Where(r => r.Notes.Any(n => n.Contains("Comment:"))).ToList();
        Assert.That(commentStrings.Count, Is.EqualTo(2), "Should identify strings in comments");
        var actualString = result.Replacements.First(r => r.OriginalValue == @"""actual string""");
        Assert.That(actualString.Notes.Any(n => n.Contains("Comment:")), Is.False, "Should not mark actual string as comment");
    }

    [Test]
    public void ProcessCppFile_WithStringsInPreprocessor_TracksContext()
    {
        // Arrange
        var content = @"
            #define STRING1 ""string in define""
            const char* str = ""actual string"";
            #ifdef STRING1
            #define STRING2 ""another string in define""
            #endif
        ";
        File.WriteAllText(_tempFilePath, content);

        // Act
        var result = _processor.ProcessCppFile(_tempFilePath, "test-group");

        // Assert
        Assert.That(result.Replacements.Count, Is.EqualTo(3), "Should find all strings including those in preprocessor");
        var preprocessorStrings = result.Replacements.Where(r => r.Notes.Any(n => n.Contains("Preprocessor directive"))).ToList();
        Assert.That(preprocessorStrings.Count, Is.EqualTo(2), "Should identify strings in preprocessor");
        var actualString = result.Replacements.First(r => r.OriginalValue == @"""actual string""");
        Assert.That(actualString.Notes.Any(n => n.Contains("Preprocessor directive")), Is.False, "Should not mark actual string as preprocessor");
    }

    [Test]
    public void ProcessCppFile_WithStringsInFunctions_TracksContext()
    {
        // Arrange
        var content = @"
            class MyClass {
                void MyFunction() {
                    const char* str1 = ""string in function"";
                }
                static void StaticFunction() {
                    const char* str2 = ""string in static function"";
                }
            };
            const char* str3 = ""string outside function"";
        ";
        File.WriteAllText(_tempFilePath, content);

        // Act
        var result = _processor.ProcessCppFile(_tempFilePath, "test-group");

        // Assert
        Assert.That(result.Replacements.Count, Is.EqualTo(3), "Should find all strings");
        var functionStrings = result.Replacements.Where(r => r.Notes.Any(n => n.Contains("Function:"))).ToList();
        Assert.That(functionStrings.Count, Is.EqualTo(2), "Should identify strings in functions");
        var classStrings = result.Replacements.Where(r => r.Notes.Any(n => n.Contains("Class: MyClass"))).ToList();
        Assert.That(classStrings.Count, Is.EqualTo(2), "Should identify strings in class");
        var outsideString = result.Replacements.First(r => r.OriginalValue == @"""string outside function""");
        Assert.That(outsideString.Notes.Any(n => n.Contains("Function:")), Is.False, "Should not mark outside string as in function");
    }

    [Test]
    public void ProcessCppFile_WithEscapedStrings_HandlesEscapesCorrectly()
    {
        // Arrange
        var content = @"
            const char* str1 = ""Hello\nWorld"";
            const char* str2 = ""Path: C:\\Program Files\\App"";
            const char* str3 = ""Quote: \"""";
            const char* str4 = ""Tab\tand\r\nNewline"";
        ";
        File.WriteAllText(_tempFilePath, content);

        // Act
        var result = _processor.ProcessCppFile(_tempFilePath, "test-group");

        // Assert
        Assert.That(result.Replacements.Count, Is.EqualTo(4), "Should find all strings with escapes");
        Assert.That(result.Replacements.Select(r => r.OriginalValue), Is.EquivalentTo(new[]
        {
            @"""Hello\nWorld""",
            @"""Path: C:\\Program Files\\App""",
            @"""Quote: \""""",
            @"""Tab\tand\r\nNewline"""
        }));
    }

    [Test]
    public void ProcessCppFile_WithSurroundingContext_CapturesContext()
    {
        // Arrange
        var content = @"
            namespace MyNamespace {
                class MyClass {
                    void MyFunction() {
                        // Some comment
                        const char* str = ""string with context"";
                        // More code
                    }
                };
            }
        ";
        File.WriteAllText(_tempFilePath, content);

        // Act
        var result = _processor.ProcessCppFile(_tempFilePath, "test-group");

        // Assert
        var record = result.Replacements.First();
        Assert.That(record.Notes.Any(n => n.Contains("Function: MyFunction")), Is.True);
        Assert.That(record.Notes.Any(n => n.Contains("Class: MyClass")), Is.True);
        Assert.That(record.Notes.Any(n => n.Contains("Surrounding code:")), Is.True);
    }

    [Test]
    public void ProcessCppFile_WithReplacementRecordProperties_SetsAllProperties()
    {
        // Arrange
        var content = @"const char* str = ""test string"";";
        File.WriteAllText(_tempFilePath, content);

        // Act
        var result = _processor.ProcessCppFile(_tempFilePath, "test-group");

        // Assert
        var record = result.Replacements.First();
        Assert.That(record.Property, Is.EqualTo("(string literal)"));
        Assert.That(record.OriginalValue, Is.EqualTo(@"""test string"""));
        Assert.That(record.NewKey, Is.EqualTo("en:test string"));
        Assert.That(record.FilePath, Is.EqualTo(_tempFilePath));
        Assert.That(record.LineNumber, Is.GreaterThan(0));
        Assert.That(record.Column, Is.GreaterThan(0));
        Assert.That(record.GroupId, Is.EqualTo("test-group"));
        Assert.That(record.Source, Is.EqualTo(ExtractionSource.CppRegex));
        Assert.That(record.LineContent, Is.Not.Empty);
        Assert.That(record.Notes, Is.Not.Empty);
    }
    [Test]
    public void ProcessCppFile_WithConcatenatedStrings_ExtractsSeparately()
    {
        // Arrange
        var content = @"
                const char* str1 = ""Hello "" ""World""; // Concatenated
                std::string s = L""Wide "" L""String"";
            ";
        File.WriteAllText(_tempFilePath, content);

        // Act
        var result = _processor.ProcessCppFile(_tempFilePath, "test-group");

        // Assert
        // Expects that "Hello " and "World" are extracted as two separate literals.
        // Same for L"Wide " and L"String".
        Assert.That(result.Replacements.Count, Is.EqualTo(4), "Should find all parts of concatenated strings as separate literals.");
        Assert.That(result.Replacements.Select(r => r.OriginalValue), Is.EquivalentTo(new[]
        {
                @"""Hello """,
                @"""World""",
                @"L""Wide """,
                @"L""String"""
            }));
    }

    /// <summary>
    /// Ignore case for now
    /// </summary>
    public void ProcessCppFile_WithMultiLineStringContinuations_ExtractsCorrectly()
    {
        // Arrange
        var content = @"
                const char* multiLineStr = ""This is a string \
    that spans multiple lines."";
                const wchar_t* wideMultiLine = L""Another \
    wide string example."";
            ";
        File.WriteAllText(_tempFilePath, content);

        // Act
        var result = _processor.ProcessCppFile(_tempFilePath, "test-group");

        // Assert
        Assert.That(result.Replacements.Count, Is.EqualTo(2), "Should find multi-line strings.");
        Assert.That(result.Replacements[0].OriginalValue, Is.EqualTo(@"""This is a string that spans multiple lines."""));
        Assert.That(result.Replacements[1].OriginalValue, Is.EqualTo(@"L""Another wide string example."""));
        // Note: The OriginalValue might or might not contain the actual newline characters
        // depending on how RegexCppProcessor handles line continuations.
        // This assertion assumes it reconstructs the string without the backslash-newline.
        // Adjust if OriginalValue is expected to be different (e.g. @"""This is a string \\\nthat spans multiple lines.""")
    }

    [Test]
    public void ProcessCppFile_StringsInLoggingMacros_AreAnnotated()
    {
        // Arrange
        var content = @"
                LOG(""This is a log message."");
                TRACE(""Trace this: %d"", value);
                const char* str = ""actual string""; // Control
            ";
        File.WriteAllText(_tempFilePath, content);

        // Act
        var result = _processor.ProcessCppFile(_tempFilePath, "test-group");

        // Assert
        Assert.That(result.Replacements.Count, Is.EqualTo(3), "Should find strings, including those in logging macros.");

    }

    [Test]
    public void ProcessCppFile_StringsInConstexprCharDeclarations_AreAnnotated()
    {
        // Arrange
        var content = @"
                constexpr const char* constStr = ""Compile time string"";
                static const wchar_t* staticWideStr = L""Static wide string"";
                const char* normalStr = ""Normal string""; // Control
            ";
        File.WriteAllText(_tempFilePath, content);

        // Act
        var result = _processor.ProcessCppFile(_tempFilePath, "test-group");

        // Assert
        Assert.That(result.Replacements.Count, Is.EqualTo(3));

    }

    [Test]
    public void ProcessCppFile_StringsInArrayInitializers_AreAnnotated()
    {
        // Arrange
        var content = @"
                const char* arr[] = { ""one"", ""two"", L""three_wide"" };
                const char* str = ""actual string""; // Control
            ";
        File.WriteAllText(_tempFilePath, content);

        // Act
        var result = _processor.ProcessCppFile(_tempFilePath, "test-group");

        // Assert
        Assert.That(result.Replacements.Count, Is.EqualTo(4));

        var arrStr1 = result.Replacements.First(r => r.OriginalValue == @"""one""");
        Assert.That(arrStr1.Notes.Any(n => n.Contains("Surrounding code")), Is.True, "String 'one' in array initializer should be annotated.");

        var arrStr2 = result.Replacements.First(r => r.OriginalValue == @"""two""");
        Assert.That(arrStr2.Notes.Any(n => n.Contains("Surrounding code")), Is.True, "String 'two' in array initializer should be annotated.");

        var arrStr3 = result.Replacements.First(r => r.OriginalValue == @"L""three_wide""");
        Assert.That(arrStr3.Notes.Any(n => n.Contains("Surrounding code")), Is.True, "String 'three_wide' in array initializer should be annotated.");

    }
}