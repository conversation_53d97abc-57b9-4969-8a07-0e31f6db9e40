# Amazon Bedrock Integration for Localization Tools

## Overview

The localization tool provides native Amazon Bedrock integration for intelligent text analysis and translation validation. This implementation uses REST API calls with AWS Signature Version 4 authentication, eliminating the need for external AWS SDK dependencies while providing enterprise-grade reliability and performance.

### Key Features

- **Zero Dependencies**: Pure REST API implementation using standard .NET HttpClient
- **Multi-Model Support**: Claude 3 (Sonnet/Haiku), Amazon Titan, Meta Llama, Mistral models
- **Intelligent Rate Limiting**: Exponential backoff with jitter and Retry-After header support
- **Robust Error Handling**: Comprehensive retry logic for network issues and API limits
- **Flexible Authentication**: Configuration file credentials or environment variables
- **Production Ready**: Extensive logging, debugging, and monitoring capabilities

## Prerequisites

### AWS Account Setup

1. **AWS Account**: Active AWS account with billing enabled
2. **Bedrock Access**: Request access to desired foundation models in AWS Bedrock console
3. **IAM Permissions**: Proper IAM permissions for Bedrock model invocation
4. **Regional Availability**: Ensure Bedrock is available in your chosen AWS region

### Required AWS Permissions

Your AWS credentials must have the following IAM permissions:

```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "bedrock:InvokeModel",
                "bedrock:InvokeModelWithResponseStream"
            ],
            "Resource": [
                "arn:aws:bedrock:*::foundation-model/*"
            ]
        }
    ]
}
```

**Recommended**: Attach the AWS managed policy `AmazonBedrockFullAccess` for comprehensive access.

### Model Access Request

1. Navigate to [AWS Bedrock Console](https://console.aws.amazon.com/bedrock/)
2. Go to **Model access** in the left sidebar
3. Click **Request model access**
4. Select desired models (e.g., Claude 3 Sonnet, Titan Text Express)
5. Submit request and wait for approval (typically 5-30 minutes)

## Configuration

### Basic Configuration Structure

```json
{
    "llmProvider": "BedrockProvider",
    "promptTemplate": "Your custom analysis prompt...",
    "bedrockProviderConfig": {
        "region": "us-east-1",
        "modelId": "anthropic.claude-3-sonnet-20240229-v1:0",
        "temperature": 0.0,
        "maxRetries": 3,
        "timeoutSeconds": 120,
        "useChunking": true,
        "chunkSize": 3000,
        "maxTokens": 4096,
        "accessKey": "YOUR_AWS_ACCESS_KEY",
        "secretKey": "YOUR_AWS_SECRET_KEY"
    }
}
```

### Configuration Parameters

#### Required Parameters

| Parameter | Type | Description | Example |
|-----------|------|-------------|---------|
| `region` | string | AWS region where Bedrock is available | `"us-east-1"` |
| `modelId` | string | Bedrock foundation model identifier | `"anthropic.claude-3-sonnet-20240229-v1:0"` |

#### Optional Parameters

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `temperature` | number | `0.0` | Model creativity (0.0-1.0) |
| `maxRetries` | integer | `3` | Maximum retry attempts |
| `timeoutSeconds` | integer | `120` | Request timeout in seconds |
| `useChunking` | boolean | `true` | Process large files in chunks |
| `chunkSize` | integer | `3000` | Characters per chunk |
| `maxTokens` | integer | `4096` | Maximum response tokens |

#### Authentication Parameters

**Option 1: Configuration File Credentials**
```json
{
    "accessKey": "AKIAIOSFODDN7EXAMPLE",
    "secretKey": "wJalrXUEnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY"
}
```

**Option 2: Temporary Credentials**
```json
{
    "accessKey": "AKIAIOSFODNN7EXAMPLE",
    "secretKey": "wJalrXUtnFENI/K7MDENG/bPxRfiCYEXAMPLEKEY",
    "sessionToken": "IQoJb3JpZ2luX2VjEHoaCXVzLWVhc3QtMSJHMEUCIQD..."
}
```

**Option 3: Environment Variables**
Set the following environment variables (credentials will be automatically detected):
- `AWS_ACCESS_KEY_ID`
- `AWS_SECRET_ACCESS_KEY`
- `AWS_SESSION_TOKEN` (optional, for temporary credentials)
- `AWS_DEFAULT_REGION` (optional, fallback region)

## Supported Models

### Anthropic Claude Models

| Model ID | Description | Use Case |
|----------|-------------|----------|
| `anthropic.claude-3-sonnet-20240229-v1:0` | Claude 3 Sonnet | Balanced performance and cost |
| `anthropic.claude-3-haiku-20240307-v1:0` | Claude 3 Haiku | Fast, cost-effective |
| `anthropic.claude-instant-v1` | Claude Instant | Legacy, fast responses |

### Amazon Titan Models

| Model ID | Description | Use Case |
|----------|-------------|----------|
| `amazon.titan-text-express-v1` | Titan Text Express | General text generation |
| `amazon.titan-text-lite-v1` | Titan Text Lite | Lightweight tasks |

### Meta Llama Models

| Model ID | Description | Use Case |
|----------|-------------|----------|
| `meta.llama2-70b-chat-v1` | Llama 2 70B Chat | High-quality analysis |
| `meta.llama2-13b-chat-v1` | Llama 2 13B Chat | Balanced performance |

### Mistral Models

| Model ID | Description | Use Case |
|----------|-------------|----------|
| `mistral.mistral-7b-instruct-v0:2` | Mistral 7B Instruct | Code analysis |
| `mistral.mixtral-8x7b-instruct-v0:1` | Mixtral 8x7B | Advanced reasoning |

## Usage Examples

### Example 1: Production Configuration with Claude 3 Sonnet

```json
{
    "llmProvider": "BedrockProvider",
    "promptTemplate": "You are analyzing C# code from a WPF application (file: {{fileName}}). Identify user-facing text that needs translation. Return JSON with 'records' array containing objects with: lineNumber, column, originalValue, reason, lineText. Skip technical strings, logs, and configuration keys.",
    "bedrockProviderConfig": {
        "region": "us-east-1",
        "modelId": "anthropic.claude-3-sonnet-20240229-v1:0",
        "temperature": 0.0,
        "maxRetries": 5,
        "timeoutSeconds": 180,
        "useChunking": true,
        "chunkSize": 2500,
        "maxTokens": 4096
    }
}
```

### Example 2: Development Configuration with Environment Variables

```json
{
    "llmProvider": "BedrockProvider",
    "promptTemplate": "Analyze this code for user-facing strings that need translation...",
    "bedrockProviderConfig": {
        "region": "us-west-2",
        "modelId": "anthropic.claude-3-haiku-20240307-v1:0",
        "temperature": 0.1,
        "maxRetries": 3,
        "timeoutSeconds": 60,
        "maxTokens": 2048
    }
}
```

### Example 3: High-Volume Processing with Titan

```json
{
    "llmProvider": "BedrockProvider",
    "promptTemplate": "Identify translatable strings in this code...",
    "bedrockProviderConfig": {
        "region": "us-east-1",
        "modelId": "amazon.titan-text-express-v1",
        "temperature": 0.0,
        "maxRetries": 10,
        "timeoutSeconds": 300,
        "useChunking": true,
        "chunkSize": 5000,
        "maxTokens": 8192
    }
}
```

## Rate Limiting and Performance

### Intelligent Retry Logic

The implementation includes sophisticated retry mechanisms:

1. **Exponential Backoff**: 1s → 2s → 4s → 8s → 16s → 30s (capped)
2. **Jitter**: Random 0-1000ms added to prevent thundering herd effects
3. **Retry-After Headers**: Respects AWS-provided retry timing
4. **Error Classification**: Different strategies for different error types

### Performance Optimization

- **Request Batching**: Processes multiple texts efficiently
- **Connection Reuse**: Single HttpClient instance with keep-alive
- **Chunking**: Handles large files without hitting token limits
- **Parallel Processing**: Configurable concurrency levels

### Rate Limit Handling

```
TooManyRequests (429) → Exponential backoff with jitter
Throttling → Respect Retry-After headers
Network errors → Standard retry with backoff
Authentication errors → Immediate failure (no retry)
```

## Error Handling and Troubleshooting

### Common Issues and Solutions

| Error | Cause | Solution |
|-------|-------|----------|
| `403 Forbidden` | Missing IAM permissions | Add `bedrock:InvokeModel` permission |
| `403 Forbidden` | Model not accessible | Request model access in Bedrock console |
| `429 TooManyRequests` | Rate limiting | Automatic retry with backoff |
| `400 Bad Request` | Invalid model ID | Check model ID and region availability |
| `401 Unauthorized` | Invalid credentials | Verify AWS access keys |

### Debug Mode

Enable detailed logging by checking the console output for:
- `[DEBUG] Request URL` - API endpoint being called
- `[DEBUG] Response Status` - HTTP status codes
- `[DEBUG] Response Headers` - Token usage and latency metrics
- `[DEBUG] Rate limited` - Retry timing information

### Monitoring and Metrics

The implementation provides comprehensive metrics:
- **Token Usage**: Input/output token counts per request
- **Latency**: Request processing time from AWS
- **Success Rate**: Percentage of successful API calls
- **Retry Statistics**: Number of retries and backoff timing

## Security Best Practices

### Credential Management

1. **Never commit credentials** to version control
2. **Use environment variables** for production deployments
3. **Rotate credentials regularly** (every 90 days recommended)
4. **Use IAM roles** when running on AWS infrastructure
5. **Apply least privilege** principle to IAM policies

### Network Security

1. **Use HTTPS only** (enforced by implementation)
2. **Validate SSL certificates** (default behavior)
3. **Monitor API usage** through AWS CloudTrail
4. **Set appropriate timeouts** to prevent hanging connections

## Migration Guide

### From Local LLM to Bedrock

1. **Backup current configuration**:
   ```bash
   cp etc/llmconfig.json etc/llmconfig-local-backup.json
   ```

2. **Update configuration**:
   ```json
   {
     "llmProvider": "BedrockProvider",  // Changed from "LocalLlmProvider"
     "bedrockProviderConfig": {         // Replaces "localLlmProviderConfig"
       "region": "us-east-1",
       "modelId": "anthropic.claude-3-sonnet-20240229-v1:0"
     }
   }
   ```

3. **Test with small dataset** before full deployment

4. **Monitor costs** through AWS Billing dashboard

### Performance Comparison

| Aspect | Local LLM | Amazon Bedrock |
|--------|-----------|----------------|
| Setup Complexity | High | Medium |
| Hardware Requirements | GPU recommended | None |
| Scalability | Limited | Unlimited |
| Cost Model | Fixed | Pay-per-use |
| Model Quality | Variable | Enterprise-grade |
| Maintenance | Self-managed | Fully managed |

## Cost Optimization

### Token Usage Optimization

1. **Optimize prompts** to reduce input tokens
2. **Use chunking** for large files
3. **Choose appropriate models** (Haiku for simple tasks, Sonnet for complex)
4. **Monitor token usage** through debug logs

### Model Selection Guidelines

- **Claude 3 Haiku**: Simple classification tasks, high volume
- **Claude 3 Sonnet**: Complex analysis, balanced cost/performance
- **Titan Text Express**: Cost-effective for basic text processing
- **Llama 2**: Open-source alternative with good performance

## Support and Resources

### AWS Resources

- [Amazon Bedrock Documentation](https://docs.aws.amazon.com/bedrock/)
- [Bedrock Model Documentation](https://docs.aws.amazon.com/bedrock/latest/userguide/models-supported.html)
- [AWS Support Center](https://console.aws.amazon.com/support/)

### Implementation Support

- Check debug logs for detailed error information
- Verify IAM permissions and model access
- Test with minimal configuration first
- Monitor AWS CloudTrail for API call details

### Community

- Report issues through project repository
- Contribute improvements via pull requests
- Share configuration examples and best practices
