{"Version": 1, "WorkspaceRootPath": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\tests\\unit\\build\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{A0860F5E-0D2E-3B8F-9F97-F6C5074027B7}|localizationlib_unit_tests.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\tests\\unit\\src\\xamlprocessortests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{BB6BC777-A517-3A1B-B98D-2F6C0F78586E}|localizationlib\\localizationlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\processer\\xaml\\xamlprocessor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{BB6BC777-A517-3A1B-B98D-2F6C0F78586E}|localizationlib\\localizationlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\projectlocalizationextractor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{BB6BC777-A517-3A1B-B98D-2F6C0F78586E}|localizationlib\\localizationlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\processer\\cpp\\cppextractor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{BB6BC777-A517-3A1B-B98D-2F6C0F78586E}|localizationlib\\localizationlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\processer\\cs\\consolidatedcsprocessor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{BB6BC777-A517-3A1B-B98D-2F6C0F78586E}|localizationlib\\localizationlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\processer\\cs\\csextractor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A0860F5E-0D2E-3B8F-9F97-F6C5074027B7}|localizationlib_unit_tests.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\tests\\unit\\src\\consolidatedcppprocessortests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{BB6BC777-A517-3A1B-B98D-2F6C0F78586E}|localizationlib\\localizationlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\processer\\cpp\\consolidatedcppprocessor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{BB6BC777-A517-3A1B-B98D-2F6C0F78586E}|localizationlib\\localizationlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\utils\\localizationutils.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{BB6BC777-A517-3A1B-B98D-2F6C0F78586E}|localizationlib\\localizationlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\common\\projecttextextractorconfigmanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A0860F5E-0D2E-3B8F-9F97-F6C5074027B7}|localizationlib_unit_tests.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\tests\\unit\\src\\regexcppprocessortests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A0860F5E-0D2E-3B8F-9F97-F6C5074027B7}|localizationlib_unit_tests.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\tests\\unit\\src\\stringconcatenationprocessortests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{BB6BC777-A517-3A1B-B98D-2F6C0F78586E}|localizationlib\\localizationlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\projectlocalizationreplacer.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{BB6BC777-A517-3A1B-B98D-2F6C0F78586E}|localizationlib\\localizationlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\common\\projecttextextractorconfig.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{BB6BC777-A517-3A1B-B98D-2F6C0F78586E}|localizationlib\\localizationlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\iextractor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A0860F5E-0D2E-3B8F-9F97-F6C5074027B7}|localizationlib_unit_tests.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\tests\\unit\\src\\xamltexttocodebehindprocessortests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{BB6BC777-A517-3A1B-B98D-2F6C0F78586E}|localizationlib\\localizationlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\code-restructure\\xamltexttocodebehindprocessor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{BB6BC777-A517-3A1B-B98D-2F6C0F78586E}|localizationlib\\localizationlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\processer\\cs\\roslyn\\roslyncsprocessor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{BB6BC777-A517-3A1B-B98D-2F6C0F78586E}|localizationlib\\localizationlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\processer\\xaml\\xamlextractor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A0860F5E-0D2E-3B8F-9F97-F6C5074027B7}|localizationlib_unit_tests.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\tests\\unit\\src\\reportgeneratortests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A0860F5E-0D2E-3B8F-9F97-F6C5074027B7}|localizationlib_unit_tests.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\tests\\unit\\src\\csextractormodetests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A0860F5E-0D2E-3B8F-9F97-F6C5074027B7}|localizationlib_unit_tests.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\tests\\unit\\src\\xamlextractormodetests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A0860F5E-0D2E-3B8F-9F97-F6C5074027B7}|localizationlib_unit_tests.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\tests\\unit\\src\\roslyncsprocessortests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{BB6BC777-A517-3A1B-B98D-2F6C0F78586E}|localizationlib\\localizationlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\processer\\cs\\llm\\llmcsprocessor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A0860F5E-0D2E-3B8F-9F97-F6C5074027B7}|localizationlib_unit_tests.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\tests\\unit\\src\\regexcsprocessortests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{BB6BC777-A517-3A1B-B98D-2F6C0F78586E}|localizationlib\\localizationlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\processer\\cs\\regex\\regexcsprocessor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{BB6BC777-A517-3A1B-B98D-2F6C0F78586E}|localizationlib\\localizationlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\processer\\cs\\icsprocessor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A0860F5E-0D2E-3B8F-9F97-F6C5074027B7}|localizationlib_unit_tests.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\tests\\unit\\src\\stringinterpolationedgecasetests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A0860F5E-0D2E-3B8F-9F97-F6C5074027B7}|localizationlib_unit_tests.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\tests\\unit\\src\\stringinterpolationprocessortests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 1, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{e1b7d1f8-9b3c-49b1-8f4f-bfc63a88835d}"}, {"$type": "Document", "DocumentIndex": 0, "Title": "XamlProcessorTests.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\tests\\unit\\src\\XamlProcessorTests.cs", "RelativeDocumentMoniker": "..\\src\\XamlProcessorTests.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\tests\\unit\\src\\XamlProcessorTests.cs", "RelativeToolTip": "..\\src\\XamlProcessorTests.cs", "ViewState": "AgIAAGoDAAAAAAAAAAAAAIMDAAADAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-04T04:31:58.754Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "ProjectLocalizationExtractor.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\ProjectLocalizationExtractor.cs", "RelativeDocumentMoniker": "..\\..\\..\\src\\ProjectLocalizationExtractor.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\ProjectLocalizationExtractor.cs", "RelativeToolTip": "..\\..\\..\\src\\ProjectLocalizationExtractor.cs", "ViewState": "AgIAAF8AAAAAAAAAAIAwwBAAAAAnAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-07T09:12:47.446Z"}, {"$type": "Document", "DocumentIndex": 6, "Title": "ConsolidatedCppProcessorTests.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\tests\\unit\\src\\ConsolidatedCppProcessorTests.cs", "RelativeDocumentMoniker": "..\\src\\ConsolidatedCppProcessorTests.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\tests\\unit\\src\\ConsolidatedCppProcessorTests.cs", "RelativeToolTip": "..\\src\\ConsolidatedCppProcessorTests.cs", "ViewState": "AgIAAO4AAAAAAAAAAAAwwOgAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-25T06:17:07.934Z"}, {"$type": "Document", "DocumentIndex": 9, "Title": "ProjectTextExtractorConfigManager.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\common\\ProjectTextExtractorConfigManager.cs", "RelativeDocumentMoniker": "..\\..\\..\\src\\common\\ProjectTextExtractorConfigManager.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\common\\ProjectTextExtractorConfigManager.cs", "RelativeToolTip": "..\\..\\..\\src\\common\\ProjectTextExtractorConfigManager.cs", "ViewState": "AgIAADwAAAAAAAAAAAApwEUAAAAbAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-07T09:13:04.385Z"}, {"$type": "Document", "DocumentIndex": 7, "Title": "ConsolidatedCppProcessor.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\processer\\cpp\\ConsolidatedCppProcessor.cs", "RelativeDocumentMoniker": "..\\..\\..\\src\\processer\\cpp\\ConsolidatedCppProcessor.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\processer\\cpp\\ConsolidatedCppProcessor.cs", "RelativeToolTip": "..\\..\\..\\src\\processer\\cpp\\ConsolidatedCppProcessor.cs", "ViewState": "AgIAABIAAAAAAAAAAAAwwCAAAAAjAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-25T06:25:00.941Z"}, {"$type": "Document", "DocumentIndex": 5, "Title": "CsExtractor.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\processer\\cs\\CsExtractor.cs", "RelativeDocumentMoniker": "..\\..\\..\\src\\processer\\cs\\CsExtractor.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\processer\\cs\\CsExtractor.cs", "RelativeToolTip": "..\\..\\..\\src\\processer\\cs\\CsExtractor.cs", "ViewState": "AgIAAAkAAAAAAAAAAAAAABgAAAAWAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-25T06:02:21.117Z"}, {"$type": "Document", "DocumentIndex": 4, "Title": "ConsolidatedCsProcessor.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\processer\\cs\\ConsolidatedCsProcessor.cs", "RelativeDocumentMoniker": "..\\..\\..\\src\\processer\\cs\\ConsolidatedCsProcessor.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\processer\\cs\\ConsolidatedCsProcessor.cs", "RelativeToolTip": "..\\..\\..\\src\\processer\\cs\\ConsolidatedCsProcessor.cs", "ViewState": "AgIAABEAAAAAAAAAAAApwB0AAAAfAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-08T04:30:41.422Z"}, {"$type": "Document", "DocumentIndex": 12, "Title": "ProjectLocalizationReplacer.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\ProjectLocalizationReplacer.cs", "RelativeDocumentMoniker": "..\\..\\..\\src\\ProjectLocalizationReplacer.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\ProjectLocalizationReplacer.cs", "RelativeToolTip": "..\\..\\..\\src\\ProjectLocalizationReplacer.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAJwAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-30T07:19:26.938Z"}, {"$type": "Document", "DocumentIndex": 1, "Title": "XamlProcessor.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\processer\\xaml\\XamlProcessor.cs", "RelativeDocumentMoniker": "..\\..\\..\\src\\processer\\xaml\\XamlProcessor.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\processer\\xaml\\XamlProcessor.cs", "RelativeToolTip": "..\\..\\..\\src\\processer\\xaml\\XamlProcessor.cs", "ViewState": "AgIAAIEAAAAAAAAAAADwv4MAAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-22T09:06:33.179Z"}, {"$type": "Document", "DocumentIndex": 3, "Title": "CppExtractor.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\processer\\cpp\\CppExtractor.cs", "RelativeDocumentMoniker": "..\\..\\..\\src\\processer\\cpp\\CppExtractor.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\processer\\cpp\\CppExtractor.cs", "RelativeToolTip": "..\\..\\..\\src\\processer\\cpp\\CppExtractor.cs", "ViewState": "AgIAABEAAAAAAAAAAAAQwBkAAAASAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-25T07:04:35.727Z"}, {"$type": "Document", "DocumentIndex": 10, "Title": "RegexCppProcessorTests.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\tests\\unit\\src\\RegexCppProcessorTests.cs", "RelativeDocumentMoniker": "..\\src\\RegexCppProcessorTests.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\tests\\unit\\src\\RegexCppProcessorTests.cs", "RelativeToolTip": "..\\src\\RegexCppProcessorTests.cs", "ViewState": "AgIAAJsAAAAAAAAAAAAuwHUBAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-25T03:48:46.518Z"}, {"$type": "Document", "DocumentIndex": 13, "Title": "ProjectTextExtractorConfig.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\common\\ProjectTextExtractorConfig.cs", "RelativeDocumentMoniker": "..\\..\\..\\src\\common\\ProjectTextExtractorConfig.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\common\\ProjectTextExtractorConfig.cs", "RelativeToolTip": "..\\..\\..\\src\\common\\ProjectTextExtractorConfig.cs", "ViewState": "AgIAAJ4AAAAAAAAAAAA2wL8AAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-25T10:36:51.508Z"}, {"$type": "Document", "DocumentIndex": 8, "Title": "LocalizationUtils.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\utils\\LocalizationUtils.cs", "RelativeDocumentMoniker": "..\\..\\..\\src\\utils\\LocalizationUtils.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\utils\\LocalizationUtils.cs", "RelativeToolTip": "..\\..\\..\\src\\utils\\LocalizationUtils.cs", "ViewState": "AgIAAMgAAAAAAAAAAAAuwNYAAAAyAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-03-26T15:56:02.419Z"}, {"$type": "Document", "DocumentIndex": 11, "Title": "StringConcatenationProcessorTests.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\tests\\unit\\src\\StringConcatenationProcessorTests.cs", "RelativeDocumentMoniker": "..\\src\\StringConcatenationProcessorTests.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\tests\\unit\\src\\StringConcatenationProcessorTests.cs", "RelativeToolTip": "..\\src\\StringConcatenationProcessorTests.cs", "ViewState": "AgIAANoBAAAAAAAAAAAcwPYBAABPAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-16T12:47:14.095Z"}, {"$type": "Document", "DocumentIndex": 14, "Title": "IExtractor.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\IExtractor.cs", "RelativeDocumentMoniker": "..\\..\\..\\src\\IExtractor.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\IExtractor.cs", "RelativeToolTip": "..\\..\\..\\src\\IExtractor.cs", "ViewState": "AgIAAAkAAAAAAAAAAAAqwAoAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-08T04:30:25.397Z"}, {"$type": "Document", "DocumentIndex": 15, "Title": "XamlTextToCodeBehindProcessorTests.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\tests\\unit\\src\\XamlTextToCodeBehindProcessorTests.cs", "RelativeDocumentMoniker": "..\\src\\XamlTextToCodeBehindProcessorTests.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\tests\\unit\\src\\XamlTextToCodeBehindProcessorTests.cs", "RelativeToolTip": "..\\src\\XamlTextToCodeBehindProcessorTests.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAACUAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-28T05:45:04.095Z"}, {"$type": "Document", "DocumentIndex": 16, "Title": "XamlTextToCodeBehindProcessor.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\code-restructure\\XamlTextToCodeBehindProcessor.cs", "RelativeDocumentMoniker": "..\\..\\..\\src\\code-restructure\\XamlTextToCodeBehindProcessor.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\code-restructure\\XamlTextToCodeBehindProcessor.cs", "RelativeToolTip": "..\\..\\..\\src\\code-restructure\\XamlTextToCodeBehindProcessor.cs", "ViewState": "AgIAACkAAAAAAAAAAAAAADIAAABGAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-28T06:53:52.084Z"}, {"$type": "Document", "DocumentIndex": 17, "Title": "RoslynCsProcessor.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\processer\\cs\\roslyn\\RoslynCsProcessor.cs", "RelativeDocumentMoniker": "..\\..\\..\\src\\processer\\cs\\roslyn\\RoslynCsProcessor.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\processer\\cs\\roslyn\\RoslynCsProcessor.cs", "RelativeToolTip": "..\\..\\..\\src\\processer\\cs\\roslyn\\RoslynCsProcessor.cs", "ViewState": "AgIAAFQAAAAAAAAAAAAYwGEAAAAQAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-08T04:29:00.218Z"}, {"$type": "Document", "DocumentIndex": 18, "Title": "XamlExtractor.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\processer\\xaml\\XamlExtractor.cs", "RelativeDocumentMoniker": "..\\..\\..\\src\\processer\\xaml\\XamlExtractor.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\processer\\xaml\\XamlExtractor.cs", "RelativeToolTip": "..\\..\\..\\src\\processer\\xaml\\XamlExtractor.cs", "ViewState": "AgIAAB4AAAAAAAAAAAAzwDEAAABJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-08T04:31:26.691Z"}, {"$type": "Document", "DocumentIndex": 19, "Title": "ReportGeneratorTests.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\tests\\unit\\src\\ReportGeneratorTests.cs", "RelativeDocumentMoniker": "..\\src\\ReportGeneratorTests.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\tests\\unit\\src\\ReportGeneratorTests.cs", "RelativeToolTip": "..\\src\\ReportGeneratorTests.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-04T04:31:54.849Z"}, {"$type": "Document", "DocumentIndex": 20, "Title": "CsExtractorModeTests.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\tests\\unit\\src\\CsExtractorModeTests.cs", "RelativeDocumentMoniker": "..\\src\\CsExtractorModeTests.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\tests\\unit\\src\\CsExtractorModeTests.cs", "RelativeToolTip": "..\\src\\CsExtractorModeTests.cs", "ViewState": "AgIAAB8AAAAAAAAAAAArwFwAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-17T06:08:00.927Z"}, {"$type": "Document", "DocumentIndex": 21, "Title": "XamlExtractorModeTests.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\tests\\unit\\src\\XamlExtractorModeTests.cs", "RelativeDocumentMoniker": "..\\src\\XamlExtractorModeTests.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\tests\\unit\\src\\XamlExtractorModeTests.cs", "RelativeToolTip": "..\\src\\XamlExtractorModeTests.cs", "ViewState": "AgIAAFcAAAAAAAAAAAASwF8AAAAFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-04T10:00:33.954Z"}, {"$type": "Document", "DocumentIndex": 23, "Title": "LLMCsProcessor.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\processer\\cs\\llm\\LLMCsProcessor.cs", "RelativeDocumentMoniker": "..\\..\\..\\src\\processer\\cs\\llm\\LLMCsProcessor.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\processer\\cs\\llm\\LLMCsProcessor.cs", "RelativeToolTip": "..\\..\\..\\src\\processer\\cs\\llm\\LLMCsProcessor.cs", "ViewState": "AgIAAE0AAAAAAAAAAAAwwFQAAAA1AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-08T04:31:43.308Z"}, {"$type": "Document", "DocumentIndex": 22, "Title": "RoslynCsProcessorTests.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\tests\\unit\\src\\RoslynCsProcessorTests.cs", "RelativeDocumentMoniker": "..\\src\\RoslynCsProcessorTests.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\tests\\unit\\src\\RoslynCsProcessorTests.cs", "RelativeToolTip": "..\\src\\RoslynCsProcessorTests.cs", "ViewState": "AgIAADAAAAAAAAAAAAAiwDQAAAAFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-18T06:54:32.48Z"}, {"$type": "Document", "DocumentIndex": 25, "Title": "RegexCsProcessor.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\processer\\cs\\regex\\RegexCsProcessor.cs", "RelativeDocumentMoniker": "..\\..\\..\\src\\processer\\cs\\regex\\RegexCsProcessor.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\processer\\cs\\regex\\RegexCsProcessor.cs", "RelativeToolTip": "..\\..\\..\\src\\processer\\cs\\regex\\RegexCsProcessor.cs", "ViewState": "AgIAAAIAAAAAAAAAAAAqwBQAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-08T04:16:57.344Z"}, {"$type": "Document", "DocumentIndex": 26, "Title": "ICsProcessor.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\processer\\cs\\ICsProcessor.cs", "RelativeDocumentMoniker": "..\\..\\..\\src\\processer\\cs\\ICsProcessor.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\processer\\cs\\ICsProcessor.cs", "RelativeToolTip": "..\\..\\..\\src\\processer\\cs\\ICsProcessor.cs", "ViewState": "AgIAAAkAAAAAAAAAAAAAABwAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-08T04:13:05.892Z"}, {"$type": "Document", "DocumentIndex": 28, "Title": "StringInterpolationProcessorTests.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\tests\\unit\\src\\StringInterpolationProcessorTests.cs", "RelativeDocumentMoniker": "..\\src\\StringInterpolationProcessorTests.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\tests\\unit\\src\\StringInterpolationProcessorTests.cs", "RelativeToolTip": "..\\src\\StringInterpolationProcessorTests.cs", "ViewState": "AgIAAJYAAAAAAAAAAAASwLIAAACGAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-07T09:42:52.619Z"}, {"$type": "Document", "DocumentIndex": 27, "Title": "StringInterpolationEdgeCaseTests.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\tests\\unit\\src\\StringInterpolationEdgeCaseTests.cs", "RelativeDocumentMoniker": "..\\src\\StringInterpolationEdgeCaseTests.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\tests\\unit\\src\\StringInterpolationEdgeCaseTests.cs", "RelativeToolTip": "..\\src\\StringInterpolationEdgeCaseTests.cs", "ViewState": "AgIAABsAAAAAAAAAAAAYwCUAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-07T10:31:45.001Z"}, {"$type": "Document", "DocumentIndex": 24, "Title": "RegexCsProcessorTests.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\tests\\unit\\src\\RegexCsProcessorTests.cs", "RelativeDocumentMoniker": "..\\src\\RegexCsProcessorTests.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\tests\\unit\\src\\RegexCsProcessorTests.cs", "RelativeToolTip": "..\\src\\RegexCsProcessorTests.cs", "ViewState": "AgIAAEwAAAAAAAAAAAAqwFkAAAAHAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-19T03:24:43.157Z"}]}]}]}