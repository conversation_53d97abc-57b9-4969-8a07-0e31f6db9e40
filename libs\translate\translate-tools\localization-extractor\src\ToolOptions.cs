/*
 * Copyright 2021-present Alcon. All rights reserved.
 * Alcon proprietary/confidential.
 * Use is subject to license terms.
 */

using CommandLine;
using CommandLine.Text;
using LocalizationLib;
using System.Collections.Generic;

namespace LocalizationExtractor;

public class ToolOptions
{
    [Option('p', "projectPath", Required = true,
        HelpText = "Path to the project folder containing XAML files.")]
    public string ProjectPath { get; set; }

    // Report file is optional; if omitted, a default is used.
    [Option('r', "reportFile", Required = false,
        HelpText = "Path to the output report file. If omitted, defaults to a file next to the tool.")]
    public string ReportFile { get; set; }

    [Option('t', "projectTypes", Required = false, Default = "cs",
        HelpText = "Which project types to process. Valid values: 'cs' (C#), 'cpp' (C++), 'all' (both C# and C++). Comma-separated values also supported.")]
    public string ProjectTypes { get; set; }

    [Option("csProcessor", Required = false, Default = "consolidated",
        HelpText = "Which C# processor to use: 'roslyn', 'regex', 'consolidated' (default, combines regex with LLM verification if config available), or 'llm'.")]
    public string CsProcessorOption { get; set; }

    [Option("cppProcessor", Required = false, Default = "consolidated",
        HelpText = "Which C++ processor to use: 'regex', 'consolidated' (default, combines regex with LLM verification if config available), or 'llm'.")]
    public string CppProcessorOption { get; set; }

    [Option("convertStringInterpolation", Required = false, Default = true,
        HelpText = "Convert string interpolations to String.Format.")]
    public bool ConvertStringInterpolation { get; set; }

    [Option("convertStringConcatenation", Required = false, Default = true,
        HelpText = "Convert string concatenations (+) to String.Format.")]
    public bool ConvertStringConcatenation { get; set; }

    [Option("convertXamlTextToCodeBehind", Required = false, Default = true,
        HelpText = "Convert XAML text to code-behind.")]
    public bool ConvertXamlTextToCodeBehind { get; set; }

    [Option('m', "extractionMode", Required = false, Default = ExtractionMode.RestructureAndExtract,
        HelpText = "Extraction mode: RestructureOnly, ExtractionOnly, RestructureAndExtract (default).")]
    public ExtractionMode ExtractionMode { get; set; }

    public CodeRestructureOptions RestructureOptions => new CodeRestructureOptions
    {
        ConvertStringInterpolation = this.ConvertStringInterpolation,
        ConvertStringConcatenation = this.ConvertStringConcatenation,
        ConvertXamlTextToCodeBehind = this.ConvertXamlTextToCodeBehind
    };

    [Usage(ApplicationAlias = "LocalizeExtractor.exe")]
    public static IEnumerable<Example> Examples
    {
        get
        {
            yield return new Example("Process C# project with Roslyn processor", new ToolOptions
            {
                ProjectPath = @"C:\Projects\MyCSharpApp",
                ProjectTypes = "cs",
                CsProcessorOption = "roslyn"
            });

            yield return new Example("Process C++ project with consolidated processor", new ToolOptions
            {
                ProjectPath = @"C:\Projects\MyCppApp",
                ProjectTypes = "cpp",
                CppProcessorOption = "consolidated"
            });

            yield return new Example("Process both C# and C++ projects", new ToolOptions
            {
                ProjectPath = @"C:\Projects\MyMixedApp",
                ProjectTypes = "cs,cpp",
                CsProcessorOption = "roslyn",
                CppProcessorOption = "consolidated"
            });

            yield return new Example("Process all project types with default processors", new ToolOptions
            {
                ProjectPath = @"C:\Projects\MyApp",
                ProjectTypes = "all"
            });

            yield return new Example("Process C# project with custom report location", new ToolOptions
            {
                ProjectPath = @"C:\Projects\MyCSharpApp",
                ReportFile = @"C:\Projects\MyCSharpApp\Localization\Report.txt",
                ProjectTypes = "cs",
                CsProcessorOption = "consolidated"
            });
        }
    }
}
