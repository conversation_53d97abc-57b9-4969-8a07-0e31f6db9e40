﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{8C8FB788-720B-3C55-AAC6-7B5A125D0EC6}"
	ProjectSection(ProjectDependencies) = postProject
		{F0AEDFA8-0806-3B7C-BF34-C4180DF012B8} = {F0AEDFA8-0806-3B7C-BF34-C4180DF012B8}
		{D4859805-07F9-3863-BD39-0B33241A7E06} = {D4859805-07F9-3863-BD39-0B33241A7E06}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ZERO_CHECK", "ZERO_CHECK.vcxproj", "{F0AEDFA8-0806-3B7C-BF34-C4180DF012B8}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "translate_csharp", "translate_csharp.csproj", "{D4859805-07F9-3863-BD39-0B33241A7E06}"
	ProjectSection(ProjectDependencies) = postProject
		{F0AEDFA8-0806-3B7C-BF34-C4180DF012B8} = {F0AEDFA8-0806-3B7C-BF34-C4180DF012B8}
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Win32 = Debug|Win32
		Release|Win32 = Release|Win32
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{8C8FB788-720B-3C55-AAC6-7B5A125D0EC6}.Debug|Win32.ActiveCfg = Debug|Win32
		{8C8FB788-720B-3C55-AAC6-7B5A125D0EC6}.Release|Win32.ActiveCfg = Release|Win32
		{F0AEDFA8-0806-3B7C-BF34-C4180DF012B8}.Debug|Win32.ActiveCfg = Debug|Win32
		{F0AEDFA8-0806-3B7C-BF34-C4180DF012B8}.Debug|Win32.Build.0 = Debug|Win32
		{F0AEDFA8-0806-3B7C-BF34-C4180DF012B8}.Release|Win32.ActiveCfg = Release|Win32
		{F0AEDFA8-0806-3B7C-BF34-C4180DF012B8}.Release|Win32.Build.0 = Release|Win32
		{D4859805-07F9-3863-BD39-0B33241A7E06}.Debug|Win32.ActiveCfg = Debug|Any CPU
		{D4859805-07F9-3863-BD39-0B33241A7E06}.Debug|Win32.Build.0 = Debug|Any CPU
		{D4859805-07F9-3863-BD39-0B33241A7E06}.Release|Win32.ActiveCfg = Release|Any CPU
		{D4859805-07F9-3863-BD39-0B33241A7E06}.Release|Win32.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {98FB3FBE-F57D-34DC-939D-4F7FC9C8A9D7}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal
