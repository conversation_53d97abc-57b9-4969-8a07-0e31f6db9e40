{"format": 1, "restore": {"D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-text-manager\\build\\ui_extractor_tool\\ui_extractor_tool.csproj": {}}, "projects": {"D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-text-manager\\build\\ui_extractor_tool\\ui_extractor_tool.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-text-manager\\build\\ui_extractor_tool\\ui_extractor_tool.csproj", "projectName": "ui_extractor_tool", "projectPath": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-text-manager\\build\\ui_extractor_tool\\ui_extractor_tool.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-text-manager\\build\\ui_extractor_tool\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net48"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "D:\\kibisoft\\workarea\\VisionPlanner\\NGSS_nugets": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net48": {"targetAlias": "net48", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net48": {"targetAlias": "net48", "dependencies": {"CommunityToolkit.Mvvm": {"target": "Package", "version": "[8.2.2, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.400\\RuntimeIdentifierGraph.json"}}}}}