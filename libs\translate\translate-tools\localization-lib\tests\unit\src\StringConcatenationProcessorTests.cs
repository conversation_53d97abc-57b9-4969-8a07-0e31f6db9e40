using NUnit.Framework;
using System;
using System.IO;
using LocalizationLib;

namespace LocalizationLib.Tests
{
    [TestFixture]
    public class StringConcatenationProcessorTests
    {
        private StringConcatenationProcessor _processor;
        private string _tempFile;

        [SetUp]
        public void Setup()
        {
            _processor = new StringConcatenationProcessor();
            _tempFile = Path.GetTempFileName();
        }

        [TearDown]
        public void TearDown()
        {
            if (File.Exists(_tempFile))
            {
                File.Delete(_tempFile);
            }
        }

        [Test]
        public void ProcessFile_BasicConcatenation_ConvertedCorrectly()
        {
            // Arrange
            string code = "var message = \"Hello, \" + name + \"!\";";
            File.WriteAllText(_tempFile, code);

            // Act
            var result = _processor.ProcessFile(_tempFile);

            // Assert
            Assert.IsTrue(result.Changed, "File should be changed by conversion");
            Assert.AreEqual(
                "var message = string.Format(\"Hello, {0}!\", name);",
                result.UpdatedContent.Trim()
            );
            Assert.AreEqual(1, result.Conversions.Count);
        }

        [Test]
        public void ProcessFile_MultipleVariables_ConvertedCorrectly()
        {
            // Arrange
            string code = "var message = \"Hello, \" + firstName + \" \" + lastName + \"!\";";
            File.WriteAllText(_tempFile, code);

            // Act
            var result = _processor.ProcessFile(_tempFile);

            // Assert
            Assert.IsTrue(result.Changed);
            Assert.AreEqual(
                "var message = string.Format(\"Hello, {0} {1}!\", firstName, lastName);",
                result.UpdatedContent.Trim()
            );
        }

        [Test]
        public void ProcessFile_OnlyVariable_NoConversion()
        {
            // Arrange
            string code = "var message = name;";
            File.WriteAllText(_tempFile, code);

            // Act
            var result = _processor.ProcessFile(_tempFile);

            // Assert
            Assert.IsFalse(result.Changed);
            Assert.AreEqual(code, result.UpdatedContent.Trim());
        }

        [Test]
        public void ProcessFile_SkipComments()
        {
            // Arrange
            string code = "// var msg = \"Hello, \" + name;";
            File.WriteAllText(_tempFile, code);

            // Act
            var result = _processor.ProcessFile(_tempFile);

            // Assert
            Assert.IsFalse(result.Changed);
            Assert.AreEqual(code, result.UpdatedContent.Trim());
        }

        [Test]
        public void ProcessFile_SkipBlockComments()
        {
            // Arrange
            string code = "/* var msg = \"Hello, \" + name; */";
            File.WriteAllText(_tempFile, code);

            // Act
            var result = _processor.ProcessFile(_tempFile);

            // Assert
            Assert.IsFalse(result.Changed);
            Assert.AreEqual(code, result.UpdatedContent.Trim());
        }

        [Test]
        public void ProcessFile_ConcatenationWithMethodCall_ConvertedCorrectly()
        {
            string code = "var message = \"Hello, \" + user.Name + \"! You have \" + GetCount() + \" messages.\";";
            File.WriteAllText(_tempFile, code);
            var result = _processor.ProcessFile(_tempFile);
            Assert.IsTrue(result.Changed);
            Assert.AreEqual(
                "var message = string.Format(\"Hello, {0}! You have {1} messages.\", user.Name, GetCount());",
                result.UpdatedContent.Trim()
            );
        }

        [Test]
        public void ProcessFile_ConcatenationWithNumbers_ConvertedCorrectly()
        {
            string code = "var message = \"Result: \" + (a + b) + \", done.\";";
            File.WriteAllText(_tempFile, code);
            var result = _processor.ProcessFile(_tempFile);
            Assert.IsTrue(result.Changed);
            Assert.AreEqual(
                "var message = string.Format(\"Result: {0}, done.\", (a + b));",
                result.UpdatedContent.Trim()
            );
        }

        [Test]
        public void ProcessFile_ConcatenationWithWhitespace_ConvertedCorrectly()
        {
            string code = "var message = \"A\"   +   x   +   \"B\";";
            File.WriteAllText(_tempFile, code);
            var result = _processor.ProcessFile(_tempFile);
            Assert.IsTrue(result.Changed);
            Assert.AreEqual(
                "var message = string.Format(\"A{0}B\", x);",
                result.UpdatedContent.Trim()
            );
        }

        [Test]
        public void ProcessFile_MultipleConcatenationsInOneLine_ConvertedCorrectly()
        {
            string code = "var s = \"A\" + x + \"B\" + y + \"C\";";
            File.WriteAllText(_tempFile, code);
            var result = _processor.ProcessFile(_tempFile);
            Assert.IsTrue(result.Changed);
            Assert.AreEqual(
                "var s = string.Format(\"A{0}B{1}C\", x, y);",
                result.UpdatedContent.Trim()
            );
        }

        [Test]
        public void ProcessFile_ConcatenationWithChainedMethodCalls_ConvertedCorrectly()
        {
            string code = "var message = \"Hello, \" + user.ToString().Trim() + \"!\";";
            File.WriteAllText(_tempFile, code);
            var result = _processor.ProcessFile(_tempFile);
            Assert.IsTrue(result.Changed);
            Assert.AreEqual(
                "var message = string.Format(\"Hello, {0}!\", user.ToString().Trim());",
                result.UpdatedContent.Trim()
            );
        }

        [Test]
        public void ProcessFile_ConcatenationInComment_NotConverted()
        {
            string code = "// var s = \"Hello, \" + name;";
            File.WriteAllText(_tempFile, code);
            var result = _processor.ProcessFile(_tempFile);
            Assert.IsFalse(result.Changed);
            Assert.AreEqual(code, result.UpdatedContent.Trim());
        }

        [Test]
        public void ProcessFile_EscapedQuotes_ConvertedCorrectly()
        {
            string code = "var s = \"She said, \\\"Hi\\\"\" + name + \"!\";";
            File.WriteAllText(_tempFile, code);
            var result = _processor.ProcessFile(_tempFile);
            Assert.IsTrue(result.Changed);
            Assert.AreEqual(
                "var s = string.Format(\"She said, \\\"Hi\\\"{0}!\", name);",
                result.UpdatedContent.Trim()
            );
        }

        [Test]
        public void ProcessFile_ParenthesizedExpression_ConvertedCorrectly()
        {
            string code = "var s = \"Sum: \" + (a + b) + \"!\";";
            File.WriteAllText(_tempFile, code);
            var result = _processor.ProcessFile(_tempFile);
            Assert.IsTrue(result.Changed);
            Assert.AreEqual(
                "var s = string.Format(\"Sum: {0}!\", (a + b));",
                result.UpdatedContent.Trim()
            );
        }

        [Test]
        public void ProcessFile_NestedParentheses_ConvertedCorrectly()
        {
            string code = "var s = \"Value: \" + (a + (b * c)) + \" units\";";
            File.WriteAllText(_tempFile, code);
            var result = _processor.ProcessFile(_tempFile);
            Assert.IsTrue(result.Changed);
            Assert.AreEqual(
                "var s = string.Format(\"Value: {0} units\", (a + (b * c)));",
                result.UpdatedContent.Trim()
            );
        }

        [Test]
        public void ProcessFile_TernaryExpression_ConvertedCorrectly()
        {
            string code = "var s = \"Result: \" + (flag ? \"Yes\" : \"No\");";
            File.WriteAllText(_tempFile, code);
            var result = _processor.ProcessFile(_tempFile);
            Assert.IsTrue(result.Changed);
            Assert.AreEqual(
                "var s = string.Format(\"Result: {0}\", (flag ? \"Yes\" : \"No\"));",
                result.UpdatedContent.Trim()
            );
        }

        [Test]
        public void ProcessFile_MultilineConcatenation_ConvertedCorrectly()
        {
            string code = "var s = \"A\" + \n    x +\n    \"B\";";
            File.WriteAllText(_tempFile, code);
            var result = _processor.ProcessFile(_tempFile);
            Assert.IsFalse(result.Changed);
        }

        [Test]
        public void ProcessFile_ConcatenationWithCommentAtEnd_ConvertedCorrectly()
        {
            string code = "var s = \"Hello, \" + name + \"!\"; // Greet the user";
            File.WriteAllText(_tempFile, code);
            var result = _processor.ProcessFile(_tempFile);
            Assert.IsTrue(result.Changed);
            Assert.AreEqual(
                "var s = string.Format(\"Hello, {0}!\", name); // Greet the user",
                result.UpdatedContent.Trim()
            );
        }

        [Test]
        public void ProcessFile_NoStringLiteral_NoConversion()
        {
            string code = "var s = a + b;";
            File.WriteAllText(_tempFile, code);
            var result = _processor.ProcessFile(_tempFile);
            Assert.IsFalse(result.Changed);
            Assert.AreEqual(code, result.UpdatedContent.Trim());
        }

        [Test]
        public void ProcessFile_WhitespaceConcatenation_ConvertedCorrectly()
        {
            string code = "var s = \"Test\" +   \" \";";
            File.WriteAllText(_tempFile, code);
            var result = _processor.ProcessFile(_tempFile);
            Assert.IsTrue(result.Changed);
            Assert.AreEqual(
                "var s = \"Test \";",
                result.UpdatedContent.Trim()
            );
        }

        [Test]
        public void ProcessFile_WhitespaceOnlyConcatenation_ConvertedCorrectly()
        {
            string code = "var s = \"  \" + \" \";";
            File.WriteAllText(_tempFile, code);
            var result = _processor.ProcessFile(_tempFile);
            Assert.IsTrue(result.Changed);
            Assert.AreEqual(
                "var s = \"   \";",
                result.UpdatedContent.Trim()
            );
        }

        [Test]
        public void ProcessFile_ConcatenationWithEmptyString_ConvertedCorrectly()
        {
            string code = "var s = \"Hello\" + \"\";";
            File.WriteAllText(_tempFile, code);
            var result = _processor.ProcessFile(_tempFile);
            Assert.IsTrue(result.Changed);
            Assert.AreEqual(
                "var s = \"Hello\";",
                result.UpdatedContent.Trim()
            );
        }

        [Test]
        public void ProcessFile_ConcatenationWithMultipleLiterals_ConvertedCorrectly()
        {
            string code = "var s = \"A\" + \"B\" + \"C\";";
            File.WriteAllText(_tempFile, code);
            var result = _processor.ProcessFile(_tempFile);
            Assert.IsTrue(result.Changed);
            Assert.AreEqual(
                "var s = \"ABC\";",
                result.UpdatedContent.Trim()
            );
        }

        [Test]
        public void ProcessFile_ArrayIndexer_ConvertedCorrectly()
        {
            string code = "var s = \"Item: \" + arr[0] + \"!\";";
            File.WriteAllText(_tempFile, code);
            var result = _processor.ProcessFile(_tempFile);
            Assert.IsTrue(result.Changed);
            Assert.AreEqual(
                "var s = string.Format(\"Item: {0}!\", arr[0]);",
                result.UpdatedContent.Trim()
            );
        }

        [Test]
        public void ProcessFile_ObjectInitializerProperty_ConvertedCorrectly()
        {
            string code = @"var dlg = new ConfirmationDlg() { TitleTxt = ""Error "" + (int)err, MessageTxt = msg };";
            File.WriteAllText(_tempFile, code);
            var result = _processor.ProcessFile(_tempFile);
            Assert.IsTrue(result.Changed);
            Assert.AreEqual(
                "var dlg = new ConfirmationDlg() { TitleTxt = string.Format(\"Error {0}\", (int)err), MessageTxt = msg };",
                result.UpdatedContent.Trim()
            );
        }

        [Test]
        public void ProcessFile_MultilineComplexConcatenation_Skipped()
        {
            string code =
        @"string template = ""<DataTemplate xmlns=""[http://schemas.microsoft.com/winfx/2006/xaml/presentation""](http://schemas.microsoft.com/winfx/2006/xaml/presentation"") xmlns:GeneralUiWidget=""clr-namespace:TRICS.Planner.UI.General.Widget;assembly=TRICSPlannerUIGeneral"">"" +
    ""<StackPanel Orientation=""Horizontal"" Height=""40"" IsEnabled=""{Binding IsEditMode}"">"" +
    ""<GeneralUiWidget:ValidatedParamEditBox DataContext=""{Binding Columns["" + i.ToString() + ""].Arc1AngleParam}"" Format=""N0"" HorizontalAlignment=""Left"" Margin=""0,0,0,0"" Width=""62"" />"" +
    ""</StackPanel>"";";
            File.WriteAllText(_tempFile, code);
            var result = _processor.ProcessFile(_tempFile);
            Assert.IsFalse(result.Changed, "Processor should skip complex multiline concatenations");
            Assert.AreEqual(code, result.UpdatedContent.Trim(), "Multiline complex concatenation should be left unchanged");
        }

        [Test]
        public void ProcessString_CurvatureConcatenation_ConvertedCorrectly()
        {
            // Arrange
            string code = @"curvature1 = ""K1 ("" + Fields.G1 + "")""; curvature2 = ""K2 ("" + Fields.G2 + "")"";";

            // Act
            var result = _processor.ProcessString(code);

            // Log conversion result for debugging
            TestContext.WriteLine("UpdatedContent:\n" + result.UpdatedContent);
            foreach (var conv in result.Conversions)
            {
                TestContext.WriteLine($"Line {conv.line}, Col {conv.col}: '{conv.original}' => '{conv.converted}'");
            }

            // Assert
            Assert.IsTrue(result.Changed, "Processor should detect and convert concatenation.");
            Assert.That(result.UpdatedContent, Does.Contain("string.Format(\"K1 ({0})\", Fields.G1)"));
            Assert.That(result.UpdatedContent, Does.Contain("curvature2 = string.Format(\"K2 ({0})\", Fields.G2);"));
            Assert.AreEqual(1, result.Conversions.Count, "Should only convert the concatenation, not the already-formatted string.");
        }

        [Test]
        public void ProcessString_SimpleReturnStatement_ConvertedCorrectly()
        {
            // Arrange
            string code = "return value + \" \" + suffix + \"s\";";

            // Act
            var result = _processor.ProcessString(code);

            // Log conversion result for debugging
            TestContext.WriteLine("UpdatedContent:\n" + result.UpdatedContent);
            foreach (var conv in result.Conversions)
            {
                TestContext.WriteLine($"Line {conv.line}, Col {conv.col}: '{conv.original}' => '{conv.converted}'");
            }

            // Assert
            Assert.IsTrue(result.Changed, "Processor should detect and convert return statement concatenation.");
            Assert.AreEqual("return string.Format(\"{0} {1}s\", value, suffix);", result.UpdatedContent.Trim());
            Assert.AreEqual(1, result.Conversions.Count);
        }

        [Test]
        public void ProcessString_ComplexReturnStatement_ConvertedCorrectly()
        {
            // Arrange
            string code = "return \"Error processing file: \" + ex.GetType().Name + \" occurred at \" + DateTime.Now.ToString() + \".\";";

            // Act
            var result = _processor.ProcessString(code);

            // Log conversion result for debugging
            TestContext.WriteLine("UpdatedContent:\n" + result.UpdatedContent);
            foreach (var conv in result.Conversions)
            {
                TestContext.WriteLine($"Line {conv.line}, Col {conv.col}: '{conv.original}' => '{conv.converted}'");
            }

            // Assert
            Assert.IsTrue(result.Changed, "Processor should detect and convert complex return statement concatenation.");
            string expected = "return string.Format(\"Error processing file: {0} occurred at {1}.\", ex.GetType().Name, DateTime.Now.ToString());";
            Assert.AreEqual(expected, result.UpdatedContent.Trim());
            Assert.AreEqual(1, result.Conversions.Count);
        }

        [Test]
        public void ProcessString_TernaryWithConcatenation_ConvertedCorrectly()
        {
            // Arrange
            string code = "string signoutURL = !String.IsNullOrEmpty(DHSUrl) ? DHSUrl.TrimEnd('/') + \"/signin?uid=\" + \"signout\" : \"\";";

            // Act
            var result = _processor.ProcessString(code);

            // Log conversion result for debugging
            TestContext.WriteLine("UpdatedContent:\n" + result.UpdatedContent);
            foreach (var conv in result.Conversions)
            {
                TestContext.WriteLine($"Line {conv.line}, Col {conv.col}: '{conv.original}' => '{conv.converted}'");
            }

            // Assert
            Assert.IsTrue(result.Changed, "Processor should detect and convert string concatenation within ternary operators.");
            string expected = "string signoutURL = !String.IsNullOrEmpty(DHSUrl) ? string.Format(\"{0}/signin?uid=signout\", DHSUrl.TrimEnd('/')) : \"\";";
            Assert.AreEqual(expected, result.UpdatedContent.Trim());
            Assert.AreEqual(1, result.Conversions.Count);
        }
        
        [Test]
        public void ProcessString_TernaryWithConcatenationInOneBranch_ConvertedCorrectly()
        {
            // Arrange - Test case for ChartNumberLabel scenario
            string code = "public string ChartNumberLabel => string.IsNullOrEmpty(Patient.ChartNumber) ? \"Id: none\" : \"Id: \" + Patient.ChartNumber;";
            
            // Act
            var result = _processor.ProcessString(code);
            
            // Log for debugging
            TestContext.WriteLine("UpdatedContent:\n" + result.UpdatedContent);
            
            // Assert
            Assert.IsTrue(result.Changed, "Processor should convert ternary with concatenation in only one branch");
            string expected = "public string ChartNumberLabel => string.IsNullOrEmpty(Patient.ChartNumber) ? \"Id: none\" : string.Format(\"Id: {0}\", Patient.ChartNumber);";
            Assert.AreEqual(expected, result.UpdatedContent.Trim());
            Assert.AreEqual(1, result.Conversions.Count);
        }
        
        [Test]
        public void ProcessString_ComplexTernaryWithConcatenationInBothBranches_ShouldBeSkipped()
        {
            // Arrange - The complex case shown in the diff
            string code = "string curvature1 = K1EditBox.Visibility == Visibility.Visible ? \"K1 (\" + K1EditBox.TxtBox.Text + \")\" : \"R1 (\" + R1EditBox.TxtBox.Text + \")\";";
            
            // Act
            var result = _processor.ProcessString(code);
            
            // Log for debugging
            TestContext.WriteLine("UpdatedContent:\n" + result.UpdatedContent);
            
            // Assert
            Assert.IsFalse(result.Changed, "Complex ternary with concatenation in both branches should be skipped");
            Assert.AreEqual(code, result.UpdatedContent.Trim(), "The output should be identical to the input");
            Assert.AreEqual(0, result.Conversions.Count, "No conversions should be recorded");
        }
        
        [Test]
        public void ProcessString_MultilineTernaryWithConcatenationInBothBranches_ShouldBeSkipped()
        {
            // Arrange - Multi-line version of the complex case
            string code = @"string curvature2 = K2EditBox.Visibility == Visibility.Visible 
                ? ""K2 ("" + K2EditBox.TxtBox.Text + "")"" 
                : ""R2 ("" + R2EditBox.TxtBox.Text + "")"";";
            
            // Act
            var result = _processor.ProcessString(code);
            
            // Log for debugging
            TestContext.WriteLine("UpdatedContent:\n" + result.UpdatedContent);
            
            // Assert
            Assert.IsFalse(result.Changed, "Multi-line complex ternary with concatenation in both branches should be skipped");
            Assert.AreEqual(code, result.UpdatedContent.Trim(), "The output should be identical to the input");
            Assert.AreEqual(0, result.Conversions.Count, "No conversions should be recorded");
        }

        [Test]
        public void ProcessString_MultiLineStringConcatenation_ConvertedCorrectly()
        {
            // Arrange
            string code = @"string mekey =
    ""["" + My_Master_Control.Surgeons.Surgeon_Key
    + ""]["" + My_Master_Control.Surgeons.One_Type_of_Surgery + ""][#E]"";";

            // Act
            var result = _processor.ProcessString(code);

            // Log conversion result for debugging
            TestContext.WriteLine("UpdatedContent:\n" + result.UpdatedContent);
            foreach (var conv in result.Conversions)
            {
                TestContext.WriteLine($"Line {conv.line}, Col {conv.col}: '{conv.original}' => '{conv.converted}'");
            }

            // Assert
            Assert.IsTrue(result.Changed, "Processor should detect and convert multi-line string concatenation.");
            string expected = "string mekey = string.Format(\"[{0}][{1}][#E]\", My_Master_Control.Surgeons.Surgeon_Key, My_Master_Control.Surgeons.One_Type_of_Surgery);";
            Assert.That(result.UpdatedContent, Does.Contain(expected), "The result should contain the expected format string with [#E] suffix");
            Assert.AreEqual(1, result.Conversions.Count);
        }

        [Test]
        public void ProcessFile_MultiLineStringConcatenation_ConvertedCorrectly()
        {
            // Arrange
            string code = @"string mekey =
    ""["" + My_Master_Control.Surgeons.Surgeon_Key
    + ""]["" + My_Master_Control.Surgeons.One_Type_of_Surgery + ""][#E]"";";
            File.WriteAllText(_tempFile, code);

            // Act
            var result = _processor.ProcessFile(_tempFile);

            // Log conversion result for debugging
            TestContext.WriteLine("UpdatedContent:\n" + result.UpdatedContent);
            foreach (var conv in result.Conversions)
            {
                TestContext.WriteLine($"Line {conv.line}, Col {conv.col}: '{conv.original}' => '{conv.converted}'");
            }

            // Assert
            Assert.IsTrue(result.Changed, "Processor should detect and convert multi-line string concatenation from file.");
            Assert.That(result.UpdatedContent, Does.Contain("[#E]"), "The result should preserve the [#E] suffix");
            Assert.That(result.UpdatedContent, Does.Contain("string.Format(\"[{0}][{1}][#E]\""), "The result should contain the proper format string");
            Assert.AreEqual(1, result.Conversions.Count);
        }

        [Test]
        public void ProcessString_FormatStringConstruction_ShouldBeSkipped()
        {
            // Arrange - Test case for format string construction that should not be converted
            string code = "_format = string.IsNullOrEmpty(value) ? \"{0}\" : \"{0:\" + value + \"}\";";
            
            // Act
            var result = _processor.ProcessString(code);
            
            // Log for debugging
            TestContext.WriteLine("UpdatedContent:\n" + result.UpdatedContent);
            
            // Assert
            Assert.IsFalse(result.Changed, "Format string construction should be skipped");
            Assert.AreEqual(code, result.UpdatedContent.Trim(), "The output should be identical to the input");
            Assert.AreEqual(0, result.Conversions.Count, "No conversions should be recorded");
        }

        [Test]
        public void ProcessString_FormatStringConstructionWithVariable_ShouldBeSkipped()
        {
            // Arrange - Test case for format string construction with variable that should not be converted
            string code = "string format = \"{0:\" + precision + \"}\";";
            
            // Act
            var result = _processor.ProcessString(code);
            
            // Log for debugging
            TestContext.WriteLine("UpdatedContent:\n" + result.UpdatedContent);
            
            // Assert
            Assert.IsFalse(result.Changed, "Format string construction with variable should be skipped");
            Assert.AreEqual(code, result.UpdatedContent.Trim(), "The output should be identical to the input");
            Assert.AreEqual(0, result.Conversions.Count, "No conversions should be recorded");
        }
    }
}
