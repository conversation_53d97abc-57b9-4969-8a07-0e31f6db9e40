D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\CMakeFiles\3.30.1\CompilerIdCSharp\obj\x64\Debug\CompilerIdCSharp.csproj.AssemblyReference.cache
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\CMakeFiles\3.30.1\CompilerIdCSharp\obj\x64\Debug\CompilerIdCSharp.csproj.CoreCompileInputs.cache
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\CMakeFiles\3.30.1\CompilerIdCSharp\obj\x64\Debug\CompilerIdCSharp.exe
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\CMakeFiles\3.30.1\CompilerIdCSharp\obj\x64\Debug\CompilerIdCSharp.pdb
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\CMakeFiles\3.30.1\CompilerIdCSharp\CompilerIdCSharp.exe
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\CMakeFiles\3.30.1\CompilerIdCSharp\CompilerIdCSharp.pdb
