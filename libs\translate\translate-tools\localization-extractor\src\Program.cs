/*
 * Copyright 2021-present Alcon. All rights reserved.
 * Alcon proprietary/confidential.
 * Use is subject to license terms.
 */

using CommandLine;
using CommandLine.Text;
using LocalizationLib;
using System;
using System.Collections.Generic;

namespace LocalizationExtractor;

class Program
{
    public static void Main(string[] args)
    {
        // Parse the command line arguments
        var parserResult = Parser.Default.ParseArguments<ToolOptions>(args);
        
        parserResult.WithParsed<ToolOptions>(options =>
        {
            try
            {
                Console.WriteLine($"Starting with options:");
                Console.WriteLine($"  Project path: {options.ProjectPath}");
                Console.WriteLine($"  Report file: {options.ReportFile ?? "<default>"}");
                Console.WriteLine($"  Project types: {options.ProjectTypes}");
                Console.WriteLine($"  C# processor: {options.CsProcessorOption}");
                Console.WriteLine($"  C++ processor: {options.CppProcessorOption}");
                Console.WriteLine($"  Extraction mode: {options.ExtractionMode}");
                Console.WriteLine($"  ConvertStringInterpolation: {options.ConvertStringInterpolation}");
                Console.WriteLine($"  ConvertStringConcatenation: {options.ConvertStringConcatenation}");
                Console.WriteLine($"  ConvertXamlTextToCodeBehind: {options.ConvertXamlTextToCodeBehind}");


                var extractor = new ProjectLocalizationExtractor();
                
                extractor.Run(
                    options.ProjectPath,
                    options.ReportFile ?? "localization_extractor_report.txt",
                    options.ProjectTypes,
                    options.CsProcessorOption,
                    options.CppProcessorOption,
                    options.RestructureOptions,
                    options.ExtractionMode
                );

                Console.WriteLine("Done.");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
                Console.WriteLine(ex.StackTrace);
            }
        });

        parserResult.WithNotParsed(errors =>
        {
            Console.WriteLine("Invalid command line options:");
            foreach (var error in errors)
            {
                Console.WriteLine($"  - {error}");
            }
            Console.WriteLine("Use --help for usage information.");
        });
    }

    private static void HandleParseError(IEnumerable<Error> errors)
    {
        // Custom logging can be added here if needed.
        _ = errors; // Suppress unused parameter warning
    }
}
