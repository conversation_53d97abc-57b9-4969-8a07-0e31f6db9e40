/*
 * Copyright 2021-present Alcon. All rights reserved.
 * Alcon proprietary/confidential.
 * Use is subject to license terms.
 */

using System;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;

namespace LocalizationLib;

public class CsReplacer : IReplacer
{
    private readonly string _fileExtension = ".cs";
    public string SupportedFileTypes => _fileExtension;
    public FileOffsetManager _offsetManager = new FileOffsetManager();

    public ReplaceStatus Replace(string filePath, ExtractedTextInfo text)
    {
        string escapedText = Regex.Replace(text.OriginalText, @"\\(?![\\nrt\""])", @"\\") // Escape \ only if it's not followed by a known character (n, r, t, ")
                                .Replace("\"", "\\\"") // Escape double quotes
                                .Replace("\n", "\\n")  // Escape newlines (and other escape sequences if needed)
                                .Replace("\r", "\\r")
                                .Replace("\t", "\\t");
        Console.WriteLine($"\nProcessing string \"{escapedText}\" from {text.FilePath} inside CsReplacer..");

        if (!FirstPassValidation(filePath, text))
        {
            Console.WriteLine();
            return ReplaceStatus.NotReplaced;
        }

        string content = File.ReadAllText(filePath);
        string updatedContent = content;
        ReplaceStatus replaceStatus = ReplaceStatus.NotReplaced;
        updatedContent = HandleDescriptionAttribute(filePath, text, ref replaceStatus);
        if (replaceStatus != ReplaceStatus.Replaced) // no description attribute
        {
            var translateCallParams = "";
            if (text.LangTextID == null)
            {
                translateCallParams = $"\"en:{escapedText}\"";
            }
            else
            {
                translateCallParams = $"\"id:{text.LangTextID}|en:{escapedText}\"";
            }

            string replacement = $"Translator.Instance.TranslateM({translateCallParams})";
            updatedContent = ReplaceLineInsideCsFile(filePath, replacement, escapedText, ref text, ref replaceStatus);
        }
        if (replaceStatus == ReplaceStatus.Replaced)
        {
            string contentWithUsingLib = AddUsingStatement(filePath, updatedContent, "Alcon.Interop.Translate");

            // update line/column inside extracted text only if it has been changed
            var lineOffset = _offsetManager.LineOffset(filePath, text.LineNumber);
            var columnOffset = _offsetManager.ColumnOffset(filePath, text.LineNumber);
            text.UpdatedLineNumber = lineOffset != 0 ? text.LineNumber + lineOffset : 0;
            text.UpdatedColumn = columnOffset != 0 ? text.Column + columnOffset : 0;

            File.WriteAllText(filePath, contentWithUsingLib);
            Console.WriteLine($"File changed : {text.FilePath}\n");
        }
        return replaceStatus;
    }

    /// <summary>
    /// Add required using lib
    /// </summary>
    /// <param name="filePath"></param>
    /// <param name="content"></param>
    /// <param name="libName"></param>
    /// <returns>Updated file content</returns>
    private string AddUsingStatement(string filePath, string content, string libName)
    {
        var lines = content.Split("\n").ToList();
        if (!lines.Any(line => line.Trim().Contains(libName)))
        {
            int lastUsingIndex = -1;

            for (int i = 0; i < lines.Count; i++)
            {
                string trimmedLine = lines[i].Trim();

                if (trimmedLine.StartsWith("using "))
                {
                    lastUsingIndex = i;  // Keep track of the last `using` statement
                }
                else if (trimmedLine.StartsWith("namespace "))
                {
                    break;  // Stop searching once we reach the namespace declaration
                }
            }

            if (lastUsingIndex != -1)
            {
                // Add the new using statement
                int insertionPos = lastUsingIndex + 1;
                lines.Insert(insertionPos, $"using {libName};");
                _offsetManager.AddLineOffset(filePath, insertionPos, 1);
                Console.WriteLine("Added using statement.");
            }
            else
            {
                // No `using` found before `namespace`, add at the very top
                Console.WriteLine("No using found. Add it at the very top.");
                lines.Insert(0, $"using {libName};");
                _offsetManager.AddLineOffset(filePath, 0, 1);
            }
        }
        else
        {
            Console.WriteLine($"{libName} already added");
        }
        return string.Join('\n', lines);
    }

    /// <summary>
    /// Replaces the originalValue with the replacement on the lineNumber
    /// Updates replaceStatus with Replaced, NotReplaced, AlreadyReplaced
    /// Adds notes inside text.Notes if the replacement couldn't be done
    /// </summary>
    /// <param name="filePath"></param>
    /// <param name="replacement"></param>
    /// <param name="text"></param>
    /// <param name="replaceStatus"></param>
    /// <returns>Updated file content</returns>
    private string ReplaceLineInsideCsFile(string filePath, string replacement, string escapedText, ref ExtractedTextInfo text, ref ReplaceStatus replaceStatus)
    {
        string content = File.ReadAllText(filePath);
        var lines = content.Split("\n").ToList();
        var lineIndex = text.LineNumber + _offsetManager.LineOffset(filePath, text.LineNumber) - 1; // 0 based index
        var columnIndex = text.Column - 1; // 0 based index
        if (lineIndex < 0 || lineIndex >= lines.Count)
        {
            Console.WriteLine($"Invalid line number: {text.LineNumber}");
            replaceStatus = ReplaceStatus.NotReplaced;
            return content;
        }

        string lineText = lines[lineIndex];
        string toFind = $"\"{escapedText}\"";
        int idx = lineText.IndexOf(toFind);
        if (idx >= 0)
        {
            if (idx != columnIndex)
            {
                // adjust column with offset if existing
                columnIndex += _offsetManager.ColumnOffset(filePath, text.LineNumber);
                if (columnIndex >= 0 && columnIndex + toFind.Length < lineText.Length && lineText.Substring(columnIndex, toFind.Length) == toFind)
                {
                    idx = columnIndex;
                }
            }
            if (!ReplacementAlreadyPresent(idx, replacement, lineText))
            {
                int charIdx = lineText.IndexOf("$");
                if (charIdx >= 0 && charIdx == idx - 1)
                {
                    // remove $ char as well
                    idx--;
                    toFind = "$" + toFind;
                }
                lineText = lineText.Remove(idx, toFind.Length).Insert(idx, replacement);
                _offsetManager.ComputeColumnOffset(filePath, lineText, escapedText, text.LineNumber, columnIndex, idx);
                lines[lineIndex] = lineText;
                replaceStatus = ReplaceStatus.Replaced;
            }
            else
            {
                replaceStatus = ReplaceStatus.AlreadyReplaced;
                Console.WriteLine("Replacement already present on line.");
            }
        }
        else
        {
            if (ReplacementAlreadyPresent(columnIndex, replacement, lineText))
            {
                replaceStatus = ReplaceStatus.AlreadyReplaced;
                Console.WriteLine("Replacement already present on line.");
            }
            else
            {
                // if quoted original text wasn't found, translate call can appear without languageId - using "en:{originalText}"
                string englishText = $"\"en:{escapedText}\"";
                var englishTextIdx = lineText.IndexOf(englishText);
                if (englishTextIdx >= 0 && ReplacementAlreadyPresent(englishTextIdx, replacement, lineText))
                {
                    replaceStatus = ReplaceStatus.AlreadyReplaced;
                    Console.WriteLine("Replacement already present on line.");
                }
                else
                {
                    replaceStatus = ReplaceStatus.NotReplaced;
                    text.Notes.Add($"Replacer: String {toFind} wasn't found on line number \"{lineIndex + 1}\".");
                    Console.WriteLine($"String {toFind} wasn't found on line number \"{lineIndex + 1}\". Notes updated!");
                }
            }
        }
        return string.Join('\n', lines);
    }

    /// <summary>
    /// Checks if translate call is already used for the originalText.
    /// </summary>
    /// <param name="originalTextIdx"></param>
    /// <param name="replacement"></param>
    /// <param name="lineText"></param>
    /// <returns>True if replacement is already on line for the originalText, false otherwise</returns>
    private bool ReplacementAlreadyPresent(int originalTextIdx, string replacement, string lineText)
    {
        var replacementIdx = lineText.IndexOf(replacement);
        if (replacementIdx > 0 && replacementIdx + replacement.Length > originalTextIdx)
        {
            return true;
        }
        else
        {
            // translate binding can still appear with different converterParameter: containing "id:<id>|en:<text>, or just en:<text>
            int converterParamIdx = replacement.IndexOf("id:");
            if (converterParamIdx < 0)
            {
                converterParamIdx = replacement.IndexOf("en:");
            }
            if (converterParamIdx < 0)
            {
                return false;
            }
            string translateBinding = replacement.Substring(0, converterParamIdx);
            int translateBindingIdx = lineText.IndexOf(translateBinding);
            if (translateBindingIdx > 0 && translateBindingIdx == originalTextIdx)
            {
                return true;
            }
        }
        return false;
    }

    /// <summary>
    /// Handles description attribute
    /// </summary>
    /// <param name="filePath"></param>
    /// <param name="text"></param>
    /// <param name="replaceStatus"></param>
    /// <returns>Content with updated LocalizedDescription</returns>
    private string HandleDescriptionAttribute(string filePath, ExtractedTextInfo text, ref ReplaceStatus replaceStatus)
    {
        string content = File.ReadAllText(filePath);
        var lines = content.Split("\n").ToList();
        var lineIndex = text.LineNumber + _offsetManager.LineOffset(filePath, text.LineNumber) - 1; // 0 based index
        var columnIndex = text.Column + _offsetManager.ColumnOffset(filePath, lineIndex + 1) - 1; // 0 based index
        if (lineIndex < 0 || lineIndex >= lines.Count)
        {
            Console.WriteLine($"Invalid line number: {text.LineNumber}");
            return content;
        }

        string lineText = lines[lineIndex];
        string toFind = $"\"{text.OriginalText}\"";
        int idx = lineText.IndexOf(toFind);
        string pattern = @"\[Description\(""([^""]*)""\)\]";
        Match match = Regex.Match(lineText, pattern);
        if (match.Success && match.Groups[1].Value.Contains(text.OriginalText))
        {
            string replacement = $"[LocalizedDescription(\"{text.LangTextID}\",\"{text.OriginalText}\")]";
            lineText = Regex.Replace(lineText, pattern, replacement);
            lines[lineIndex] = lineText;
            replaceStatus = ReplaceStatus.Replaced;
            _offsetManager.ComputeColumnOffset(filePath, lineText, text.OriginalText, text.LineNumber, columnIndex, idx);
        }
        return string.Join("\n", lines);
    }

    /// <summary>
    /// Validates
    /// 1. If filePath exists
    /// 2. If the fileType can be handled
    /// </summary>
    /// <param name="filePath"></param>
    /// <param name="text"></param>
    /// <returns>True if the validation passes, false otherwise</returns>
    private bool FirstPassValidation(string filePath, ExtractedTextInfo text)
    {
        if (!Path.Exists(filePath))
        {
            Console.WriteLine($"FilePath {filePath} doesn't exists!");
            return false;
        }
        if (Path.GetExtension(filePath) != _fileExtension)
        {
            Console.WriteLine("Skipping file! Extension not handled inside CsReplacer!");
            return false;
        }
        return true;
    }
}