/*
 * Copyright 2021-present Alcon. All rights reserved.
 * Alcon proprietary/confidential.
 * Use is subject to license terms.
 */

using NUnit.Framework;
using System;
using System.IO;

namespace LocalizationLib.Tests;

[TestFixture]
public class CsReplacerTest
{
    private string _tempFolder;
    private string _projectFolder;
    private string _configFileContent = @"
  ""LastExtraction"": ""2025-04-07T11:20:38.6076263Z"",
  ""ExtractedTexts"": {
    ""CsTestFile.cs:9:31:23303582"": {
      ""FilePath"": ""CsTestFile.cs"",
      ""LineNumber"": 9,
      ""Column"": 31,
      ""OriginalText"": ""Back"",
      ""Property"": ""(string literal)"",
      ""GroupId"": ""Test.CsReplacer"",
      ""TextHash"": ""23303582"",
      ""Source"": 1,
      ""LastUpdated"": ""2025-04-07T11:20:38.6037644Z"",
      ""NeedsTranslation"": true,
      ""Status"": 1,
      ""LangTextID"": ""-216643263""
    },
    ""CsTestFile.cs:13:31:23810612"": {
      ""FilePath"": ""CsTestFile.cs"",
      ""LineNumber"": 13,
      ""Column"": 31,
      ""OriginalText"": ""Save"",
      ""Property"": ""(string literal)"",
      ""GroupId"": ""Test.CsReplacer"",
      ""TextHash"": ""23810612"",
      ""Source"": 1,
      ""LastUpdated"": ""2025-04-07T11:20:38.6040413Z"",
      ""NeedsTranslation"": true,
      ""Status"": 1,
      ""LangTextID"": ""-216136233""
    },
     ""CsTestFile.cs:16:31:1477736384"": {
      ""FilePath"": ""CsTestFile.cs"",
      ""LineNumber"": 16,
      ""Column"": 31,
      ""OriginalText"": ""Update"",
      ""Property"": ""(string literal)"",
      ""GroupId"": ""Test.CsReplacer"",
      ""TextHash"": ""1477736384"",
      ""Source"": 1,
      ""LastUpdated"": ""2025-04-07T11:20:38.6040533Z"",
      ""NeedsTranslation"": true,
      ""Status"": 0,
      ""LangTextID"": ""-1477914973""
    },
      ""CsTestFile.cs:21:31:981124258"": {
      ""FilePath"": ""CsTestFile.cs"",
      ""LineNumber"": 16,
      ""Column"": 31,
      ""OriginalText"": ""Delete"",
      ""Property"": ""(string literal)"",
      ""GroupId"": ""Test.CsReplacer"",
      ""TextHash"": ""981124258"",
      ""Source"": 1,
      ""LastUpdated"": ""2025-04-07T11:20:38.6040604Z"",
      ""NeedsTranslation"": true,
      ""Status"": 0,
      ""LangTextID"": ""-1974527099""
    },
      ""CsTestFile.cs:25:31:-1532914814"": {
      ""FilePath"": ""CsTestFile.cs"",
      ""LineNumber"": 16,
      ""Column"": 31,
      ""OriginalText"": ""New user"",
      ""Property"": ""(string literal)"",
      ""GroupId"": ""Test.CsReplacer"",
      ""TextHash"": ""-1532914814"",
      ""Source"": 1,
      ""LastUpdated"": ""2025-04-07T11:20:38.6040712Z"",
      ""NeedsTranslation"": true,
      ""Status"": 0,
      ""LangTextID"": ""1354481061""
    },
    ""CsTestFile.cs:29:31:2075061090"": {
      ""FilePath"": ""CsTestFile.cs"",
      ""LineNumber"": 29,
      ""Column"": 31,
      ""OriginalText"": ""It's a special case\n"",
      ""Property"": ""(string literal)"",
      ""GroupId"": ""Test.CsReplacer"",
      ""TextHash"": ""2075061090"",
      ""Source"": 1,
      ""LastUpdated"": ""2025-04-07T11:20:38.6040752Z"",
      ""NeedsTranslation"": true,
      ""Status"": 1,
      ""LangTextID"": ""-339478267""
    }

  }";

    private string _csFileContent = @"using System;
namespace Test.CsReplacer
{
    public class CsTestFile
    {
        public CsTestFile() { }
        public void GoBack()
        {
            Console.WriteLine(""Back"");
        }
        public void Save()
        {
            Console.WriteLine(""Save"");
        }
        public void Update()
        {
            Console.WriteLine(""Update"");
        }
        public void Delete()
        {
            Console.WriteLine(""Delete"");
        }
        public void NewUser()
        {
           Console.WriteLine(""New user"");
        }
        public void SpecialCase()
        {
           Console.WriteLine(""It's a special case\n"");
        }
    }
}
";

    [SetUp]
    public void SetUp()
    {
        // Reset the config manager before each test
        ProjectTextExtractorConfigManager.Reset();

        // Create a unique temporary folder for the tests.
        _tempFolder = Path.Combine(Path.GetTempPath(), "CsReplacerTests_" + Guid.NewGuid());
        Directory.CreateDirectory(_tempFolder);

        // Create subfolder for the "project"
        _projectFolder = Path.Combine(_tempFolder, "Project");
        Directory.CreateDirectory(_projectFolder);
    }

    [TearDown]
    public void TearDown()
    {
        if (Directory.Exists(_tempFolder))
        {
            Directory.Delete(_tempFolder, true);
        }
    }

    [Test]
    public void Run_Replace_ApprovedAndOpenStatus()
    {
        string csFile = Path.Combine(_projectFolder, "CsTestFile.cs");
        File.WriteAllText(csFile, _csFileContent);

        string configFile = Path.Combine(_projectFolder, "ProjectTextExtractorConfig.json");
        string configFileContent = $"{{\"ProjectPath\": \"{_projectFolder.Replace("\\", "\\\\")}\"," + _configFileContent + "}";
        File.WriteAllText(configFile, configFileContent);

        // run the ProjectLocalizationReplacer for cs files inside the projectFolder
        var aggregator = new ProjectLocalizationReplacer();
        aggregator.Run(_projectFolder, "cs");

        // assert "using Alcon.Interop.Translate;" has been added for file
        string updatedContent = File.ReadAllText(csFile);
        StringAssert.Contains(@"using Alcon.Interop.Translate;", updatedContent,
            "The library has been added for use inside the file.");

        // assert "Translator.Instance.TranslateM" has been added for the strings with Approved status inside the config file
        StringAssert.Contains("Translator.Instance.TranslateM(\"id:-216643263|en:Back\")", updatedContent,
            "The translate call has been added for \"Back\" string with id \"-216643263\"");
        StringAssert.Contains("Translator.Instance.TranslateM(\"id:-216136233|en:Save\")", updatedContent,
            "The translate call has been added for \"Save\" string with id \"-216136233\"");
        StringAssert.Contains("Translator.Instance.TranslateM(\"id:-339478267|en:It's a special case\\n\")", updatedContent,
        "The translate call has been added for \"It's a special case\n\" string with id \"-339478267\"");

        // assert "Translator.Instance.TranslateM" hasn't been added for the strings with Open status inside the config file
        StringAssert.DoesNotContain("Translator.Instance.TranslateM(\"id:-1477914973|en:Update\")", updatedContent,
            "The translate call hasn't been added for \"Update\" string with id \"-1477914973\"");
        StringAssert.DoesNotContain("Translator.Instance.TranslateM(\"id:-1974527099|en:Delete\")", updatedContent,
            "The translate call hasn't been added for \"Delete\" string with id \"-1974527099\"");
        StringAssert.DoesNotContain("Translator.Instance.TranslateM(\"id:1354481061|en:New user\")", updatedContent,
                    "The translate call hasn't been added for \"New user\" string with id \"1354481061\"");

        var config = ProjectTextExtractorConfigManager.Instance;

        // assert status has been changed to resolved inside config file
        Assert.IsTrue(config.GetExtractedTextStatus("CsTestFile.cs:9:31:23303582") == TextStatus.Resolved);
        Assert.IsTrue(config.GetExtractedTextStatus("CsTestFile.cs:13:31:23810612") == TextStatus.Resolved);
        Assert.IsTrue(config.GetExtractedTextStatus("CsTestFile.cs:29:31:2075061090") == TextStatus.Resolved);

        // assert status remains unchanged for the open ones
        Assert.IsTrue(config.GetExtractedTextStatus("CsTestFile.cs:16:31:1477736384") == TextStatus.Open);
        Assert.IsTrue(config.GetExtractedTextStatus("CsTestFile.cs:21:31:981124258") == TextStatus.Open);
        Assert.IsTrue(config.GetExtractedTextStatus("CsTestFile.cs:25:31:-1532914814") == TextStatus.Open);

        // assert updated lineNumber, updated column in config file
        Assert.IsTrue(config.GetExtractedText("CsTestFile.cs:9:31:23303582").UpdatedLineNumber == 10);
        Assert.IsTrue(config.GetExtractedText("CsTestFile.cs:9:31:23303582").UpdatedColumn == 80);
        Assert.IsTrue(config.GetExtractedText("CsTestFile.cs:13:31:23810612").UpdatedLineNumber == 14);
        Assert.IsTrue(config.GetExtractedText("CsTestFile.cs:13:31:23810612").UpdatedColumn == 80);
        Assert.IsTrue(config.GetExtractedText("CsTestFile.cs:29:31:2075061090").UpdatedLineNumber == 30);
        Assert.IsTrue(config.GetExtractedText("CsTestFile.cs:29:31:2075061090").UpdatedColumn == 79);
    }

    [Test]
    public void Run_Replace_SourceLineNotMatching()
    {
        string csFile = Path.Combine(_projectFolder, "CsTestFile.cs");
        File.WriteAllText(csFile, _csFileContent);

        string configFile = Path.Combine(_projectFolder, "ProjectTextExtractorConfig.json");
        string configFileContent = $"{{\"ProjectPath\": \"{_projectFolder.Replace("\\", "\\\\")}\"," +
@"""LastExtraction"": ""2025-04-07T11:20:38.6076263Z"",
  ""ExtractedTexts"": {
      ""CsTestFile.cs:17:31:-1532914814"": {
      ""FilePath"": ""CsTestFile.cs"",
      ""LineNumber"": 17,
      ""Column"": 31,
      ""OriginalText"": ""New user"",
      ""Property"": ""(string literal)"",
      ""GroupId"": ""Test.CsReplacer"",
      ""TextHash"": ""-1532914814"",
      ""Source"": 1,
      ""LastUpdated"": ""2025-04-07T11:20:38.6040712Z"",
      ""NeedsTranslation"": true,
      ""Status"": 1,
      ""LangTextID"": ""1354481061""
    }
  }
}";
        File.WriteAllText(configFile, configFileContent);

        // run the ProjectLocalizationReplacer for cs files inside the projectFolder
        var aggregator = new ProjectLocalizationReplacer();
        aggregator.Run(_projectFolder, "cs");

        // assert "using Alcon.Interop.Translate;" hasn't been added for file
        string updatedContent = File.ReadAllText(csFile);
        StringAssert.DoesNotContain(@"using Alcon.Interop.Translate;", updatedContent,
            "The library has NOT been added for use inside the file.");

        // assert "Translator.Instance.Translate" hasn't been added for string with line number not matching
        StringAssert.DoesNotContain("Translator.Instance.Translate(\"1354481061\", \"New user\")", updatedContent,
            "The translate call has NOT been added for \"New user\" string with id \"1354481061\"");

        var config = ProjectTextExtractorConfigManager.Instance;

        // assert status remained Approved
        Assert.IsTrue(config.GetExtractedTextStatus("CsTestFile.cs:17:31:-1532914814") == TextStatus.Approved);

        // assert notes has been updated
        Assert.IsTrue(config.GetExtractedText("CsTestFile.cs:17:31:-1532914814").Notes.Contains("Replacer: String \"New user\" wasn't found on line number \"17\"."));
    }

    [Test]
    public void Run_Replace_TranslateCallAlreadyOnLineWhileApprovedStatus()
    {
        string csFile = Path.Combine(_projectFolder, "CsTestFile.cs");
        string csFileContent = @"using System; using Alcon.Interop.Translate;
namespace Test.CsReplacer
{
    public class CsTestFile
    {
        public CsTestFile() { }
        public void GoBack()
        {
            Console.WriteLine(Translator.Instance.TranslateM(""id:-216643263|en:Back""));
        }
        public void Save()
        {
            Console.WriteLine(""Save"");
        }
        public void Update()
        {
            Console.WriteLine(""Update"");
        }
        public void Delete()
        {
            Console.WriteLine(""Delete"");
        }
        public void NewUser()
        {
           Console.WriteLine(""New user"");
        }
    }
}
";
        File.WriteAllText(csFile, csFileContent);

        string configFile = Path.Combine(_projectFolder, "ProjectTextExtractorConfig.json");
        string configFileContent = $"{{\"ProjectPath\": \"{_projectFolder.Replace("\\", "\\\\")}\"," + _configFileContent + "}";
        File.WriteAllText(configFile, configFileContent);

        // run the ProjectLocalizationReplacer for cs files inside the projectFolder
        var aggregator = new ProjectLocalizationReplacer();
        aggregator.Run(_projectFolder, "cs");

        string updatedContent = File.ReadAllText(csFile);
        StringAssert.Contains(@"using Alcon.Interop.Translate;", updatedContent,
            "The library has been added for use inside the file.");

        // assert "Translator.Instance.Translate" has been added once for the strings with Approved status inside the config file
        StringAssert.Contains("Translator.Instance.TranslateM(\"id:-216643263|en:Back\")", updatedContent,
            "The translate call has been added for \"Back\" string with id \"-216643263\"");
        StringAssert.Contains("Translator.Instance.TranslateM(\"id:-216136233|en:Save\")", updatedContent,
            "The translate call has been added for \"Save\" string with id \"-216136233\"");

        var config = ProjectTextExtractorConfigManager.Instance;

        // assert status changed to resolved
        Assert.IsTrue(config.GetExtractedText("CsTestFile.cs:9:31:23303582").Status == TextStatus.Resolved);
        Assert.IsTrue(config.GetExtractedText("CsTestFile.cs:13:31:23810612").Status == TextStatus.Resolved);

        // assert updateLineNumber, updatedColumn has been set only for those replaced after the call, for the replaced ones it hasn't been changed
        Assert.IsTrue(config.GetExtractedText("CsTestFile.cs:9:31:23303582").UpdatedLineNumber == 0);
        Assert.IsTrue(config.GetExtractedText("CsTestFile.cs:9:31:23303582").UpdatedColumn == 0);
        Assert.IsTrue(config.GetExtractedText("CsTestFile.cs:13:31:23810612").UpdatedLineNumber == 0);
        Assert.IsTrue(config.GetExtractedText("CsTestFile.cs:13:31:23810612").UpdatedColumn == 80);
    }

    [Test]
    public void Run_Replace_NeedsTranslationFalse()
    {
        string csFile = Path.Combine(_projectFolder, "CsTestFile.cs");
        File.WriteAllText(csFile, _csFileContent);

        string configFile = Path.Combine(_projectFolder, "ProjectTextExtractorConfig.json");
        string configFileContent = $"{{\"ProjectPath\": \"{_projectFolder.Replace("\\", "\\\\")}\"," +
@"""LastExtraction"": ""2025-04-07T11:20:38.6076263Z"",
  ""ExtractedTexts"": {
      ""CsTestFile.cs:25:31:-1532914814"": {
      ""FilePath"": ""CsTestFile.cs"",
      ""LineNumber"": 17,
      ""Column"": 31,
      ""OriginalText"": ""New user"",
      ""Property"": ""(string literal)"",
      ""GroupId"": ""Test.CsReplacer"",
      ""TextHash"": ""-1532914814"",
      ""Source"": 1,
      ""LastUpdated"": ""2025-04-07T11:20:38.6040712Z"",
      ""NeedsTranslation"": false,
      ""Status"": 1,
      ""LangTextID"": ""1354481061""
    }
  }
}";
        File.WriteAllText(configFile, configFileContent);

        // run the ProjectLocalizationReplacer for cs files inside the projectFolder
        var aggregator = new ProjectLocalizationReplacer();
        aggregator.Run(_projectFolder, "cs");

        string updatedContent = File.ReadAllText(csFile);

        // assert using Alcon.Interop.Translate hasn't been added
        StringAssert.DoesNotContain(@"using Alcon.Interop.Translate;", updatedContent,
            "The library hasn't been added for use inside the file.");

        // assert "Translator.Instance.Translate" hasn't been added for the string with need translation on false inside the config file
        StringAssert.DoesNotContain("Translator.Instance.TranslateM(\"id:-1354481061|en:New user\")", updatedContent,
            "The translate call hasn't been added for \"New user\" string with id \"1354481061\"");
    }

    [Test]
    public void Run_Replace_TextAppearingTwiceOnTheSameLine()
    {
        string csFile = Path.Combine(_projectFolder, "CsTestFile.cs");
        string csFileContent = @"using System;
namespace Test.CsReplacer
{
    public class CsTestFile
    {
        public CsTestFile() { }
        public void GoBack()
        {
            Console.WriteLine(""Back"" + ""Back""));
        }
    }
}
";
        File.WriteAllText(csFile, csFileContent);

        string configFile = Path.Combine(_projectFolder, "ProjectTextExtractorConfig.json");
        string configFileContent = $"{{\"ProjectPath\": \"{_projectFolder.Replace("\\", "\\\\")}\"," +
@"""LastExtraction"": ""2025-04-07T11:20:38.6076263Z"",
 ""ExtractedTexts"": {
 ""CsTestFile.cs:9:31:23303582"": {
     ""FilePath"": ""CsTestFile.cs"",
     ""LineNumber"": 9,
     ""Column"": 31,
     ""OriginalText"": ""Back"",
     ""Property"": ""(string literal)"",
     ""GroupId"": ""Test.CsReplacer"",
     ""TextHash"": ""23303582"",
     ""Source"": 1,
     ""LastUpdated"": ""2025-04-07T11:20:38.6037644Z"",
     ""NeedsTranslation"": true,
     ""Status"": 1,
     ""LangTextID"": ""-216643263""
 },
 ""CsTestFile.cs:9:41:23303582"": {
     ""FilePath"": ""CsTestFile.cs"",
     ""LineNumber"": 9,
     ""Column"": 41,
     ""OriginalText"": ""Back"",
     ""Property"": ""(string literal)"",
     ""GroupId"": ""Test.CsReplacer"",
     ""TextHash"": ""23303582"",
     ""Source"": 1,
     ""LastUpdated"": ""2025-04-07T11:20:38.6037644Z"",
     ""NeedsTranslation"": true,
     ""Status"": 1,
     ""LangTextID"": ""-216643263""
 }
}
}";
        File.WriteAllText(configFile, configFileContent);

        // run the ProjectLocalizationReplacer for cs files inside the projectFolder
        var aggregator = new ProjectLocalizationReplacer();
        aggregator.Run(_projectFolder, "cs");

        string updatedContent = File.ReadAllText(csFile);

        // assert "Translator.Instance.Translate" has been added for the strings with Approved status inside the config file
        StringAssert.Contains("Translator.Instance.TranslateM(\"id:-216643263|en:Back\") + Translator.Instance.TranslateM(\"id:-216643263|en:Back\")", updatedContent,
            "The translate call has been added for \"Back\" string with id \"-216643263\"");

        var config = ProjectTextExtractorConfigManager.Instance;

        // assert updated column/linenumber
        Assert.IsTrue(config.GetExtractedText("CsTestFile.cs:9:31:23303582").UpdatedColumn == 80);
        Assert.IsTrue(config.GetExtractedText("CsTestFile.cs:9:31:23303582").UpdatedLineNumber == 10);

        Assert.IsTrue(config.GetExtractedText("CsTestFile.cs:9:41:23303582").UpdatedColumn == 138);
        Assert.IsTrue(config.GetExtractedText("CsTestFile.cs:9:41:23303582").UpdatedLineNumber == 10);

        // assert status changed to resolved
        Assert.IsTrue(config.GetExtractedTextStatus("CsTestFile.cs:9:31:23303582") == TextStatus.Resolved);
        Assert.IsTrue(config.GetExtractedTextStatus("CsTestFile.cs:9:41:23303582") == TextStatus.Resolved);
    }

    [Test]
    public void Run_Replace_HandleDescriptionAttribute()
    {
        string csFile = Path.Combine(_projectFolder, "CsTestFile.cs");
        string csFileContent = @"using System;
namespace Test.CsReplacer
{
    public enum CsTestEnum
    {
        [Description(""Back"")]
        Back,
        [Description(""Update"")]
        Update
    }
}
";
        File.WriteAllText(csFile, csFileContent);

        string configFile = Path.Combine(_projectFolder, "ProjectTextExtractorConfig.json");
        string configFileContent = $"{{\"ProjectPath\": \"{_projectFolder.Replace("\\", "\\\\")}\"," +
@"""LastExtraction"": ""2025-04-07T11:20:38.6076263Z"",
 ""ExtractedTexts"": {
 ""CsTestFile.cs:6:23:23303582"": {
     ""FilePath"": ""CsTestFile.cs"",
     ""LineNumber"": 6,
     ""Column"": 23,
     ""OriginalText"": ""Back"",
     ""Property"": ""(string literal)"",
     ""GroupId"": ""Test.CsReplacer"",
     ""TextHash"": ""23303582"",
     ""Source"": 1,
     ""LastUpdated"": ""2025-04-07T11:20:38.6037644Z"",
     ""NeedsTranslation"": true,
     ""Status"": 1,
     ""LangTextID"": ""-216643263""
 },
 ""CsTestFile.cs:8:23:1477736384"": {
     ""FilePath"": ""CsTestFile.cs"",
     ""LineNumber"": 8,
     ""Column"": 23,
     ""OriginalText"": ""Update"",
     ""Property"": ""(string literal)"",
     ""GroupId"": ""Test.CsReplacer"",
     ""TextHash"": ""1477736384"",
     ""Source"": 1,
     ""LastUpdated"": ""2025-04-07T11:20:38.6037644Z"",
     ""NeedsTranslation"": true,
     ""Status"": 1,
     ""LangTextID"": ""-1477914973""
 }
}
}";

        File.WriteAllText(configFile, configFileContent);

        // run the ProjectLocalizationReplacer for cs files inside the projectFolder
        var aggregator = new ProjectLocalizationReplacer();
        aggregator.Run(_projectFolder, "cs");

        // assert "using Alcon.Interop.Translate;" has been added for file
        string updatedContent = File.ReadAllText(csFile);
        StringAssert.Contains(@"using Alcon.Interop.Translate;", updatedContent,
            "The library has been added for use inside the file.");

        // assert "Translator.Instance.TranslateM" has been added for the strings with Approved status inside the config file
        StringAssert.Contains("[LocalizedDescription(\"-216643263\",\"Back\")]", updatedContent,
            "The LocalizedDescriptor has been added for \"Back\" string with id \"-216643263\"");
        StringAssert.Contains("[LocalizedDescription(\"-1477914973\",\"Update\")]", updatedContent,
            "The LocalizedDescriptor has been added for \"Update\" string with id \"-1477914973\"");

        var config = ProjectTextExtractorConfigManager.Instance;

        // assert status has been changed to resolved inside config file
        Assert.IsTrue(config.GetExtractedTextStatus("CsTestFile.cs:6:23:23303582") == TextStatus.Resolved);
        Assert.IsTrue(config.GetExtractedTextStatus("CsTestFile.cs:8:23:1477736384") == TextStatus.Resolved);

        // assert updated lineNumber, updated column in config file
        Assert.IsTrue(config.GetExtractedText("CsTestFile.cs:6:23:23303582").UpdatedLineNumber == 7);
        Assert.IsTrue(config.GetExtractedText("CsTestFile.cs:6:23:23303582").UpdatedColumn == 45);
        Assert.IsTrue(config.GetExtractedText("CsTestFile.cs:8:23:1477736384").UpdatedLineNumber == 9);
        Assert.IsTrue(config.GetExtractedText("CsTestFile.cs:8:23:1477736384").UpdatedColumn == 46);
    }
}