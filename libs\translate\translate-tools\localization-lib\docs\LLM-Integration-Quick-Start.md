# LLM Integration Quick Start Guide

## Overview

The localization tools support two LLM integration options for intelligent text analysis:

| Feature | LM Studio (Local) | Amazon Bedrock (Cloud) |
|---------|-------------------|-------------------------|
| **Privacy** | ✅ Complete (offline) | ❌ Cloud-based |
| **Cost** | ✅ Free after setup | 💰 Pay-per-token |
| **Setup** | ⚠️ Medium complexity | ✅ Simple (with AWS account) |
| **Quality** | ⚠️ Good | ✅ Excellent |
| **Speed** | ✅ Very fast (local) | ⚠️ Network dependent |
| **Maintenance** | ⚠️ Self-managed | ✅ Fully managed |

## Quick Setup

### Option 1: LM Studio (Local AI)

**Best for**: Privacy-focused environments, cost-sensitive projects, offline usage

1. **Install LM Studio**: Download from [https://lmstudio.ai/](https://lmstudio.ai/)
2. **Download a model**: Qwen2.5-Coder-32B-Instruct ⭐ **Best for code analysis**
3. **Start server**: Load model and start server on port 1234
4. **Configure**: Copy `etc/llmconfig-lmstudio-example.json` to `etc/llmconfig.json`

```json
{
    "llmProvider": "LocalLlmProvider",
    "localLlmProviderConfig": {
        "baseUrl": "http://localhost:1234",
        "modelName": "Qwen2.5-Coder-32B-Instruct"
    }
}
```

**📖 Full Guide**: [LM Studio Integration Guide](LM-Studio-Integration.md)

### Option 2: Amazon Bedrock (Cloud AI)

**Best for**: Enterprise environments, high-quality analysis, managed infrastructure

1. **AWS Setup**: Create AWS account and request Bedrock model access
2. **IAM Permissions**: Add `AmazonBedrockFullAccess` policy
3. **Configure**: Copy `etc/llmconfig-bedrock-example.json` to `etc/llmconfig.json`
4. **Add credentials**: Set AWS access keys in configuration

```json
{
    "llmProvider": "BedrockProvider",
    "bedrockProviderConfig": {
        "region": "us-east-1",
        "modelId": "anthropic.claude-3-sonnet-********-v1:0",
        "accessKey": "YOUR_AWS_ACCESS_KEY",
        "secretKey": "YOUR_AWS_SECRET_KEY"
    }
}
```

**📖 Full Guide**: [Amazon Bedrock Integration Guide](Amazon-Bedrock-Integration.md)

## Configuration Examples

### Development (Fast Analysis)
```json
{
    "llmProvider": "LocalLlmProvider",
    "localLlmProviderConfig": {
        "baseUrl": "http://localhost:1234",
        "temperature": 0.0,
        "maxTokens": 2048,
        "timeoutSeconds": 30
    }
}
```

### Production (High Quality)
```json
{
    "llmProvider": "BedrockProvider",
    "bedrockProviderConfig": {
        "region": "us-east-1",
        "modelId": "anthropic.claude-3-sonnet-********-v1:0",
        "temperature": 0.0,
        "maxRetries": 5,
        "timeoutSeconds": 180
    }
}
```

## Usage

Once configured, run the localization extractor with LLM verification:

```bash
localization_extractor.exe --projectPath MyProject --projectTypes cs --csProcessor llm
```

The LLM will intelligently analyze each string and determine:
- ✅ **Needs Translation**: User-facing strings (UI labels, messages, tooltips)
- ❌ **Skip**: Technical strings (logs, config keys, file paths)

Results are automatically cached in `.llmcache.json` for fast reuse. See [LLM Cache System](LLM-Cache-System.md) for details.

## Troubleshooting

### LM Studio Issues
- **Connection refused**: Ensure LM Studio server is running
- **Slow responses**: Try smaller model (CodeLlama-7B vs 13B)
- **Out of memory**: Close other applications or use smaller model

### Bedrock Issues
- **403 Forbidden**: Check IAM permissions and model access
- **429 Rate limit**: Automatic retry with exponential backoff
- **Invalid model**: Verify model ID and region availability

## Next Steps

1. **Choose your LLM option** based on requirements
2. **Follow the detailed setup guide** for your chosen option
3. **Test with a small project** first
4. **Review LLM decisions** and adjust prompts if needed
5. **Learn about cache management** - see [LLM Cache System](LLM-Cache-System.md)
5. **Scale to full project** once satisfied with results

## Support

- **LM Studio**: [LM Studio Integration Guide](LM-Studio-Integration.md)
- **Amazon Bedrock**: [Amazon Bedrock Integration Guide](Amazon-Bedrock-Integration.md)
- **General Issues**: Check tool-specific README files
