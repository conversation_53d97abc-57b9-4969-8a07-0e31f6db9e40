using System.ComponentModel;

namespace Alcon.Interop.Translate;

/// <summary>
/// Custom DescriptionAttribute that uses the Translator API to get localized text.
/// </summary>
public class LocalizedDescriptionAttribute : DescriptionAttribute
{
    private readonly string _textId;
    private readonly string _englishText;
    private bool _isLocalized = false;

    /// <summary>
    /// Initializes a new instance of the <see cref="LocalizedDescriptionAttribute"/> class.
    /// </summary>
    /// <param name="textId">The unique identifier used for translation lookup.</param>
    /// <param name="englishText">The default English text description.</param>
    public LocalizedDescriptionAttribute(string textId, string englishText)
        : base(englishText)
    {
        _textId = textId;
        _englishText = englishText;
        
        // Subscribe to language changes
        Translator.Instance.PropertyChanged += OnTranslatorPropertyChanged;
    }

    private void OnTranslatorPropertyChanged(object? sender, PropertyChangedEventArgs e)
    {
        if (e.PropertyName == nameof(Translator.CurrentUserLanguage))
        {
            // Reset the localized flag when language changes
            _isLocalized = false;
        }
    }

    /// <summary>
    /// Gets the localized description.
    /// </summary>
    public override string Description
    {
        get
        {
            if (!_isLocalized)
            {
                // Use the Translator API to fetch the localized text.
                // Translator.Instance.Translate takes the text ID and the default English text.
                string translated = Translator.Instance.Translate(_textId, _englishText);

                // If translation fails or returns null/empty,
                // fallback to the englishText.
                if (string.IsNullOrWhiteSpace(translated))
                {
                    translated = _englishText;
                }

                // Cache the translated value
                base.DescriptionValue = translated;
                _isLocalized = true;
            }
            return base.DescriptionValue;
        }
    }
}

