D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\CMakeFiles\4.0.0-rc4\CompilerIdCSharp\obj\x64\Debug\CompilerIdCSharp.csproj.AssemblyReference.cache
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\CMakeFiles\4.0.0-rc4\CompilerIdCSharp\obj\x64\Debug\CompilerIdCSharp.csproj.CoreCompileInputs.cache
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\CMakeFiles\4.0.0-rc4\CompilerIdCSharp\obj\x64\Debug\CompilerIdCSharp.exe
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\CMakeFiles\4.0.0-rc4\CompilerIdCSharp\obj\x64\Debug\CompilerIdCSharp.pdb
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\CMakeFiles\4.0.0-rc4\CompilerIdCSharp\CompilerIdCSharp.exe
D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\tests\unit\build\CMakeFiles\4.0.0-rc4\CompilerIdCSharp\CompilerIdCSharp.pdb
