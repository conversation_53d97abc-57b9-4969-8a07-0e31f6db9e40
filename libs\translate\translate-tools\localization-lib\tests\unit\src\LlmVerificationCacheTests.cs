using NUnit.Framework;
using System;
using System.IO;
using LocalizationLib;
using LocalizationLib.Llm;

namespace LocalizationLib.Tests
{
    [TestFixture]
    public class LlmVerificationCacheTests
    {
        private string _testProjectPath;
        private LlmVerificationCache _cache;
        private LlmServerSpec _testLlmSpec;

        [SetUp]
        public void SetUp()
        {
            _testProjectPath = Path.Combine(Path.GetTempPath(), $"LlmCacheTest_{Guid.NewGuid()}");
            Directory.CreateDirectory(_testProjectPath);
            
            _cache = new LlmVerificationCache(_testProjectPath);
            
            _testLlmSpec = new LlmServerSpec
            {
                Provider = "local",
                Model = "qwen2.5-coder-32b",
                Version = "v1.0",
                Endpoint = "http://localhost:1234"
            };
        }

        [TearDown]
        public void TearDown()
        {
            if (Directory.Exists(_testProjectPath))
            {
                Directory.Delete(_testProjectPath, true);
            }
        }

        [Test]
        public void AddVerificationResult_ShouldStoreResult()
        {
            // Arrange
            string text = "Hello World";
            string filePath = "src/test.cs";
            int lineNumber = 10;
            int columnNumber = 26;
            bool needsTranslation = true;
            string reason = "User-facing message";
            string context = "String literal in method";

            // Act
            string lineContent = "Console.WriteLine(\"Hello World\");";
            _cache.AddVerificationResult(text, filePath, lineNumber, columnNumber,
                needsTranslation, reason, context, ExtractionSource.CsLlm, _testLlmSpec, lineContent);

            // Assert
            var result = _cache.GetVerificationResult(text, filePath, lineNumber, columnNumber, _testLlmSpec);
            Assert.IsNotNull(result);
            Assert.AreEqual(text, result.Text);
            Assert.AreEqual(needsTranslation, result.NeedsTranslation);
            Assert.AreEqual(reason, result.Reason);
            Assert.AreEqual(context, result.Context);
            Assert.AreEqual(ExtractionSource.CsLlm, result.Source);
            Assert.AreEqual(lineContent, result.LineContent);
            Assert.AreEqual(_testLlmSpec.Provider, result.LlmServerSpec.Provider);
            Assert.AreEqual(_testLlmSpec.Model, result.LlmServerSpec.Model);
        }

        [Test]
        public void GetVerificationResult_WithDifferentLlmSpec_ShouldReturnNull()
        {
            // Arrange
            string text = "Test String";
            string filePath = "src/test.cs";
            int lineNumber = 5;
            int columnNumber = 15;

            _cache.AddVerificationResult(text, filePath, lineNumber, columnNumber,
                false, "Switch case value", "Switch statement", ExtractionSource.CsLlm, _testLlmSpec);

            var differentLlmSpec = new LlmServerSpec
            {
                Provider = "bedrock",
                Model = "claude-3-sonnet",
                Version = "v2.0"
            };

            // Act
            var result = _cache.GetVerificationResult(text, filePath, lineNumber, columnNumber, differentLlmSpec);

            // Assert
            Assert.IsNull(result, "Should not find result with different LLM spec");
        }

        [Test]
        public void AddVerificationResult_ShouldUseRelativePath()
        {
            // Arrange
            string absolutePath = Path.Combine(_testProjectPath, "src", "models", "User.cs");
            string text = "User Name";
            int lineNumber = 15;
            int columnNumber = 45;

            // Act
            _cache.AddVerificationResult(text, absolutePath, lineNumber, columnNumber,
                true, "Property default value", "Class property", ExtractionSource.CsLlm, _testLlmSpec);

            // Assert
            var result = _cache.GetVerificationResult(text, absolutePath, lineNumber, columnNumber, _testLlmSpec);
            Assert.IsNotNull(result);
            Assert.AreEqual("src\\models\\User.cs", result.RelativeFilePath.Replace("/", "\\"));
        }

        [Test]
        public void GetStatistics_ShouldReturnCorrectCounts()
        {
            // Arrange
            _cache.AddVerificationResult("Button Text", "ui/button.cs", 1, 15,
                true, "UI text", "Button content", ExtractionSource.CsLlm, _testLlmSpec);

            _cache.AddVerificationResult("LOG_DEBUG", "utils/logger.cs", 5, 8,
                false, "Log level constant", "Switch case", ExtractionSource.CsLlm, _testLlmSpec);

            var bedrockSpec = new LlmServerSpec { Provider = "bedrock", Model = "claude-3", Version = "v1" };
            _cache.AddVerificationResult("Error Message", "errors/handler.cs", 10, 25,
                true, "Exception message", "Error handling", ExtractionSource.CsLlm, bedrockSpec);

            // Act
            var stats = _cache.GetStatistics();

            // Assert
            Assert.AreEqual(3, stats.TotalEntries);
            Assert.AreEqual(2, stats.EntriesNeedingTranslation);
            Assert.AreEqual(1, stats.EntriesSkippingTranslation);
            Assert.AreEqual(2, stats.EntriesByProvider["local"]);
            Assert.AreEqual(1, stats.EntriesByProvider["bedrock"]);
            Assert.AreEqual(2, stats.EntriesByModel["qwen2.5-coder-32b"]);
            Assert.AreEqual(1, stats.EntriesByModel["claude-3"]);
        }

        [Test]
        public void CleanExpiredEntries_ShouldRemoveOldEntries()
        {
            // Arrange
            _cache.AddVerificationResult("Old Text", "old.cs", 1, 10,
                true, "old reason", "old context", ExtractionSource.CsLlm, _testLlmSpec);

            // Manually set timestamp to old date (simulate old entry)
            var stats = _cache.GetStatistics();
            Assert.AreEqual(1, stats.TotalEntries);

            // Act
            _cache.CleanExpiredEntries(0); // Remove entries older than 0 days (all entries)

            // Assert
            stats = _cache.GetStatistics();
            Assert.AreEqual(0, stats.TotalEntries);
        }

        [Test]
        public void CacheKey_ShouldBeConsistent()
        {
            // Arrange
            string text = "Consistent Text";
            string filePath = "src/test.cs";
            int lineNumber = 20;
            int columnNumber = 15;

            // Act - Add same result twice
            _cache.AddVerificationResult(text, filePath, lineNumber, columnNumber,
                true, "First reason", "First context", ExtractionSource.CsLlm, _testLlmSpec);

            _cache.AddVerificationResult(text, filePath, lineNumber, columnNumber,
                false, "Second reason", "Second context", ExtractionSource.CsLlm, _testLlmSpec);

            // Assert - Should only have one entry (second overwrites first)
            var stats = _cache.GetStatistics();
            Assert.AreEqual(1, stats.TotalEntries);

            var result = _cache.GetVerificationResult(text, filePath, lineNumber, columnNumber, _testLlmSpec);
            Assert.AreEqual("Second reason", result.Reason);
            Assert.AreEqual(false, result.NeedsTranslation);
        }

        [Test]
        public void LlmServerSpec_ToString_ShouldFormatCorrectly()
        {
            // Arrange
            var spec = new LlmServerSpec
            {
                Provider = "bedrock",
                Model = "claude-3-sonnet",
                Version = "2024-02-29"
            };

            // Act
            string result = spec.ToString();

            // Assert
            Assert.AreEqual("bedrock:claude-3-sonnet:2024-02-29", result);
        }

        [Test]
        public void Cache_ShouldPersistBetweenInstances()
        {
            // Arrange
            string text = "Persistent Text";
            string filePath = "persistent.cs";
            int lineNumber = 1;
            int columnNumber = 5;

            _cache.AddVerificationResult(text, filePath, lineNumber, columnNumber,
                true, "Persistent reason", "Persistent context", ExtractionSource.CsLlm, _testLlmSpec);

            // Act - Create new cache instance with same project path
            var newCache = new LlmVerificationCache(_testProjectPath);
            var result = newCache.GetVerificationResult(text, filePath, lineNumber, columnNumber, _testLlmSpec);

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual(text, result.Text);
            Assert.AreEqual("Persistent reason", result.Reason);
        }
    }
}
