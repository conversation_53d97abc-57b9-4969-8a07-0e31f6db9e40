﻿
<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup Label="Globals">
    <ProjectGuid>{BB6BC777-A517-3A1B-B98D-2F6C0F78586E}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <Configurations>Debug;Release</Configurations>
    <EnableDefaultItems>false</EnableDefaultItems>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
    <ManagedAssembly>true</ManagedAssembly>
    <TargetFramework>net8.0</TargetFramework>
    <AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
    <OutputType>Library</OutputType>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)' == 'Debug'">
    <OutputPath>D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\localizationlib\Debug\</OutputPath>
    <AssemblyName>localizationlib</AssemblyName>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    <DebugType>full</DebugType>
    <DefineConstants>TRACE;DEBUG;_DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <LangVersion>latest</LangVersion>
    <Optimize>false</Optimize>
    <Platform>x64</Platform>
    <WarningLevel>3</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)' == 'Release'">
    <OutputPath>D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\localizationlib\Release\</OutputPath>
    <AssemblyName>localizationlib</AssemblyName>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    <DebugType>none</DebugType>
    <DefineConstants>TRACE;_DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR</DefineConstants>
    <ErrorReport>queue</ErrorReport>
    <LangVersion>latest</LangVersion>
    <Optimize>true</Optimize>
    <Platform>x64</Platform>
    <WarningLevel>1</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <None Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\CMakeLists.txt">
      <Link>CMakeLists.txt</Link>
    </None>
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="D:\kibisoft\workarea\git\interop\cmake\Globals.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\CMakeFiles\cmake.verify_globs" />
  </ItemGroup>
  <Target Condition="'$(Configuration)' == 'Debug'"
    Name="CustomCommand_Debug_d3571293915ca1cc1e16bd33cdb78d61"
    Inputs="D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-lib/CMakeLists.txt;D:\kibisoft\workarea\git\interop\cmake\Globals.cmake;D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\CMakeFiles\cmake.verify_globs"
    Outputs="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\localizationlib\CMakeFiles\generate.stamp"
    BeforeTargets="CoreCompile"
    DependsOnTargets="PrepareForBuild">
    <Exec Command="echo Building Custom Rule D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-lib/CMakeLists.txt" />
    <Exec Command="setlocal&#10;&quot;C:\Program Files\CMake\bin\cmake.exe&quot; -SD:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-lib/tests/unit -BD:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-lib/tests/unit/build --check-stamp-file D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-lib/tests/unit/build/localizationlib/CMakeFiles/generate.stamp&#10;if %errorlevel% neq 0 goto :cmEnd&#10;:cmEnd&#10;endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone&#10;:cmErrorLevel&#10;exit /b %1&#10;:cmDone&#10;if %errorlevel% neq 0 goto :VCEnd&#10;:VCEnd" />
  </Target>
  <ItemGroup>
    <UpToDateCheckInput Include="D:\kibisoft\workarea\git\interop\cmake\Globals.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\CMakeFiles\cmake.verify_globs" />
  </ItemGroup>
  <Target Condition="'$(Configuration)' == 'Release'"
    Name="CustomCommand_Release_d3571293915ca1cc1e16bd33cdb78d61"
    Inputs="D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-lib/CMakeLists.txt;D:\kibisoft\workarea\git\interop\cmake\Globals.cmake;D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\CMakeFiles\cmake.verify_globs"
    Outputs="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\localizationlib\CMakeFiles\generate.stamp"
    BeforeTargets="CoreCompile"
    DependsOnTargets="PrepareForBuild">
    <Exec Command="echo Building Custom Rule D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-lib/CMakeLists.txt" />
    <Exec Command="setlocal&#10;&quot;C:\Program Files\CMake\bin\cmake.exe&quot; -SD:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-lib/tests/unit -BD:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-lib/tests/unit/build --check-stamp-file D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-lib/tests/unit/build/localizationlib/CMakeFiles/generate.stamp&#10;if %errorlevel% neq 0 goto :cmEnd&#10;:cmEnd&#10;endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone&#10;:cmErrorLevel&#10;exit /b %1&#10;:cmDone&#10;if %errorlevel% neq 0 goto :VCEnd&#10;:VCEnd" />
  </Target>
  <ItemGroup>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\src\IExtractor.cs">
      <Link>src\IExtractor.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\src\ProjectLocalizationExtractor.cs">
      <Link>src\ProjectLocalizationExtractor.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\src\ProjectLocalizationReplacer.cs">
      <Link>src\ProjectLocalizationReplacer.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\src\ReportGenerator.cs">
      <Link>src\ReportGenerator.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\src\code-restructure\StringConcatenationProcessor.cs">
      <Link>src\code-restructure\StringConcatenationProcessor.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\src\code-restructure\StringInterpolationProcessor.cs">
      <Link>src\code-restructure\StringInterpolationProcessor.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\src\code-restructure\XamlTextToCodeBehindProcessor.cs">
      <Link>src\code-restructure\XamlTextToCodeBehindProcessor.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\src\common\ProjectTextExtractorConfig.cs">
      <Link>src\common\ProjectTextExtractorConfig.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\src\common\ProjectTextExtractorConfigManager.cs">
      <Link>src\common\ProjectTextExtractorConfigManager.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\src\merge\FileMerge.cs">
      <Link>src\merge\FileMerge.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\src\merge\XMLElementComparer.cs">
      <Link>src\merge\XMLElementComparer.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\src\processer\cpp\ConsolidatedCppProcessor.cs">
      <Link>src\processer\cpp\ConsolidatedCppProcessor.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\src\processer\cpp\CppExtractor.cs">
      <Link>src\processer\cpp\CppExtractor.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\src\processer\cpp\ICppProcessor.cs">
      <Link>src\processer\cpp\ICppProcessor.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\src\processer\cpp\RegexCppProcessor.cs">
      <Link>src\processer\cpp\RegexCppProcessor.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\src\processer\cs\ConsolidatedCsProcessor.cs">
      <Link>src\processer\cs\ConsolidatedCsProcessor.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\src\processer\cs\CsExtractor.cs">
      <Link>src\processer\cs\CsExtractor.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\src\processer\cs\CsProcessorHelpers.cs">
      <Link>src\processer\cs\CsProcessorHelpers.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\src\processer\cs\ICsProcessor.cs">
      <Link>src\processer\cs\ICsProcessor.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\src\processer\cs\llm\BaseLlmClient.cs">
      <Link>src\processer\cs\llm\BaseLlmClient.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\src\processer\cs\llm\BedrockLlmClient.cs">
      <Link>src\processer\cs\llm\BedrockLlmClient.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\src\processer\cs\llm\ILlmClient.cs">
      <Link>src\processer\cs\llm\ILlmClient.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\src\processer\cs\llm\LLMConfigLoader.cs">
      <Link>src\processer\cs\llm\LLMConfigLoader.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\src\processer\cs\llm\LLMCsProcessor.cs">
      <Link>src\processer\cs\llm\LLMCsProcessor.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\src\processer\cs\llm\LlmCacheManager.cs">
      <Link>src\processer\cs\llm\LlmCacheManager.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\src\processer\cs\llm\LlmClientFactory.cs">
      <Link>src\processer\cs\llm\LlmClientFactory.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\src\processer\cs\llm\LlmModels.cs">
      <Link>src\processer\cs\llm\LlmModels.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\src\processer\cs\llm\LlmPrompts.cs">
      <Link>src\processer\cs\llm\LlmPrompts.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\src\processer\cs\llm\LlmVerificationCache.cs">
      <Link>src\processer\cs\llm\LlmVerificationCache.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\src\processer\cs\llm\LocalLlmClient.cs">
      <Link>src\processer\cs\llm\LocalLlmClient.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\src\processer\cs\regex\RegexCsProcessor.cs">
      <Link>src\processer\cs\regex\RegexCsProcessor.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\src\processer\cs\roslyn\ILiteralFilter.cs">
      <Link>src\processer\cs\roslyn\ILiteralFilter.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\src\processer\cs\roslyn\RoslynCsProcessor.cs">
      <Link>src\processer\cs\roslyn\RoslynCsProcessor.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\src\processer\cs\roslyn\RoslynStringLiteralFilter.cs">
      <Link>src\processer\cs\roslyn\RoslynStringLiteralFilter.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\src\processer\xaml\XamlExtractor.cs">
      <Link>src\processer\xaml\XamlExtractor.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\src\processer\xaml\XamlProcessor.cs">
      <Link>src\processer\xaml\XamlProcessor.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\src\replacer\CsReplacer.cs">
      <Link>src\replacer\CsReplacer.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\src\replacer\FileOffsetManager.cs">
      <Link>src\replacer\FileOffsetManager.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\src\replacer\IReplacer.cs">
      <Link>src\replacer\IReplacer.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\src\replacer\XamlReplacer.cs">
      <Link>src\replacer\XamlReplacer.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\src\utils\LLMConfigPathResolver.cs">
      <Link>src\utils\LLMConfigPathResolver.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\src\utils\LocalizationUtils.cs">
      <Link>src\utils\LocalizationUtils.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\src\utils\PathUtils.cs">
      <Link>src\utils\PathUtils.cs</Link>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Microsoft.CodeAnalysis.CSharp.Scripting" Version="4.10.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="CommunityToolkit.Mvvm" Version="8.2.2" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\ZERO_CHECK.vcxproj">
      <Project>{4CCA6B49-B9A4-3195-B68D-768F5CC142CD}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
  </ItemGroup>
</Project>