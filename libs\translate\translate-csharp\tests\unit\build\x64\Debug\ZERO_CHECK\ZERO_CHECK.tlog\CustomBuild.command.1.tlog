^D:\KIBISOFT\WORKAREA\GIT\INTEROP\LIBS\TRANSLATE\TRANSLATE-CSHARP\TESTS\UNIT\BUILD\CMAKEFILES\5481A18745029BE9CDC246F605EBDD0F\GENERATE.STAMP.RULE
setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SD:/kibisoft/workarea/git/interop/libs/translate/translate-csharp/tests/unit -BD:/kibisoft/workarea/git/interop/libs/translate/translate-csharp/tests/unit/build --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file D:/kibisoft/workarea/git/interop/libs/translate/translate-csharp/tests/unit/build/translate_unit_tests.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
