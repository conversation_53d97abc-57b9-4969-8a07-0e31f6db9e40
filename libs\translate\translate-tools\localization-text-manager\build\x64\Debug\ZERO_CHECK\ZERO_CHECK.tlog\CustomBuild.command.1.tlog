^D:\KIBISOFT\WORKAREA\GIT\INTEROP\LIBS\TRANSLATE\TRANSLATE-TOOLS\LOCALIZATION-TEXT-MANAGER\BUILD\CMAKEFILES\9D7AE70F1B0822E0659F38F6FE3001EA\GENERATE.STAMP.RULE
setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SD:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-text-manager -BD:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-text-manager/build --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-text-manager/build/localization_text_manager.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
