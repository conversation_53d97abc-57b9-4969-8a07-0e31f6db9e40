/*
 * Copyright 2021-present Alcon. All rights reserved.
 * Alcon proprietary/confidential.
 * Use is subject to license terms.
 */

using System;
using LocalizationLib.Llm;

namespace LocalizationLib.Llm
{
    /// <summary>
    /// Manages LLM verification cache independently from project configuration.
    /// Provides a clean interface for LLM cache operations without coupling to config management.
    /// </summary>
    public class LlmCacheManager
    {
        private readonly LlmVerificationCache _cache;
        private readonly LlmServerSpecFactory _serverSpecFactory;

        public LlmCacheManager(string projectPath)
        {
            _cache = new LlmVerificationCache(projectPath);
            _serverSpecFactory = new LlmServerSpecFactory();
        }

        /// <summary>
        /// Gets verification result from cache
        /// </summary>
        public CachedVerificationResult GetVerificationResult(string text, string filePath, int lineNumber,
            int columnNumber, ILlmClient llmClient, LLMConfig llmConfig)
        {
            var llmSpec = _serverSpecFactory.CreateFromClient(llmClient, llmConfig);
            return _cache.GetVerificationResult(text, filePath, lineNumber, columnNumber, llmSpec);
        }

        /// <summary>
        /// Adds verification result to cache
        /// </summary>
        public void AddVerificationResult(string text, string filePath, int lineNumber, int columnNumber,
            bool needsTranslation, string reason, string context, ExtractionSource source,
            ILlmClient llmClient, LLMConfig llmConfig, string lineContent = null)
        {
            var llmSpec = _serverSpecFactory.CreateFromClient(llmClient, llmConfig);
            _cache.AddVerificationResult(text, filePath, lineNumber, columnNumber, needsTranslation,
                reason, context, source, llmSpec, lineContent);
        }

        /// <summary>
        /// Gets cache statistics
        /// </summary>
        public CacheStatistics GetStatistics()
        {
            return _cache.GetStatistics();
        }

        /// <summary>
        /// Cleans expired cache entries
        /// </summary>
        public void CleanExpiredEntries(int maxAgeDays = 30)
        {
            _cache.CleanExpiredEntries(maxAgeDays);
        }
    }

    /// <summary>
    /// Factory for creating LlmServerSpec from LLM clients and configurations
    /// </summary>
    public class LlmServerSpecFactory
    {
        /// <summary>
        /// Creates LlmServerSpec from LLM client and config
        /// </summary>
        public LlmServerSpec CreateFromClient(ILlmClient llmClient, LLMConfig llmConfig)
        {
            if (llmClient == null || llmConfig == null)
            {
                return new LlmServerSpec
                {
                    Provider = "unknown",
                    Model = "unknown",
                    Version = "unknown",
                    Endpoint = ""
                };
            }

            // Determine provider type
            string provider = llmClient.GetType().Name.ToLower().Contains("bedrock") ? "bedrock" : "local";
            
            string model = "";
            string endpoint = "";
            
            if (provider == "local" && llmConfig.localLlmProviderConfig != null)
            {
                model = llmConfig.localLlmProviderConfig.modelId ?? "unknown";
                endpoint = llmConfig.localLlmProviderConfig.serviceUrl ?? "";
            }
            else if (provider == "bedrock" && llmConfig.bedrockProviderConfig != null)
            {
                model = llmConfig.bedrockProviderConfig.modelId ?? "unknown";
                endpoint = llmConfig.bedrockProviderConfig.region ?? "";
            }

            return new LlmServerSpec
            {
                Provider = provider,
                Model = model,
                Version = "v1.0", // Could be enhanced to get actual version
                Endpoint = endpoint
            };
        }
    }
}
