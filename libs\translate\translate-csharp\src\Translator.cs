﻿using Alcon.Interop.Translate.Helper;
using Alcon.Interop.Translate.Models;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Text.RegularExpressions;

namespace Alcon.Interop.Translate;

public class Translator : INotifyPropertyChanged
{
    #region INotifyPropertyChanged IMPLEMENTATION

    public event PropertyChangedEventHandler PropertyChanged;
    protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }

    #endregion

    #region PRIVATE FIELDS

    private static readonly Translator _instance = new();

    private readonly Dictionary<string, Dictionary<string, LocalizedString>> _translations = new(StringComparer.OrdinalIgnoreCase);
    private const string _defaultLanguageKey = "English";
    private string _currentUserLanguage = _defaultLanguageKey;

    #endregion

    #region CONSTRUCTORS

    static Translator()
    {
    }

    #endregion

    #region PUBLIC PROPERTIES

    public static Translator Instance => _instance;

    public string CurrentUserLanguage
    {
        get => _currentUserLanguage;
        set
        {
            if (_currentUserLanguage != value)
            {
                _currentUserLanguage = value;
            }
        }
    }

    public void UpdateUI()
    {
        OnPropertyChanged(nameof(CurrentUserLanguage));
    }

    public HashSet<LanguageModel> AvailableLanguages { get; private set; } = new HashSet<LanguageModel>();

    #endregion

    #region PUBLIC METHODS

    /// <summary>
    /// Loads translations from specified files
    /// </summary>
    /// <param name="translationFiles"></param>
    public void Initialize(List<string> translationFiles, string currentLanguage = null)
    {
        _translations.Clear();

        foreach (string translationFile in translationFiles)
        {
            _ = LoadTranslation(translationFile);
        }

        if (!string.IsNullOrEmpty(currentLanguage))
        {
            CurrentUserLanguage = currentLanguage;
            UpdateUI();
        }

        //--- TODO - return errors
    }

    /// <summary>
    /// Translates the text given the specific metadata
    /// </summary>
    /// <param name="metadata"></param>
    /// <returns></returns>
    public string TranslateM(string metadata)
    {
        return !string.IsNullOrWhiteSpace(metadata) &&
            TranslateUtils.GetTextId(metadata) is string textId &&
            TranslateMetadata.TryGetValue(metadata, TranslateMetadata.EnglishTextPrefix, out string englishText)
            ? Translate(textId, englishText)
            : metadata;
    }

    /// <summary>
    /// Translates the text given the specific metadata containing placeholders for the formatted values. Accepted placeholders are %[String], %[Number] and %[Date]
    /// </summary>
    /// <param name="metadata"></param>
    /// <param name="values">All values must be formatted strings</param>
    /// <returns></returns>
    public string TranslateM(string metadata, params string[] values)
    {
        return TranslateM(metadata) is string translation
            ? TranslationFormat(translation, values)
            : metadata;
    }

    /// <summary>
    /// Translates the specific text in the current language of the Translator
    /// </summary>
    /// <param name="textId"></param>
    /// <param name="englishText"></param>
    /// <returns></returns>
    public string Translate(string textId, string englishText)
    {
        return Translate(textId, englishText, CurrentUserLanguage);
    }

    /// <summary>
    /// Translates the specific text in the specified language
    /// </summary>
    /// <param name="textId"></param>
    /// <param name="englishText"></param>
    /// <param name="language"></param>
    /// <returns></returns>
    public string Translate(string textId, string englishText, string language)
    {
        if (language is null) return englishText;

        return _translations.ContainsKey(language) &&
            _translations[language].ContainsKey(textId)
            ? _translations[language][textId].Text ?? englishText
            : englishText;
    }

    #endregion

    #region PRIVATE METHODS

    private (bool result, string errorMessage) LoadTranslation(string translationFile)
    {
        try
        {
            (LanguageTranslation translation, string errorMessage) = Serializer.DeserializeFromXmlFile<LanguageTranslation>(translationFile);

            if (string.IsNullOrEmpty(errorMessage))
            {
                _translations[translation.Language] = new();

                foreach (LocalizedStringGroup group in translation.Groups)
                {
                    foreach (LocalizedString ls in group.Children)
                    {
                        _translations[translation.Language][ls.Id] = ls;
                    }
                }

                // Add the default language - English
                if (!AvailableLanguages.Any(t => t.Name == _defaultLanguageKey))
                {
                    AvailableLanguages.Add(new LanguageModel { Name = _defaultLanguageKey });
                }

                // Add the current loaded translation to available languages
                AvailableLanguages.Add(new LanguageModel { Name = translation.Language });
            }

            return (!string.IsNullOrEmpty(errorMessage), errorMessage);
        }
        catch (Exception e)
        {
            return (false, e.ToString());
        }
    }

    private string TranslationFormat(string translation, params string[] values)
    {
        MatchCollection matchCollection = Regex.Matches(translation, TranslateMetadata.Placeholders);

        if (values == null || values.Any(v => v == null) || matchCollection.Count != values.Length)
        {
            return translation;
        }

        for (int i = matchCollection.Count - 1; i >= 0; i--)
        {
            Match match = matchCollection[i];

            translation = translation.Remove(match.Index, match.Length).Insert(match.Index, values[i]);
        }

        return translation;
    }

    #endregion
}
