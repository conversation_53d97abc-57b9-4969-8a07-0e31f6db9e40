/*
 * Copyright 2021-present Alcon. All rights reserved.
 * Alcon proprietary/confidential.
 * Use is subject to license terms.
 */

using NUnit.Framework;
using System;
using System.IO;
using System.Linq;
using System.Xml.Linq;

namespace LocalizationLib.Tests;

[TestFixture]
public class FileMergerTest
{
    private string _tempFolder;

    private string _firstLanguageFileContent = @"<?xml version=""1.0"" encoding=""utf-8""?>
        <language version=""rel_01.14"" name=""Romanian"">
          <fontclasses>
            <fontclass name=""font18"" face=""Cal<PERSON><PERSON>, Arial"" weight=""400"" size=""18"" />
            <fontclass name=""font14"" face=""Calibri, Arial"" weight=""400"" size=""14"" />
            <fontclass name=""font14Bold"" face=""Cal<PERSON><PERSON>, <PERSON>l"" weight=""800"" size=""14"" />
            <fontclass name=""font16"" face=""<PERSON><PERSON><PERSON>, <PERSON>l"" weight=""400"" size=""16"" />
            <fontclass name=""font16Bold"" face=""<PERSON><PERSON><PERSON>, <PERSON>l"" weight=""700"" size=""16"" />
            <fontclass name=""font18Light"" face=""Calibri, Arial"" weight=""100"" size=""18"" />
          </fontclasses>
          <strings>
            <group id=""TestProject.Test1.TestFile1"">
              <LocalizedString id=""1"" EnglishText=""Hello World"" Text=""Hello World"" fontclass=""font18"" FontFamily=""Calibri"" FontSize=""18"" FontWeight=""400"" width=""38"" height=""21"" ControlType=""TextBlock"" Category=""Text""/>
              <LocalizedString id=""2"" EnglishText=""Go Back"" Text=""Go Back"" fontclass=""font14"" FontFamily=""Calibri"" FontSize=""14"" FontWeight=""400"" width=""38"" height=""21"" ControlType=""TextBlock"" Category=""Text""/>
            </group>
            <group id=""TestProject.Test1.TestFile2"">
              <LocalizedString id=""1"" EnglishText=""Save"" Text=""Save"" fontclass=""font18"" FontFamily=""Calibri"" FontSize=""18"" FontWeight=""400"" width=""38"" height=""21"" ControlType=""TextBlock"" Category=""Text""/>
            </group>
          </strings>
        </language>";


    private string _secondLanguageFileContent = @"<?xml version=""1.0"" encoding=""utf-8""?>
        <language version=""rel_01.14"" name=""Romanian"">
          <fontclasses>
            <fontclass name=""font18"" face=""Calibri, Arial"" weight=""400"" size=""18"" />
            <fontclass name=""font14"" face=""Calibri, Arial"" weight=""400"" size=""14"" />
            <fontclass name=""font14Bold"" face=""Calibri, Arial"" weight=""800"" size=""14"" />
            <fontclass name=""font16"" face=""Segoe UI"" weight=""400"" size=""16"" />
            <fontclass name=""font16Bold"" face=""Segoe UI"" weight=""700"" size=""16"" />
            <fontclass name=""font18Light"" face=""Calibri, Arial"" weight=""100"" size=""18"" />
            <fontclass name=""font12"" face=""Calibri, Arial"" weight=""400"" size=""12"" />
          </fontclasses>
          <strings>
            <group id=""TestProject.Test1.TestFile1"">
              <LocalizedString id=""1"" EnglishText=""Save"" Text=""Save"" fontclass=""font18"" FontFamily=""Calibri"" FontSize=""18"" FontWeight=""400"" width=""38"" height=""21"" ControlType=""TextBlock"" Category=""Text""/>
              <LocalizedString id=""2"" EnglishText=""Go Back"" Text=""Go Back"" fontclass=""font14"" FontFamily=""Calibri"" FontSize=""14"" FontWeight=""400"" width=""38"" height=""21"" ControlType=""TextBlock"" Category=""Text"" SourceFile=""TestProject\TestFile1.cs""/>
            </group>
            <group id=""TestProject.Test1.TestFile2"">
              <LocalizedString id=""1"" EnglishText=""Save"" Text=""Save"" fontclass=""font18"" FontFamily=""Calibri"" FontSize=""18"" FontWeight=""400"" width=""38"" height=""21"" ControlType=""TextBlock"" Category=""Text""/>
              <LocalizedString id=""2"" EnglishText=""Update"" Text=""Update"" fontclass=""font18"" FontFamily=""Calibri"" FontSize=""18"" FontWeight=""400"" width=""38"" height=""21"" ControlType=""TextBlock"" Category=""Text""/>
            </group>
            <group id=""TestProject.Test1.TestFile3"">
              <LocalizedString id=""1"" EnglishText=""Add user"" Text=""Add user"" fontclass=""font18"" FontFamily=""Calibri"" FontSize=""18"" FontWeight=""400"" width=""38"" height=""21"" ControlType=""TextBlock"" Category=""Text""/>
              <LocalizedString id=""2"" EnglishText=""Delete user"" Text=""Delete user"" fontclass=""font18"" FontFamily=""Calibri"" FontSize=""18"" FontWeight=""400"" width=""38"" height=""21"" ControlType=""TextBlock"" Category=""Text""/>
            </group>
          </strings>
        </language>";

    [SetUp]
    public void SetUp()
    {
        // Create a unique temporary folder for these tests
        _tempFolder = Path.Combine(Path.GetTempPath(), "FileMergerTests_" + Guid.NewGuid());
        Directory.CreateDirectory(_tempFolder);
    }

    [TearDown]
    public void TearDown()
    {
        if (Directory.Exists(_tempFolder))
        {
            Directory.Delete(_tempFolder, true);
        }
    }

    [Test]
    public void RunFileMerge_ForceMerge_False()
    {
        string languageFile1 = Path.Combine(_tempFolder, "LanguageFile1.xml");
        File.WriteAllText(languageFile1, _firstLanguageFileContent);

        string languageFile2 = Path.Combine(_tempFolder, "LanguageFile2.xml");
        File.WriteAllText(languageFile2, _secondLanguageFileContent);

        string updatedLanguageFile = Path.Combine(_tempFolder, "updatedLanguageFile.xml");

        FileMerge fileMerge = new FileMerge();
        fileMerge.Merge(languageFile1, languageFile2, updatedLanguageFile);

        // updated language file should exist
        Assert.IsTrue(File.Exists(updatedLanguageFile));

        XDocument xmlDocument = XDocument.Load(updatedLanguageFile);

        // updated languageFile should have new elements added
        Assert.IsTrue(xmlDocument.Descendants("fontclass").Any(fc => (string)fc.Attribute("name") == "font12"));
        Assert.IsTrue(xmlDocument.Descendants("group").Any(group => (string)group.Attribute("id") == "TestProject.Test1.TestFile3"));
        Assert.IsTrue(xmlDocument.Descendants("LocalizedString").Any(ls => (string)ls.Attribute("id") == "2" && ls.Parent.Name == "group" && (string)ls.Parent.Attribute("id") == "TestProject.Test1.TestFile2"));
        Assert.IsTrue(xmlDocument.Descendants("LocalizedString").Any(ls => (string)ls.Attribute("EnglishText") == "Go Back" && (string)ls.Attribute("SourceFile") == "TestProject\\TestFile1.cs"));


        // different value attributes inside element shouldn't be changed as forceMerge isn't set
        Assert.IsTrue(xmlDocument.Descendants("fontclass").Any(fc => (string)fc.Attribute("name") == "font16" && (string)fc.Attribute("face") == "Calibri, Arial"));
        Assert.IsTrue(xmlDocument.Descendants("fontclass").Any(fc => (string)fc.Attribute("name") == "font16Bold" && (string)fc.Attribute("face") == "Calibri, Arial"));
    }

    [Test]
    public void RunFileMerge_ForceMerge_True()
    {
        string languageFile1 = Path.Combine(_tempFolder, "LanguageFile1.xml");
        File.WriteAllText(languageFile1, _firstLanguageFileContent);

        string languageFile2 = Path.Combine(_tempFolder, "LanguageFile2.xml");
        File.WriteAllText(languageFile2, _secondLanguageFileContent);

        string updatedLanguageFile = Path.Combine(_tempFolder, "updatedLanguageFile.xml");

        FileMerge fileMerge = new FileMerge();
        fileMerge.Merge(languageFile1, languageFile2, updatedLanguageFile, true);

        // updated language file should exist
        Assert.IsTrue(File.Exists(updatedLanguageFile));

        XDocument xmlDocument = XDocument.Load(updatedLanguageFile);

        // updated languageFile should have new elements added
        Assert.IsTrue(xmlDocument.Descendants("fontclass").Any(fc => (string)fc.Attribute("name") == "font12"));
        Assert.IsTrue(xmlDocument.Descendants("group").Any(group => (string)group.Attribute("id") == "TestProject.Test1.TestFile3"));
        Assert.IsTrue(xmlDocument.Descendants("LocalizedString").Any(ls => (string)ls.Attribute("id") == "2" && ls.Parent.Name == "group" && (string)ls.Parent.Attribute("id") == "TestProject.Test1.TestFile2"));
        Assert.IsTrue(xmlDocument.Descendants("LocalizedString").Any(ls => (string)ls.Attribute("EnglishText") == "Go Back" && (string)ls.Attribute("SourceFile") == "TestProject\\TestFile1.cs"));

        // different value attributes inside element should be changed as forceMerge is set
        Assert.IsTrue(xmlDocument.Descendants("fontclass").Any(fc => (string)fc.Attribute("name") == "font16" && (string)fc.Attribute("face") == "Segoe UI"));
        Assert.IsTrue(xmlDocument.Descendants("fontclass").Any(fc => (string)fc.Attribute("name") == "font16Bold" && (string)fc.Attribute("face") == "Segoe UI"));
    }

    [Test]
    public void RunFileMerge_ExtensionsNotMatching()
    {
        string languageFile1 = Path.Combine(_tempFolder, "LanguageFile1.xaml");
        File.WriteAllText(languageFile1, _firstLanguageFileContent);

        string languageFile2 = Path.Combine(_tempFolder, "LanguageFile2.xml");
        File.WriteAllText(languageFile2, _secondLanguageFileContent);

        string updatedLanguageFile = Path.Combine(_tempFolder, "updatedLanguageFile.xml");

        FileMerge fileMerge = new FileMerge();
        fileMerge.Merge(languageFile1, languageFile2, updatedLanguageFile, true);

        // output file shouldn't exist
        Assert.IsFalse(File.Exists(updatedLanguageFile));
    }
}