/*
 * Copyright 2021-present Alcon. All rights reserved.
 * Alcon proprietary/confidential.
 * Use is subject to license terms.
 */

using NUnit.Framework;
using System;
using System.IO;
using System.Linq;

namespace LocalizationLib.Tests;

[TestFixture]
public class CsExtractorModeTests
{
    private string _tempFolder;
    private string _projectFolder;
    private string _reportFile;

    [SetUp]
    public void SetUp()
    {
        // Reset the config manager before each test
        ProjectTextExtractorConfigManager.Reset();
        
        // Create a unique temporary folder for these tests
        _tempFolder = Path.Combine(Path.GetTempPath(), "CsExtractorModeTests_" + Guid.NewGuid());
        Directory.CreateDirectory(_tempFolder);

        // Create subfolder for the "project"
        _projectFolder = Path.Combine(_tempFolder, "Project");
        Directory.CreateDirectory(_projectFolder);

        // Paths for report
        _reportFile = Path.Combine(_tempFolder, "report.txt");
    }

    [TearDown]
    public void TearDown()
    {
        if (Directory.Exists(_tempFolder))
        {
            Directory.Delete(_tempFolder, true);
        }
    }

    [Test]
    public void Run_FindOnlyMode_DoesNotModifyCsFiles()
    {
        // 1) Arrange: Create a sample .cs file with a hard-coded string
        string csContent = @"
using System;

namespace Demo
{
    public class TestClass
    {
        public void ShowMessage()
        {
            Console.WriteLine(""Hello World"");
        }
    }
}
";
        string csPath = Path.Combine(_projectFolder, "TestClass.cs");
        File.WriteAllText(csPath, csContent);

        // 2) Act: Run extractor in find-only mode
        var extractor = new ProjectLocalizationExtractor(); 
        extractor.Run(_projectFolder, _reportFile, "cs");

        // 3) Assert: 
        //    a) The .cs file is unchanged
        string postCs = File.ReadAllText(csPath);
        Assert.AreEqual(csContent, postCs, 
            "In FindOnly mode, the .cs file should NOT be modified.");

        //    b) The report file should exist and reference "FindOnly" + "Missing translation"
        Assert.IsTrue(File.Exists(_reportFile),
            "Report file should be created in find-only mode.");
        string reportContent = File.ReadAllText(_reportFile);
        StringAssert.Contains("FindOnly", reportContent, 
            "Report should indicate find-only mode for .cs files.");
        StringAssert.Contains("Missing translation", reportContent,
            "Report should mention missing translation for 'Hello World'.");

        //    c) Verify that the extracted text was stored in the config
        var config = ProjectTextExtractorConfigManager.GetConfigSafe(_projectFolder);
        Assert.IsNotNull(config, "Config should be created");
        Assert.IsTrue(config.ExtractedTexts.Count > 0, "Extracted texts should be stored in config");
        Assert.IsTrue(config.ExtractedTexts.Values.Any(x => x.OriginalText == "Hello World"), 
            "Config should contain the extracted text 'Hello World'");
    }
}
