/*
 * Copyright 2021-present Alcon. All rights reserved.
 * Alcon proprietary/confidential.
 * Use is subject to license terms.
 */

using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Xml.Linq;
using System.Text.RegularExpressions;
using System.Xml;

namespace LocalizationLib;

/// <summary>
/// Processes XAML files to move non-dependency property text values to code-behind.
/// This helps prepare the codebase for localization by ensuring all user-facing text
/// is properly managed in the code-behind files.
/// </summary>
public class XamlTextToCodeBehindProcessor
{
    private readonly string _projectPath;
    private readonly XamlProcessor _xamlProcessor;

    public XamlTextToCodeBehindProcessor(string projectPath = null)
    {
        _projectPath = projectPath;
        _xamlProcessor = new XamlProcessor(projectPath);
    }

    /// <summary>
    /// Processes a XAML file and its associated code-behind file to move non-dependency property
    /// text values from XAML to code-behind.
    /// </summary>
    /// <param name="xamlPath">Path to the XAML file to process</param>
    /// <returns>Processing result containing details about the changes made</returns>
    public FileProcessingResult ProcessFile(string xamlPath)
    {
        var result = new FileProcessingResult { FilePath = xamlPath };

        try
        {
            // First, use XamlProcessor to find all text properties
            var xamlResult = _xamlProcessor.ProcessXamlFile(xamlPath);
            
            // Filter for non-dependency properties that need to be moved
            var propertiesToMove = xamlResult.Replacements
                .Where(r => !r.IsDependencyProperty || r.IsRootElement)
                .ToList();

            if (!propertiesToMove.Any())
            {
                return result;
            }

            // Get the code-behind file path
            string codeBehindPath = GetCodeBehindPath(xamlPath);
            if (!File.Exists(codeBehindPath))
            {
                throw new FileNotFoundException($"Code-behind file not found: {codeBehindPath}");
            }

            // Read the original files
            string xamlContent = File.ReadAllText(xamlPath);
            string codeBehindContent = File.ReadAllText(codeBehindPath);

            // Process the files
            var (updatedXaml, updatedCodeBehind) = ProcessFiles(xamlContent, codeBehindContent, propertiesToMove);

            // Write the changes if any modifications were made
            if (updatedXaml != xamlContent || updatedCodeBehind != codeBehindContent)
            {
                // Use temporary files to ensure atomic writes
                string tempXamlPath = xamlPath + ".tmp";
                string tempCodeBehindPath = codeBehindPath + ".tmp";
                string xamlBackupPath = xamlPath + ".bak";
                string codeBehindBackupPath = codeBehindPath + ".bak";

                try
                {
                    File.WriteAllText(tempXamlPath, updatedXaml);
                    File.WriteAllText(tempCodeBehindPath, updatedCodeBehind);

                    File.Replace(tempXamlPath, xamlPath, xamlBackupPath);
                    File.Replace(tempCodeBehindPath, codeBehindPath, codeBehindBackupPath);

                    // Clean up backup files after successful replacement
                    if (File.Exists(xamlBackupPath)) File.Delete(xamlBackupPath);
                    if (File.Exists(codeBehindBackupPath)) File.Delete(codeBehindBackupPath);
                }
                finally
                {
                    // Clean up temporary files if they still exist
                    if (File.Exists(tempXamlPath)) File.Delete(tempXamlPath);
                    if (File.Exists(tempCodeBehindPath)) File.Delete(tempCodeBehindPath);
                }

                result.Changed = true;
                result.Replacements.AddRange(propertiesToMove);
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error processing file {xamlPath}: {ex.Message}");
            throw;
        }

        return result;
    }

    private (string xaml, string codeBehind) ProcessFiles(
        string xamlContent,
        string codeBehindContent,
        List<ReplacementRecord> propertiesToMove)
    {
        // Load XAML with preserved whitespace
        var settings = new XmlReaderSettings { IgnoreWhitespace = false };
        var doc = XDocument.Parse(xamlContent, LoadOptions.PreserveWhitespace);

        // Remove XML declaration if it exists
        doc.Declaration = null;

        var codeBehindLines = codeBehindContent.Split('\n').ToList();

        // Process each property to move
        foreach (var property in propertiesToMove)
        {
            if (property.IsRootElement)
            {
                // Handle root element properties differently
                ProcessRootElementProperty(doc, codeBehindLines, property);
            }
            else
            {
                // Find all elements with the matching property and value
                var elements = doc.Descendants()
                    .Where(e => e.Attributes()
                        .Any(a => a.Name.LocalName == property.Property && 
                                 a.Value == property.OriginalValue))
                    .ToList();

                foreach (var element in elements)
                {
                    // Get or generate element name
                    string elementName = element.Attribute(XName.Get("Name", "http://schemas.microsoft.com/winfx/2006/xaml"))?.Value;
                    if (string.IsNullOrEmpty(elementName))
                    {
                        elementName = GenerateUniqueName(element.Name.LocalName);
                        element.Add(new XAttribute(XName.Get("Name", "http://schemas.microsoft.com/winfx/2006/xaml"), elementName));
                    }

                    // Remove the property
                    element.Attribute(property.Property)?.Remove();

                    // Add the property assignment to code-behind
                    AddPropertyAssignment(codeBehindLines, elementName, property);
                }
            }
        }

        // Convert back to string while preserving formatting
        var sb = new StringBuilder();
        using (var writer = new StringWriter(sb))
        {
            // Save without XML declaration
            var ws = new XmlWriterSettings 
            { 
                OmitXmlDeclaration = true,
                Indent = true,
                NewLineHandling = NewLineHandling.Entitize
            };
            using (var xmlWriter = XmlWriter.Create(writer, ws))
            {
                doc.Save(xmlWriter);
            }
        }
        string updatedXaml = sb.ToString();

        // Restore original line endings
        updatedXaml = updatedXaml.Replace("\r\n", "\n").Replace("\r", "\n");
        if (xamlContent.Contains("\r\n"))
        {
            updatedXaml = updatedXaml.Replace("\n", "\r\n");
        }

        return (updatedXaml, string.Join("\n", codeBehindLines));
    }

    private void ProcessRootElementProperty(XDocument doc, List<string> codeBehindLines, ReplacementRecord property)
    {
        // Find the root element
        var rootElement = doc.Root;
        if (rootElement == null) return;

        // Remove the property from the root element
        rootElement.Attribute(property.Property)?.Remove();

        // Add the property assignment directly in the constructor
        AddRootPropertyAssignment(codeBehindLines, property);
    }

    private void AddRootPropertyAssignment(List<string> codeBehindLines, ReplacementRecord property)
    {
        // Find the InitializeComponent method
        int initComponentIndex = -1;
        for (int i = 0; i < codeBehindLines.Count; i++)
        {
            if (codeBehindLines[i].Contains("InitializeComponent();"))
            {
                initComponentIndex = i;
                break;
            }
        }

        if (initComponentIndex == -1)
        {
            throw new InvalidOperationException("InitializeComponent method not found in code-behind file");
        }

        // Add the property assignment after InitializeComponent
        string assignment = $"            {property.Property} = \"{property.OriginalValue}\";";
        codeBehindLines.Insert(initComponentIndex + 1, assignment);
    }

    private string GenerateUniqueName(string baseName)
    {
        return $"autoGen_{baseName}_{Guid.NewGuid().ToString("N").Substring(0, 8)}";
    }

    private void AddPropertyAssignment(List<string> codeBehindLines, string elementName, ReplacementRecord property)
    {
        // Find the InitializeComponent method
        int initComponentIndex = -1;
        for (int i = 0; i < codeBehindLines.Count; i++)
        {
            if (codeBehindLines[i].Contains("InitializeComponent();"))
            {
                initComponentIndex = i;
                break;
            }
        }

        if (initComponentIndex == -1)
        {
            throw new InvalidOperationException("InitializeComponent method not found in code-behind file");
        }

        // Add the property assignment after InitializeComponent
        string assignment = $"            {elementName}.{property.Property} = \"{property.OriginalValue}\";";
        codeBehindLines.Insert(initComponentIndex + 1, assignment);
    }

    private string GetCodeBehindPath(string xamlPath)
    {
        string directory = Path.GetDirectoryName(xamlPath);
        string fileName = Path.GetFileNameWithoutExtension(xamlPath);
        return Path.Combine(directory, fileName + ".xaml.cs");
    }
} 