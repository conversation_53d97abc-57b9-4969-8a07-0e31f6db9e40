﻿using System;
using System.Globalization;
using System.Linq;
using System.Windows.Data;

namespace Alcon.Interop.Translate;

public class TranslateConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        return parameter is string metadata &&
            Translator.Instance.TranslateM(metadata) is string translation
            ? translation
            : Binding.DoNothing;
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}

public class TranslateFormatConverter : IMultiValueConverter
{
    public object Convert(object[] values, Type targetType, object parameter, CultureInfo culture)
    {
        return parameter is string metadata &&
            values?.Length > 0 &&
            Translator.Instance.TranslateM(metadata, values.Select(v => v as string).ToArray()) is string translation
            ? translation
            : Binding.DoNothing;
    }

    public object[] ConvertBack(object value, Type[] targetTypes, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}
