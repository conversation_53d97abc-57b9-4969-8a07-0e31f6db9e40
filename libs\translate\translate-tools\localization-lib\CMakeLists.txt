cmake_minimum_required(VERSION 3.22.0)
include_guard(GLOBAL)
project(localizationlib VERSION 1.0 LANGUAGES CSharp)

include("${CMAKE_CURRENT_SOURCE_DIR}/../../../../cmake/Globals.cmake")

set(TARGET ${PROJECT_NAME})

file(GLOB_RECURSE CS_FILES CONFIGURE_DEPENDS "${CMAKE_CURRENT_SOURCE_DIR}/src/*.cs")

add_library(${TARGET} SHARED ${CS_FILES})

set_target_properties(${TARGET} PROPERTIES
    DOTNET_SDK ${ROOT_DOTNET_SDK}
    DOTNET_TARGET_FRAMEWORK ${ROOT_DOTNET_TARGET_FRAMEWORK}    
    VS_PACKAGE_REFERENCES "${Microsoft.CodeAnalysis.CSharp.Scripting};${Newtonsoft.Json};${CommunityToolkit.Mvvm}"
)
