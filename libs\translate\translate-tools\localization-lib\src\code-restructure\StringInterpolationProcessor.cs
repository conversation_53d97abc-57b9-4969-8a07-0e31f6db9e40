using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Text.RegularExpressions;

namespace LocalizationLib
{
    /// <summary>
    /// Processor that converts single-line interpolated strings (e.g. $"...") to string.Format calls,
    /// skipping verbatim/multiline strings and ignoring anything inside comments.
    ///
    /// Uses a "brace-aware" parse so that quotes inside interpolation holes don't prematurely
    /// end the interpolated string. This preserves nested interpolation (only converts the outer).
    /// </summary>
    public class StringInterpolationProcessor
    {
        public class ConversionResult
        {
            public bool Changed { get; set; }
            public string UpdatedContent { get; set; }

            /// <summary>
            /// Holds info about each converted string interpolation:
            /// (LineNumber, Column, OriginalText, ConvertedText)
            /// </summary>
            public List<(int Line, int Column, string Original, string Converted)> Conversions { get; set; }
                = new List<(int, int, string, string)>();
        }

        public ConversionResult ProcessFile(string filePath)
        {
            var result = new ConversionResult();
            if (!File.Exists(filePath))
            {
                Console.WriteLine($"[ERROR] File not found: {filePath}");
                result.UpdatedContent = "";
                return result;
            }

            // Detect original file line ending and trailing newline
            string fileContent = File.ReadAllText(filePath);
            string lineEnding = "\n";
            if (fileContent.Contains("\r\n")) lineEnding = "\r\n";
            else if (fileContent.Contains("\r")) lineEnding = "\r";
            bool endsWithNewline = fileContent.EndsWith(lineEnding);

            var originalLines = File.ReadAllLines(filePath);
            var updatedLines = new string[originalLines.Length];

            bool fileChanged = false;
            bool inBlockComment = false;

            for (int lineIndex = 0; lineIndex < originalLines.Length; lineIndex++)
            {
                string line = originalLines[lineIndex];
                if (string.IsNullOrEmpty(line))
                {
                    updatedLines[lineIndex] = line;
                    continue;
                }

                var updatedLine = new StringBuilder();
                int i = 0;
                bool lineChanged = false;
                bool inLineComment = false;

                while (i < line.Length)
                {
                    // If we are currently in a block comment, check if we can exit it
                    if (inBlockComment)
                    {
                        // Look for '*/'
                        if (i + 1 < line.Length && line[i] == '*' && line[i + 1] == '/')
                        {
                            inBlockComment = false;
                            updatedLine.Append("*/");
                            i += 2;
                            continue;
                        }
                        else
                        {
                            // Still inside block comment, just copy this character
                            updatedLine.Append(line[i]);
                            i++;
                            continue;
                        }
                    }

                    // If not in a block comment, check for start of block comment
                    if (!inLineComment)
                    {
                        if (i + 1 < line.Length && line[i] == '/' && line[i + 1] == '*')
                        {
                            // Start block comment
                            inBlockComment = true;
                            updatedLine.Append("/*");
                            i += 2;
                            continue;
                        }
                        // Check for line comment
                        if (i + 1 < line.Length && line[i] == '/' && line[i + 1] == '/')
                        {
                            // Remainder of line is a comment
                            inLineComment = true;
                            // Copy rest of line as-is
                            updatedLine.Append(line.Substring(i));
                            break;
                        }
                    }

                    // If we're in a line comment, just copy the rest
                    if (inLineComment)
                    {
                        updatedLine.Append(line[i]);
                        i++;
                        continue;
                    }

                    // Now look for a standard interpolated string start: $" 
                    // skipping verbatim: $@" or @$"
                    if (line[i] == '$' && i + 1 < line.Length && line[i + 1] == '"')
                    {
                        bool isVerbatim = false;

                        // Check if preceded by '@' => @$
                        if (i - 1 >= 0 && line[i - 1] == '@')
                        {
                            isVerbatim = true;
                        }
                        // Or check if followed by '@' => $@"
                        if (i + 2 < line.Length && line[i + 2] == '@')
                        {
                            // e.g. $"@" => skip
                            isVerbatim = true;
                        }

                        if (isVerbatim)
                        {
                            // skip converting verbatim
                            updatedLine.Append("$\"");
                            i += 2;
                            continue;
                        }

                        // Attempt brace-aware parse of a single-line interpolation
                        int startCol = i + 1; // for potential logging
                        int startIndex = i;
                        i += 2; // skip past $"
                        var interpolationBuilder = new StringBuilder();
                        interpolationBuilder.Append("$\"");

                        bool foundClosingQuote = false;
                        int braceDepth = 0;

                        // Read until we find the ending quote on this same line (or we run out of line).
                        while (i < line.Length)
                        {
                            char c = line[i];
                            interpolationBuilder.Append(c);

                            // Handle escaped chars like \" or \{
                            if (c == '\\' && (i + 1 < line.Length))
                            {
                                i++;
                                interpolationBuilder.Append(line[i]);
                                i++;
                                continue;
                            }

                            if (c == '{')
                            {
                                braceDepth++;
                            }
                            else if (c == '}')
                            {
                                if (braceDepth > 0) braceDepth--;
                            }
                            else if (c == '"')
                            {
                                // Only treat this as the closing quote if we're not inside an interpolation hole
                                if (braceDepth == 0)
                                {
                                    foundClosingQuote = true;
                                    i++;
                                    break;
                                }
                            }

                            i++;
                        }

                        string originalInterpolatedText = interpolationBuilder.ToString();

                        // If we never found the closing quote or if there's a newline => skip as multiline
                        if (!foundClosingQuote ||
                            originalInterpolatedText.Contains("\r") ||
                            originalInterpolatedText.Contains("\n"))
                        {
                            // It's incomplete or multiline; leave it alone
                            updatedLine.Append(originalInterpolatedText);
                            continue;
                        }

                        // Extract the inside portion (excluding `$"` at start and final `"`)
                        string inside = originalInterpolatedText.Substring(2, originalInterpolatedText.Length - 3);

                        // Check if it even has a { } hole
                        if (!HasInterpolationHole(inside))
                        {
                            // No actual interpolation => keep as is
                            updatedLine.Append(originalInterpolatedText);
                            continue;
                        }

                        try
                        {
                            // parse holes, build string.Format, etc.
                            var (formatString, args) = ParseInterpolatedContent(inside);
                            if (args.Count == 0)
                            {
                                // no actual expressions
                                updatedLine.Append(originalInterpolatedText);
                            }
                            else
                            {
                                string safeFormat = EscapeStringLiteral(formatString);
                                string replacement = $"string.Format(\"{safeFormat}\", {string.Join(", ", args)})";

                                updatedLine.Append(replacement);
                                lineChanged = true;

                                // record conversion
                                (int lineNo, int colNo) = (lineIndex + 1, startCol);
                                // original vs. replaced
                                result.Conversions.Add((lineNo, colNo, originalInterpolatedText, replacement));
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"[ERROR] Interpolation parse failed at line {lineIndex + 1}: {ex.Message}");
                            // fallback: keep original text
                            updatedLine.Append(originalInterpolatedText);
                        }
                    }
                    else
                    {
                        // Normal character
                        updatedLine.Append(line[i]);
                        i++;
                    }
                } // end while over the line

                // Assign updated line
                if (lineChanged)
                {
                    fileChanged = true;
                    updatedLines[lineIndex] = updatedLine.ToString();
                }
                else
                {
                    updatedLines[lineIndex] = updatedLine.Length > 0
                        ? updatedLine.ToString()
                        : line;
                }
            } // end for each line

            // After processing all lines, reconstruct file with original line endings and trailing newline
            string updatedContent = string.Join(lineEnding, updatedLines);
            if (endsWithNewline)
                updatedContent += lineEnding;
            result.UpdatedContent = updatedContent;
            result.Changed = fileChanged;
            return result;
        }

        /// <summary>
        /// Check if the string content has at least one interpolation hole { ... }
        /// ignoring escaped braces {{ }}.
        /// </summary>
        private bool HasInterpolationHole(string content)
        {
            int braceLevel = 0;
            bool sawCompleteHole = false;
            for (int i = 0; i < content.Length; i++)
            {
                if (content[i] == '{')
                {
                    // Skip {{ => literal brace
                    if (i + 1 < content.Length && content[i + 1] == '{')
                    {
                        i++;
                        continue;
                    }
                    braceLevel++;
                }
                else if (content[i] == '}')
                {
                    // Skip }} => literal brace
                    if (i + 1 < content.Length && content[i + 1] == '}')
                    {
                        i++;
                        continue;
                    }
                    if (braceLevel > 0)
                    {
                        braceLevel--;
                        sawCompleteHole = true;
                    }
                }
            }
            return sawCompleteHole;
        }

        /// <summary>
        /// Parses the inside of a single-line interpolated string to find all holes { ... },
        /// replace them with {0}, {1}, etc. and build an argument list.
        /// </summary>
        private (string formatString, List<string> args) ParseInterpolatedContent(string inside)
        {
            var sb = new StringBuilder();
            var args = new List<string>();

            int i = 0;
            int lastPos = 0;
            int braceDepth = 0;
            bool inHole = false;
            int holeStart = -1;

            while (i < inside.Length)
            {
                char c = inside[i];
                if (c == '{')
                {
                    // Check for escaped {{
                    if (i + 1 < inside.Length && inside[i + 1] == '{')
                    {
                        i += 2; // skip both
                        continue;
                    }

                    if (!inHole)
                    {
                        // Copy literal text up to here
                        sb.Append(inside.Substring(lastPos, i - lastPos));
                        braceDepth = 1;
                        inHole = true;
                        holeStart = i;
                        i++;
                    }
                    else
                    {
                        braceDepth++;
                        i++;
                    }
                }
                else if (c == '}')
                {
                    // Check for escaped }}
                    if (i + 1 < inside.Length && inside[i + 1] == '}')
                    {
                        i += 2;
                        continue;
                    }

                    if (inHole)
                    {
                        braceDepth--;
                        if (braceDepth == 0)
                        {
                            // end the hole
                            int holeLength = i - holeStart - 1;
                            string expr = inside.Substring(holeStart + 1, holeLength).Trim();

                            var (exprOnly, formatSpec) = SplitFormatSpecifier(expr);
                            int argIndex = args.Count;
                            args.Add(exprOnly);

                            if (!string.IsNullOrEmpty(formatSpec))
                            {
                                sb.Append($"{{{argIndex}{formatSpec}}}");
                            }
                            else
                            {
                                sb.Append($"{{{argIndex}}}");
                            }

                            inHole = false;
                            i++;
                            lastPos = i;
                        }
                        else
                        {
                            i++;
                        }
                    }
                    else
                    {
                        i++;
                    }
                }
                else
                {
                    i++;
                }
            }

            // Append any trailing literal
            if (lastPos < inside.Length)
            {
                sb.Append(inside.Substring(lastPos));
            }

            string formatString = sb.ToString();
            return (formatString, args);
        }

        /// <summary>
        /// Splits an expression by a top-level format specifier, e.g. "value:C2" => ("value", ":C2").
        /// We only split if the colon is at braceDepth = 0 / parenDepth = 0, ignoring quotes.
        /// </summary>
        private (string expr, string formatSpec) SplitFormatSpecifier(string expr)
        {
            int depthParen = 0;
            int depthBrack = 0;
            bool inQuotes = false;
            char quoteChar = '\0';

            for (int i = 0; i < expr.Length; i++)
            {
                char c = expr[i];
                if (!inQuotes && (c == '"' || c == '\''))
                {
                    inQuotes = true;
                    quoteChar = c;
                }
                else if (inQuotes && c == quoteChar)
                {
                    inQuotes = false;
                }
                else if (!inQuotes)
                {
                    if (c == '(') depthParen++;
                    else if (c == ')') depthParen--;
                    else if (c == '[') depthBrack++;
                    else if (c == ']') depthBrack--;
                    else if (c == ':' && depthParen == 0 && depthBrack == 0)
                    {
                        // treat as format spec start
                        return (expr.Substring(0, i), ":" + expr.Substring(i + 1));
                    }
                }
            }

            return (expr, "");
        }

        /// <summary>
        /// Escape double-quotes and backslashes inside a string literal
        /// so it can safely appear in "..."
        /// </summary>
        private string EscapeStringLiteral(string text)
        {
            return text
                .Replace("\\", "\\\\")
                .Replace("\"", "\\\"");
        }
    }
}
