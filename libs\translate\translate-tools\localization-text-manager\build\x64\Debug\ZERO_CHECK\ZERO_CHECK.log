﻿  1>Checking Build System
  CMake is re-running because D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-text-manager/build/ui_extractor_tool/CMakeFiles/generate.stamp is out-of-date.
    the file 'D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-uitext-extractor/CMakeLists.txt'
    is newer than 'D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-text-manager/build/ui_extractor_tool/CMakeFiles/generate.stamp.depend'
    result='-1'
  -- Selecting Windows SDK version 10.0.22621.0 to target Windows 10.0.26100.
  -- Configuring done (0.4s)
  -- Generating done (0.2s)
  -- Build files have been written to: D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-text-manager/build
