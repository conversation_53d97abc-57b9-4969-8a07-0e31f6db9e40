<UserControl x:Class="TRICS.Planner.UI.Browser.PatientPreviewControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:LocalUiBrowser="clr-namespace:TRICS.Planner.UI.Browser"
             xmlns:GeneralConverter="clr-namespace:TRICS.Planner.UI.General.Converter;assembly=TRICSPlannerUIGeneral"
             xmlns:cmd="clr-namespace:AttachedCommandBehavior;assembly=AttachedCommandBehavior"
             xmlns:ModelBrowser="clr-namespace:TRICS.Planner.Model.PatientData;assembly=TRICSPlannerModelCommonTypes"
             xmlns:CoreMeasure="clr-namespace:TRICS.Planner.Model.Common.Measure;assembly=TRICSPlannerModelCommonTypes"             
             mc:Ignorable="d" 
             VerticalAlignment="Stretch"
             VerticalContentAlignment="Stretch"
             HorizontalAlignment="Stretch"
             HorizontalContentAlignment="Stretch">

    <UserControl.Resources>
        <GeneralConverter:BoolToVisibilityConverter x:Key="BoolToVisibilityConverter" />
        <GeneralConverter:BoolToInvisibilityConverter x:Key="BoolToInvisibilityConverter" />
        <GeneralConverter:ObjToVisibilityConverter x:Key="ObjToVisibilityConverter" />
        <GeneralConverter:ObjToInvisibilityConverter x:Key="ObjToInvisibilityConverter" />
        <GeneralConverter:EnumToBooleanConverter x:Key="EnumToBooleanConverter" />
        <GeneralConverter:EnumToVisibilityConverter x:Key="EnumToVisibilityConverter" />
        <Style TargetType="{x:Type ScrollBar}" BasedOn="{StaticResource ScrollBarStyle}"/>
    </UserControl.Resources>

    <Grid Margin="10,0,0,0">
        <Grid.RowDefinitions>
            <RowDefinition Height="53" />
            <RowDefinition Height="650" />
        </Grid.RowDefinitions>

        <Border Grid.Row="1" Style="{StaticResource VPContentBorderStyle}" Padding="0,20,0,0">
            <StackPanel Orientation="Vertical">
                <Border Background="{DynamicResource FilterTabUnselectedBackgroundColor}" BorderBrush="{DynamicResource TableBorder}"
                        Visibility="{Binding EyeInfoFilter.GroupId, Converter={StaticResource ObjToVisibilityConverter}}"
                        BorderThickness="1,1,1,1" CornerRadius="10,10,10,10" Width="400" Height="35" VerticalAlignment="Center" Margin="0,0,0,5">
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                        <TextBlock Text="Surgeries for Plan " Style="{StaticResource VPTextBlockStyle}" Foreground="{DynamicResource StaticTextColor}"
                                   HorizontalAlignment="Center" FontFamily="Calibri, Arial" FontSize="18" Margin="0,0,10,0"/>
                        
                        <Line HorizontalAlignment="Left"  Stretch="Fill" Stroke="{DynamicResource NotificationTextColor}" Opacity="0.5" Y2="1" Margin="0,10,0,10" />

                        <Button x:Name="ResetGroupFilterBtn"  VerticalAlignment="Center" HorizontalAlignment="Right" Click="ResetGroupFilterBtn_Click"
                                Style="{StaticResource HighlightedFlatButton}" Background="Transparent">
                            <Path Style="{StaticResource ContentButtonPathStyle}" Data="{DynamicResource CloseNotificationPath}" Opacity=".5" Fill="{DynamicResource ActiveTextColor}"
                                  RenderTransformOrigin="0.5,0.5" Height="18" Width="18" Stretch="Fill" />
                        </Button>
                    </StackPanel>
                </Border>
                <Grid Visibility="{Binding EyeInfoFilter.GroupId, Converter={StaticResource ObjToInvisibilityConverter}}">
                    <StackPanel x:Name="FiltersPanel" Orientation="Vertical">
                        <StackPanel x:Name="PlannedSurgeryFilterPanel" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,1,0,0"
                            Visibility="{Binding Path=EyeFlags, Converter={StaticResource EnumToVisibilityConverter}, ConverterParameter={x:Static ModelBrowser:EyeProcedureFlags.Plan}}">
                            <RadioButton GroupName="PlanCriteriaRadioGroup" Content="All" Style="{StaticResource VPLeftRadioButtonStyle}" VerticalAlignment="Bottom" SnapsToDevicePixels="True"
                                 IsChecked="{Binding Path=Criteria, Converter={StaticResource EnumToBooleanConverter}, ConverterParameter={x:Static ModelBrowser:EyeInfoFilterCriteria.All}}"/>
                            <RadioButton GroupName="PlanCriteriaRadioGroup" Content="Coming Soon" Style="{StaticResource VPCenterRadioButtonStyle}" VerticalAlignment="Bottom" SnapsToDevicePixels="True"
                                 IsChecked="{Binding Path=Criteria, Converter={StaticResource EnumToBooleanConverter}, ConverterParameter={x:Static ModelBrowser:EyeInfoFilterCriteria.Soon}}"/>
                            <RadioButton GroupName="PlanCriteriaRadioGroup" Content="Recently Updated" Style="{StaticResource VPCenterRadioButtonStyle}" VerticalAlignment="Bottom" SnapsToDevicePixels="True"
                                 IsChecked="{Binding Path=Criteria, Converter={StaticResource EnumToBooleanConverter}, ConverterParameter={x:Static ModelBrowser:EyeInfoFilterCriteria.Recent}}"/>
                            <RadioButton x:Name="ExportedRadio" GroupName="PlanCriteriaRadioGroup" Content="Exported" Style="{StaticResource VPRightRadioButtonStyle}" VerticalAlignment="Bottom" SnapsToDevicePixels="True"
                                 IsChecked="{Binding Path=Criteria, Converter={StaticResource EnumToBooleanConverter}, ConverterParameter={x:Static ModelBrowser:EyeInfoFilterCriteria.Shared}}"/>
                        </StackPanel>

                        <StackPanel x:Name="CompletedSurgeryFilterPanel" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,1,0,0"
                            Visibility="{Binding Path=EyeFlags, Converter={StaticResource EnumToVisibilityConverter}, ConverterParameter={x:Static ModelBrowser:EyeProcedureFlags.AllSurgery}}">
                            <RadioButton GroupName="SurgeryCriteriaRadioGroup" Content="All" Style="{StaticResource VPLeftRadioButtonStyle}" VerticalAlignment="Bottom" SnapsToDevicePixels="True"
                                 IsChecked="{Binding Path=Criteria, Converter={StaticResource EnumToBooleanConverter}, ConverterParameter={x:Static ModelBrowser:EyeInfoFilterCriteria.All}}"/>
                            <RadioButton GroupName="SurgeryCriteriaRadioGroup" Content="Special Cases" Style="{StaticResource VPCenterRadioButtonStyle}" VerticalAlignment="Bottom" SnapsToDevicePixels="True"
                                 IsChecked="{Binding Path=Criteria, Converter={StaticResource EnumToBooleanConverter}, ConverterParameter={x:Static ModelBrowser:EyeInfoFilterCriteria.Special}}"/>
                            <RadioButton GroupName="SurgeryCriteriaRadioGroup" Content="Recently Updated" Style="{StaticResource VPRightRadioButtonStyle}" VerticalAlignment="Bottom" SnapsToDevicePixels="True"
                                 IsChecked="{Binding Path=Criteria, Converter={StaticResource EnumToBooleanConverter}, ConverterParameter={x:Static ModelBrowser:EyeInfoFilterCriteria.Recent}}"/>
                        </StackPanel>
                        <StackPanel x:Name="MeasurementFilterPanel" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,1,0,0"
                            Visibility="{Binding Path=EyeFlags, Converter={StaticResource EnumToVisibilityConverter}, ConverterParameter={x:Static ModelBrowser:EyeProcedureFlags.AllDiagnostic}}">
                            <RadioButton x:Name="AllMeasurementsCriteriaRadio" GroupName="MeasurementCriteriaRadioGroup" Content="All" Style="{StaticResource VPLeftRadioButtonStyle}" VerticalAlignment="Bottom" SnapsToDevicePixels="True"
                                 IsChecked="{Binding Path=Criteria, Converter={StaticResource EnumToBooleanConverter}, ConverterParameter={x:Static ModelBrowser:EyeInfoFilterCriteria.All}}"/>
                            <RadioButton x:Name="PhakicCriteriaRadio" GroupName="MeasurementCriteriaRadioGroup" Content="Phakic" Style="{StaticResource VPCenterRadioButtonStyle}" VerticalAlignment="Bottom" SnapsToDevicePixels="True"
                                         IsChecked="{Binding Path=Criteria, Converter={StaticResource EnumToBooleanConverter}, ConverterParameter={x:Static ModelBrowser:EyeInfoFilterCriteria.PhakicOnly}}"/>
                            <RadioButton x:Name="PseudophakicCriteriaRadio" GroupName="MeasurementCriteriaRadioGroup" Content="Pseudophakic" Style="{StaticResource VPCenterRadioButtonStyle}" VerticalAlignment="Bottom" SnapsToDevicePixels="True"
                                         IsChecked="{Binding Path=Criteria, Converter={StaticResource EnumToBooleanConverter}, ConverterParameter={x:Static ModelBrowser:EyeInfoFilterCriteria.PseudophakicOnly}}"/>
                            <RadioButton x:Name="AphakicCriteriaRadio" GroupName="MeasurementCriteriaRadioGroup" Content="Aphakic" Style="{StaticResource VPCenterRadioButtonStyle}" VerticalAlignment="Bottom" SnapsToDevicePixels="True"
                                 IsChecked="{Binding Path=Criteria, Converter={StaticResource EnumToBooleanConverter}, ConverterParameter={x:Static ModelBrowser:EyeInfoFilterCriteria.AphakicOnly}}"/>
                            <RadioButton x:Name="RecentMeasurementsCriteriaRadio" GroupName="MeasurementCriteriaRadioGroup" Content="Recently Performed" Style="{StaticResource VPRightRadioButtonStyle}" VerticalAlignment="Bottom" SnapsToDevicePixels="True"
                                 IsChecked="{Binding Path=Criteria, Converter={StaticResource EnumToBooleanConverter}, ConverterParameter={x:Static ModelBrowser:EyeInfoFilterCriteria.Recent}}"/>
                        </StackPanel>
                    </StackPanel>
                </Grid>
                <DataGrid x:Name="OdOsDataGrid" Margin="15,20,0,0" Grid.Row="1" HorizontalAlignment="Left"
                          IsReadOnly="True" AutoGenerateColumns="False" RowDetailsVisibilityMode="Visible"
                          HeadersVisibility="None" CanUserAddRows="False" GridLinesVisibility="None"
                          FocusVisualStyle="{x:Null}" Height="500" Width="830"
                          VirtualizingPanel.IsVirtualizing="True" 
                          VirtualizingPanel.IsContainerVirtualizable="True"
                          VirtualizingPanel.VirtualizationMode="Recycling"
                          VirtualizingPanel.CacheLength="2"
                          VirtualizingPanel.ScrollUnit="Pixel"
                          ScrollViewer.HorizontalScrollBarVisibility="Disabled"
                          ScrollViewer.VerticalScrollBarVisibility="Visible"
                          ScrollViewer.PanningMode="VerticalOnly"
                          PreviewMouseWheel="OdOsDataGrid_PreviewMouseWheel"
                          SelectedCellsChanged="DataGrid_SelectedCellsChanged" 
                          cmd:CommandBehavior.Event="PreviewMouseDoubleClick" 
                          cmd:CommandBehavior.Command="{Binding DataContext.EditModuleCommand, ElementName=OdOsDataGrid}"
                          PreviewKeyDown="OnPreviewKeyDownEvent"
                          Background="Transparent"
                          BorderThickness="0" Loaded="OdOsDataGrid_Loaded"
                          ItemsSource="{Binding OdOsInfoList}"
                          SelectionUnit="Cell" SelectionMode="Single"
                          AutomationProperties.AutomationId="PreviewListBox">
                    <DataGrid.Resources>
                        <Style TargetType="ScrollBar" BasedOn="{StaticResource ScrollBarStyle}" />
                    </DataGrid.Resources>

                    <DataGrid.Columns>
                        <DataGridTemplateColumn Header="OD">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <Border BorderThickness="0" Margin="-1">
                                        <LocalUiBrowser:EyeInfoThumbnail DataContext="{Binding OdEyeInfo}" IsSelected="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type DataGridCell}}, Mode=TwoWay,Path=IsSelected}"
                                                                 PatientsManager="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type DataGrid}}, Path=DataContext}"
                                                                 MouseEnter="EyeInfoThumbnail_MouseEnter" MouseLeave="EyeInfoThumbnail_MouseLeave"/>                                        
                                    </Border>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                        <DataGridTemplateColumn Header="OS">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <Border BorderThickness="0" Margin="-1">
                                        <LocalUiBrowser:EyeInfoThumbnail DataContext="{Binding OsEyeInfo}" IsSelected="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type DataGridCell}}, Path=IsSelected}"
                                                                 PatientsManager="{Binding RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type DataGrid}}, Path=DataContext}"
                                                                 MouseEnter="EyeInfoThumbnail_MouseEnter" MouseLeave="EyeInfoThumbnail_MouseLeave"/>
                                    </Border>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                    <DataGrid.RowStyle>
                        <Style TargetType="{x:Type DataGridRow}">
                        </Style>
                    </DataGrid.RowStyle>
                </DataGrid>

                <Grid>
                    <Grid Grid.Row="2"
                          Visibility="{Binding Path=EyeInfoFilter.EyeFlags, Converter={StaticResource EnumToVisibilityConverter}, ConverterParameter={x:Static ModelBrowser:EyeProcedureFlags.Plan}}">
                        <Grid Visibility="{Binding IsChecked, ElementName=ExportedRadio, Converter={StaticResource BoolToInvisibilityConverter}}" Margin="15,25,22,0">
                            <Border Visibility="{Binding IsSelectedEyeInfoDeletable, Converter={StaticResource BoolToInvisibilityConverter}}"
                                    PreviewMouseDown="NewPlanControl_PreviewMouseDown">
                                <Button Content="New Plan" Style="{StaticResource VPLightFlatButton}" Command="{Binding NewPlanCommand}"
                                        Visibility="{Binding Config.IsNewPlanAllowed, Converter={StaticResource BoolToVisibilityConverter}}" />
                            </Border>

                            <Grid Visibility="{Binding IsSelectedEyeInfoDeletable, Converter={StaticResource BoolToVisibilityConverter}}">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="5*" />
                                    <ColumnDefinition Width="15" />
                                    <ColumnDefinition Width="5*" />
                                </Grid.ColumnDefinitions>
                                <Grid PreviewMouseDown="NewPlanControl_PreviewMouseDown">
                                    <Button Content="New Plan" Command="{Binding NewPlanCommand}" Grid.Column="0" Style="{StaticResource VPLightFlatButton}"
                                            Visibility="{Binding Config.IsNewPlanAllowed, Converter={StaticResource BoolToVisibilityConverter}}"/>
                                </Grid>
                                <Button Content="Remove Plan" Click="DelEyeInfoBtn_Click" Grid.Column="2" Style="{StaticResource VPLightFlatButton}" />
                            </Grid>
                        </Grid>
                    </Grid>

                    <Grid Grid.Row="2" Visibility="{Binding Path=EyeInfoFilter.EyeFlags, Converter={StaticResource EnumToVisibilityConverter}, ConverterParameter={x:Static ModelBrowser:EyeProcedureFlags.AllSurgery}}">
                        <Grid Margin="15,25,22,0" PreviewMouseDown="DelEyeInfoGrid_PreviewMouseDown">
                            <Button Content="Remove Surgery" Click="DelEyeInfoBtn_Click" Grid.Column="1" Style="{StaticResource VPLightFlatButton}" 
                                    IsEnabled="{Binding IsSelectedEyeInfoDeletable}"/>
                        </Grid>
                    </Grid>

                    <Grid Grid.Row="2"
                          Visibility="{Binding Path=EyeInfoFilter.EyeFlags, Converter={StaticResource EnumToVisibilityConverter}, ConverterParameter={x:Static ModelBrowser:EyeProcedureFlags.AllDiagnostic}}">
                        <LocalUiBrowser:MeasurementToolbar/>
                    </Grid>
                </Grid>
            </StackPanel>
        </Border>

        <Viewbox Width="839" Grid.Row="0" Height="53" VerticalAlignment="Bottom">
            <StackPanel x:Name="EyeInfoFilterPanel"  Grid.Row="0" Margin="15,0,0,0" Orientation="Horizontal" HorizontalAlignment="Left"  MinWidth="839">
                <RadioButton GroupName="EyeFlagsRadioGroup" Name="MeasurementsBtn" Content="Measurements" Style="{StaticResource VPTabRadioBtnStyle}" Margin="0,0,0,-1"
                         IsChecked="{Binding Path=EyeFlags, Converter={StaticResource EnumToBooleanConverter}, ConverterParameter={x:Static ModelBrowser:EyeProcedureFlags.AllDiagnostic}}"/>
                <RadioButton GroupName="EyeFlagsRadioGroup" Name="PlanSurgeriesBtn" Content="Planned Surgeries" Style="{StaticResource VPTabRadioBtnStyle}" Margin="2,0,0,-1"
                         IsChecked="{Binding Path=EyeFlags, Converter={StaticResource EnumToBooleanConverter}, ConverterParameter={x:Static ModelBrowser:EyeProcedureFlags.Plan}}"/>
                <RadioButton GroupName="EyeFlagsRadioGroup" Name="CompletedSurgeriesBtn" Content="Completed Surgeries" Style="{StaticResource VPTabRadioBtnStyle}" Margin="2,0,0,-1"
                         IsChecked="{Binding Path=EyeFlags, Converter={StaticResource EnumToBooleanConverter}, ConverterParameter={x:Static ModelBrowser:EyeProcedureFlags.AllSurgery}}"/>

                <Button Name="DHSPlanningBtn" Content="SMARTCataract Planner" Style="{StaticResource VPButtonDisguisedAsTabStyle}" Margin="2,0,0,-1" Width="175"
                    Command="{Binding DataContext.EditModuleByBrowserCommand, ElementName=OdOsDataGrid}"/>
                <Button Name="ExternalPlanningBtn" Content="SMARTCataract DX Planner" Style="{StaticResource VPButtonDisguisedAsTabStyle}" Margin="2,0,0,-1" Width="195"
                    Command="{Binding DataContext.EditModuleExternalAppCommand, ElementName=OdOsDataGrid}"/>
                <Button Name="AdiPlanningBtn" Content="AdiPlanner" Style="{StaticResource VPButtonDisguisedAsTabStyle}" Margin="2,0,0,-1" Width="165"
                    Command="{Binding DataContext.EditModuleAdiPlannerCommand, ElementName=OdOsDataGrid}"/>
            </StackPanel>
        </Viewbox>
    </Grid>
</UserControl>
