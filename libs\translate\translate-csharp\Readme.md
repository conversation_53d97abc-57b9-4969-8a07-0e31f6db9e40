# translate-csharp

A powerful and flexible translation library for .NET applications, designed to handle multi-language support with ease. The library provides a singleton-based translation system that supports multiple languages, fallback mechanisms, and dynamic UI updates.

## Features

- Singleton-based translation system
- Support for multiple languages
- Automatic fallback to English when translations are missing
- Support for metadata-based translations
- Placeholder support in translations
- Dynamic UI updates when language changes
- XML-based translation file support
- Property change notifications for UI binding

## Building with CMake

The library is built using CMake. Follow these steps to build and use it:

1. **Clone the repository**:
```bash
git clone <repository-url>
```

2. **Configure and build**:
```bash
# Windows
cmake -B build -S .
cmake --build build
```

3. **Add reference** to your project:
   - After building, the library will be available in the build directory
   - Add the built assembly as a reference to your project

## Basic Usage

### Initialization

Initialize the Translator with your translation files:

```csharp
// In your App.xaml.cs
protected override void OnStartup(StartupEventArgs e)
{
    base.OnStartup(e);
    
    var translationFiles = new List<string>
    {
        Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Translations", "YourApp_es.xml"), // Spanish
        Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Translations", "YourApp_fr.xml"), // French
        // Add more language files as needed
    };
    
    // Initialize with files and set default language
    Translator.Instance.Initialize(translationFiles, "English");
}
```

### Basic Translation

```csharp
// Simple translation
string translatedText = Translator.Instance.Translate("textId", "English Text");

// Translation with metadata
string translatedText = Translator.Instance.TranslateM("id:textId|en:English Text|f:font14|w:170|h:25|cf:false|wr:false");

// Translation with placeholders
string translatedText = Translator.Instance.TranslateM(
    "id:textId|en:Hi %[String], you have %[Number] messages.",
    "Maria", "5"
);
```

### Language Switching

```csharp
// Switch language
Translator.Instance.CurrentUserLanguage = "Spanish";
Translator.Instance.UpdateUI(); // Triggers UI updates
```

## Translation File Format

Translation files should be in XML format with the following structure:

```xml
<LanguageTranslation Language="Spanish">
  <Groups>
    <Group Name="Group1">
      <Children>
        <LocalizedString Id="-1286148484" Text="Temporal_es" />
        <!-- More translations -->
      </Children>
    </Group>
  </Groups>
</LanguageTranslation>
```

## Metadata Format

The library supports metadata for translations with the following format:
```
id:<textId>|en:<englishText>|f:<font>|w:<width>|h:<height>|cf:<clipToFit>|wr:<wordWrap>
```

Supported metadata fields:
- `id`: Unique identifier for the text
- `en`: English text (used as fallback)
- `f`: Font name
- `w`: Width
- `h`: Height
- `cf`: Clip to fit (true/false)
- `wr`: Word wrap (true/false)

## Placeholder Support

The library supports placeholders in translations using the format `%[Type]` where Type can be:
- `String`: For string values
- `Number`: For numeric values
- `Date`: For date values

Example:
```csharp
// Translation with placeholders
string result = Translator.Instance.TranslateM(
    "id:1757935796|en:Hi %[String], you have %[Number] messages.",
    "Maria", "5"
);
// Result: "Hola Maria, tienes 5 mensajes." (in Spanish)
```

## Best Practices

1. **File Organization**
   - Keep translation files in a dedicated "Translations" folder
   - Use consistent naming: `YourApp_<languageCode>.xml`
   - Ensure files are copied to output directory

2. **Error Handling**
   - The library gracefully falls back to English when:
     - A translation is not found
     - A language is not loaded
     - Invalid metadata is provided

3. **Performance**
   - Initialize the Translator once at application startup
   - Cache frequently used translations if needed
   - Use metadata sparingly for performance-critical paths

## Attributes

The library provides custom attributes to simplify localization in your code:

### LocalizedDescriptionAttribute

A custom `DescriptionAttribute` that automatically handles translations for property descriptions. It uses the Translator API to get localized text and caches the results for better performance.

```csharp
// Usage in your class
public class MyClass
{
    [LocalizedDescription("56523235", "Temporal")]
    public string TemporalProperty { get; set; }
}
```

Features:
- Automatically translates descriptions based on the current language
- Falls back to English text when translation is not available
- Caches translations for better performance
- Automatically updates when language changes
- Supports all standard DescriptionAttribute functionality

The attribute works by:
1. Taking a text ID and English text in the constructor
2. Using the Translator API to get the localized text
3. Caching the translated value for subsequent calls
4. Automatically updating when the language changes through the Translator's PropertyChanged event

## Testing

The library includes comprehensive unit tests covering:
- Basic translation functionality
- Language switching
- Fallback mechanisms
- Metadata handling
- Placeholder formatting
- Error cases

## Dependencies

- .NET 4.8 or later
- System.ComponentModel (for INotifyPropertyChanged)
