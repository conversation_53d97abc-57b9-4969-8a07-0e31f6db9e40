cmake_minimum_required(VERSION 3.18)
include_guard(GLOBAL)
project(localizationlib_unit_tests VERSION 1.0 LANGUAGES CSharp)

include("${CMAKE_CURRENT_SOURCE_DIR}/../../../../../../cmake/Globals.cmake")

# Conditionally add test_lib if not already added
if (NOT TARGET localizationlib)
    add_subdirectory("${CMAKE_CURRENT_SOURCE_DIR}/../.." "localizationlib")
endif()

set(TARGET ${PROJECT_NAME})

file(GLOB_RECURSE FILE_LIST CONFIGURE_DEPENDS "${CMAKE_CURRENT_SOURCE_DIR}/src/*.cs")

add_library(${TARGET} SHARED ${FILE_LIST})

set_target_properties(${TARGET} PROPERTIES      
    DOTNET_SDK ${ROOT_DOTNET_SDK}
    DOTNET_TARGET_FRAMEWORK ${ROOT_DOTNET_TARGET_FRAMEWORK}  
    VS_PACKAGE_REFERENCES "${NUnit};${NUnit3TestAdapter};${Microsoft.NET.Test.Sdk};${Moq}"
)

target_link_libraries(${PROJECT_NAME} PRIVATE localizationlib)

