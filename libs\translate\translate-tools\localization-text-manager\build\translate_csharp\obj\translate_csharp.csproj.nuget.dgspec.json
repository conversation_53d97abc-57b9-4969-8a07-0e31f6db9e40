{"format": 1, "restore": {"D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-text-manager\\build\\translate_csharp\\translate_csharp.csproj": {}}, "projects": {"D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-text-manager\\build\\translate_csharp\\translate_csharp.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-text-manager\\build\\translate_csharp\\translate_csharp.csproj", "projectName": "translate_csharp", "projectPath": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-text-manager\\build\\translate_csharp\\translate_csharp.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-text-manager\\build\\translate_csharp\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "D:\\kibisoft\\workarea\\VisionPlanner\\NGSS_nugets": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.400/PortableRuntimeIdentifierGraph.json"}}}}}