^D:\KIBISOFT\WORKAREA\GIT\INTEROP\LIBS\TRANSLATE\TRANSLATE-TOOLS\LOCALIZATION-LIB\BUILD\CMAKEFILES\759900A77C69A7EAA19DEF58C0B30542\GENERATE.STAMP.RULE
setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SD:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-lib -BD:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-lib/build --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-lib/build/localizationlib.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
