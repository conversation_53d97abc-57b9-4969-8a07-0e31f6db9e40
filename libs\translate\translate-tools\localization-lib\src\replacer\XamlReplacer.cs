/*
 * Copyright 2021-present Alcon. All rights reserved.
 * Alcon proprietary/confidential.
 * Use is subject to license terms.
 */

using System;
using System.Data.Common;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;

namespace LocalizationLib;

public class XamlReplacer : IReplacer
{
    private readonly string _fileExtension = ".xaml";
    public string SupportedFileTypes => _fileExtension;
    FileOffsetManager _offsetManager = new FileOffsetManager();
    public ReplaceStatus Replace(string filePath, ExtractedTextInfo text)
    {
        string escapedText = Regex.Replace(text.OriginalText, @"\\(?![\\nrt\""])", @"\\") // Escape \ only if it's not followed by a known character (n, r, t, ")
                                .Replace("\"", "\\\"") // Escape double quotes
                                .Replace("\n", "\\n")  // Escape newlines (and other escape sequences if needed)
                                .Replace("\r", "\\r")
                                .Replace("\t", "\\t");
        Console.WriteLine($"\nProcessing string \"{escapedText}\" from {filePath} inside XamlReplacer ..");

        ReplaceStatus replaceStatus = ReplaceStatus.NotReplaced;
        if (!FirstPassValidation(filePath, text))
        {
            Console.WriteLine();
            return replaceStatus;
        }

        string translateCallParams = "";
        if (text.LangTextID == null)
        {
            translateCallParams = $"en:{escapedText.Replace("'", "\\&apos;")}";
        }
        else
        {
            translateCallParams = $"id:{text.LangTextID}|en:{escapedText.Replace("'", "\\&apos;")}";
        }

        string replacement = $"\"{{Binding Converter={{StaticResource TranslateConverter}}, ConverterParameter='{translateCallParams}'}}\"";

        // check special case Run Text which supports only oneWay mode
        string tempReplacemnt = GenerateReplacementForRunProperty(filePath, text, translateCallParams);
        if (tempReplacemnt != "notfound")
        {
            replacement = tempReplacemnt;
        }

        string updatedContent = ReplaceLineInsideXamlFile(filePath, replacement, escapedText, ref text, ref replaceStatus);
        if (replaceStatus == ReplaceStatus.Replaced)
        {
            updatedContent = AddTranslateConverterResource(filePath, updatedContent, text.LineNumber);
            // set updatedLineNumber/updatedColumn inside extracted text only if it is different than the initial
            var lineOffset = _offsetManager.LineOffset(filePath, text.LineNumber);
            var columnOffset = _offsetManager.ColumnOffset(filePath, text.LineNumber);
            text.UpdatedLineNumber = lineOffset != 0 ? text.LineNumber + lineOffset : 0;
            text.UpdatedColumn = columnOffset != 0 ? text.Column + columnOffset : 0;

            File.WriteAllText(filePath, updatedContent);
            Console.WriteLine($"File changed : {text.FilePath}\n");
        }
        return replaceStatus;
    }

    /// <summary>
    /// Replaces the originalValue with the replacement on the lineNumber
    /// Updates replaceStatus with Replaced, NotReplaced, AlreadyReplaced
    /// Adds notes inside text.Notes if the replacement couldn't be done
    /// </summary>
    /// <param name="filePath"></param>
    /// <param name="replacement"></param>
    /// <param name="text"></param>
    /// <param name="replaceStatus"></param>
    /// <returns>Updated file content</returns>
    private string ReplaceLineInsideXamlFile(string filePath, string replacement, string escapedText, ref ExtractedTextInfo text, ref ReplaceStatus replaceStatus)
    {
        string content = File.ReadAllText(filePath);
        var lines = content.Split("\n").ToList();
        var lineIndex = text.LineNumber + _offsetManager.LineOffset(filePath, text.LineNumber) - 1; // 0 based index
        var columnIndex = text.Column - 1; // 0 based index
        if (lineIndex < 0 || lineIndex >= lines.Count)
        {
            Console.WriteLine($"Invalid line number: {lineIndex}");
            replaceStatus = ReplaceStatus.NotReplaced;
            return content;
        }

        string lineText = lines[lineIndex];
        string toFind = $"\"{escapedText}\"";
        int idx = lineText.IndexOf(toFind);
        if (idx >= 0)
        {
            if (idx != columnIndex)
            {
                // adjust column with offset if existing
                columnIndex += _offsetManager.ColumnOffset(filePath, text.LineNumber);
                if (columnIndex >= 0 && columnIndex + toFind.Length < lineText.Length && lineText.Substring(columnIndex, toFind.Length) == toFind)
                {
                    idx = columnIndex;
                }
            }

            if (!ReplacementAlreadyPresent(idx, replacement, lineText))
            {
                lineText = lineText.Remove(idx, toFind.Length).Insert(idx, replacement);
                int i = idx;
                _offsetManager.ComputeColumnOffset(filePath, lineText, escapedText.Replace("'","\\&apos;"), text.LineNumber, columnIndex, idx);
                lines[lineIndex] = lineText;
                replaceStatus = ReplaceStatus.Replaced;
            }
            else
            {
                replaceStatus = ReplaceStatus.AlreadyReplaced;
                Console.WriteLine($"Replacement already present on line.");
            }
        }
        else
        {
            if (ReplacementAlreadyPresent(columnIndex, replacement, lineText))
            {
                replaceStatus = ReplaceStatus.AlreadyReplaced;
                Console.WriteLine("Replacement already present on line.");
            }
            else
            {
                string englishText = $"'en:{escapedText}'";
                var englishTextIdx = lineText.IndexOf(englishText);
                if (englishTextIdx >= 0 && ReplacementAlreadyPresent(englishTextIdx, replacement, lineText))
                {
                    replaceStatus = ReplaceStatus.AlreadyReplaced;
                    Console.WriteLine("Replacement already present on line.");
                }
                else
                {
                    replaceStatus = ReplaceStatus.NotReplaced;
                    text.Notes.Add($"Replacer: String {toFind} wasn't found on line number \"{lineIndex + 1}\".");
                    Console.WriteLine($"String {toFind} wasn't found on line number \"{lineIndex + 1}\". Notes updated!");
                }
            }
        }
        return string.Join('\n', lines);
    }

    /// <summary>
    /// Generate replacement for run property
    /// </summary>
    /// <param name="filePath"></param>
    /// <param name="text"></param>
    /// <param name="translateCallParams"></param>
    /// <returns>Replacement for run property</returns>
    private string GenerateReplacementForRunProperty(string filePath, ExtractedTextInfo text, string translateCallParams)
    {
        string content = File.ReadAllText(filePath);
        var lines = content.Split("\n").ToList();
        var lineIndex = text.LineNumber + _offsetManager.LineOffset(filePath, text.LineNumber) - 1; // 0 based index
        var columnIndex = text.Column - 1; // 0 based index
        if (lineIndex < 0 || lineIndex >= lines.Count)
        {
            Console.WriteLine($"Invalid line number: {lineIndex}");
            return "notfound";
        }

        string lineText = lines[lineIndex];
        string pattern = @"<Run Text=""([^""]*)""";
        Match match = Regex.Match(lineText, pattern);
        if (match.Success && match.Groups[1].Value == text.OriginalText)
        {
            return $"\"{{Binding Converter={{StaticResource TranslateConverter}}, ConverterParameter='{translateCallParams}', Mode=OneWay}}\"";
        }
        return "notfound";
    }

    /// <summary>
    /// Adds TranslateConverter resource in xaml file
    /// </summary>
    /// <param name="filePath"></param>
    /// <param name="xamlContent"></param>
    /// <returns>Updated file content</returns>
    private string AddTranslateConverterResource(string filePath, string xamlContent, int extractedTextLineNumber)
    {
        // Detect the root element (UserControl, Window, or ResourceDictionary).
        var rootMatch = Regex.Match(xamlContent, @"^<(?<root>(UserControl|Window|ResourceDictionary))\b", RegexOptions.Multiline);
        if (!rootMatch.Success)
        {
            Console.WriteLine($"Root not found inside xaml file");
            return xamlContent;
        }

        string rootName = rootMatch.Groups["root"].Value;

        // Insert the xmlns:trConv declaration if missing.
        if (!xamlContent.Contains("xmlns:trConv="))
        {
            var startTagRegex = new Regex($@"<({rootName}\b[^>]*?)>");
            var startTagMatch = startTagRegex.Match(xamlContent);
            if (startTagMatch.Success)
            {
                string oldTag = startTagMatch.Value;
                string newTag = oldTag.Replace(">", @"
        xmlns:trConv=""clr-namespace:Alcon.Interop.Translate;assembly=translate_csharp"">");
                xamlContent = xamlContent.Replace(oldTag, newTag);

                int endOfTagIndex = startTagMatch.Index + startTagMatch.Length;
                int lineNumber = xamlContent.Substring(0, endOfTagIndex).Count(c => c == '\n') + 1;
                _offsetManager.AddLineOffset(filePath, lineNumber, 1);
                Console.WriteLine($"Added trConv namespace to <{rootName}> start tag");
            }
        }

        // Check for the TranslateConverter resource declaration.
        bool hasConverter = Regex.IsMatch(
            xamlContent,
            @"<trConv:TranslateConverter\s+x:Key=""TranslateConverter"""
        );

        if (!hasConverter)
        {
            // Try to locate an existing Resources block.
            string resourcesOpenTag = $"{rootName}.Resources>";
            int idxResOpen = xamlContent.IndexOf(resourcesOpenTag, StringComparison.Ordinal);
            if (idxResOpen < 0)
            {
                // No Resources block found; create one.
                var startTagRegex = new Regex($@"<({rootName}\b[^>]*?)>");
                var startTagMatch = startTagRegex.Match(xamlContent);
                if (startTagMatch.Success)
                {
                    int insertPos = startTagMatch.Index + startTagMatch.Length;
                    int lineNumber = xamlContent.Substring(0, insertPos).Count(c => c == '\n') + 1;
                    string snippet = "";
                    if (rootName == "ResourceDictionary")
                    {
                        // on resourceDictionary add only trConv
                        snippet = $@"
        <trConv:TranslateConverter x:Key=""TranslateConverter""/>";
                        _offsetManager.AddLineOffset(filePath, lineNumber, 1);
                    }
                    else
                    {
                        snippet = $@"
    <{rootName}.Resources>
        <trConv:TranslateConverter x:Key=""TranslateConverter""/>
    </{rootName}.Resources>";
                        _offsetManager.AddLineOffset(filePath, lineNumber, 3);

                    }
                    xamlContent = xamlContent.Insert(insertPos, snippet);

                    if (rootName == "ResourceDictionary")
                    {
                        Console.WriteLine($"Created translate Converter for {rootName}");
                    }
                    else
                    {
                        Console.WriteLine($"Created <{rootName}.Resources> block with TranslateConverter");
                    }
                }
                else
                {
                    Console.WriteLine($"Could not find <{rootName}> start tag for resource insertion.");
                }
            }
            else
            {
                // Insert into an existing Resources block.
                string resourcesCloseTag = $"</{rootName}.Resources>";
                int idxResClose = xamlContent.IndexOf(resourcesCloseTag, idxResOpen, StringComparison.Ordinal);
                if (idxResClose >= 0)
                {
                    string line = $@"        <trConv:TranslateConverter x:Key=""TranslateConverter""/>
        ";
                    xamlContent = xamlContent.Insert(idxResClose, line);

                    int lineNumber = xamlContent.Substring(0, idxResClose).Count(c => c == '\n') + 1;
                    _offsetManager.AddLineOffset(filePath, lineNumber, 1);
                    Console.WriteLine("Inserted <trConv:TranslateConverter> into existing resources block.");
                }
                else
                {
                    Console.WriteLine($"Found <{rootName}.Resources> but no closing tag. Could not insert converter.");
                }
            }
        }
        else
        {
            Console.WriteLine("TranslateConverter resource already present; no insertion needed.");
        }

        return xamlContent;
    }

    /// <summary>
    /// Checks if replacement was already done for the originalText.
    /// </summary>
    /// <param name="originalTextIdx"></param>
    /// <param name="replacement"></param>
    /// <param name="lineText"></param>
    /// <returns>True if replacement is already on line for the original text, false otherwise</returns>
    private bool ReplacementAlreadyPresent(int originalTextIdx, string replacement, string lineText)
    {
        var replacementIdx = lineText.IndexOf(replacement);
        if (replacementIdx > 0 && replacementIdx + replacement.Length > originalTextIdx)
        {
            return true;
        }
        else
        {
            // translate binding can still appear with different converterParameter: containing "id:<id>|en:<text>, or just en:<text>
            int converterParamIdx = replacement.IndexOf("id:");
            if (converterParamIdx < 0)
            {
                converterParamIdx = replacement.IndexOf("en:");
            }
            if (converterParamIdx < 0)
            {
                return false;
            }
            string translateBinding = replacement.Substring(0, converterParamIdx);
            int translateBindingIdx = lineText.IndexOf(translateBinding);
            if (translateBindingIdx > 0 && translateBindingIdx == originalTextIdx)
            {
                return true;
            }
        }
        return false;
    }

    /// <summary>
    /// Validates
    /// 1. If filePath exists
    /// 2. Id the fileType can be handled
    /// </summary>
    /// <param name="filePath"></param>
    /// <param name="text"></param>
    /// <returns>True if the validation passes, false otherwise</returns>
    private bool FirstPassValidation(string filePath, ExtractedTextInfo text)
    {
        if (!Path.Exists(filePath))
        {
            Console.WriteLine($"FilePath {filePath} doesn't exists!");
            return false;
        }
        if (Path.GetExtension(filePath) != _fileExtension)
        {
            Console.WriteLine("Skipping file! Extension not handled inside CsReplacer!");
            return false;
        }
        return true;
    }
}