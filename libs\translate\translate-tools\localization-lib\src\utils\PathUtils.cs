using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;

namespace LocalizationLib
{
    public static class PathUtils
    {
        public static bool ShouldSkipPath(string path, List<string> skipPatterns)
        {
            if (skipPatterns == null || !skipPatterns.Any())
                return false;

            // Normalize the path to use backslashes
            string normalizedPath = path.Replace('/', '\\');

            return skipPatterns.Any(pattern =>
            {
                // Convert wildcard pattern to regex pattern
                string regexPattern = "^.*" + 
                    pattern.Replace("\\", "\\\\")  // Escape backslashes
                          .Replace("*", ".*")      // Convert wildcard to regex
                          .Replace("?", ".") +     // Convert single char wildcard
                    "$";
                
                return System.Text.RegularExpressions.Regex.IsMatch(normalizedPath, regexPattern, 
                    System.Text.RegularExpressions.RegexOptions.IgnoreCase);
            });
        }

        public static bool IsInIgnoredDirectory(string path)
        {
            return path.Contains($"{Path.DirectorySeparatorChar}obj{Path.DirectorySeparatorChar}") ||
                   path.Contains($"{Path.DirectorySeparatorChar}bin{Path.DirectorySeparatorChar}") ||
                   path.Contains($"{Path.DirectorySeparatorChar}CompilerIdCSharp{Path.DirectorySeparatorChar}");
        }
    }
} 