# Localization Extractor

## Overview
The **Localization Extractor** is a command-line tool that automates the process of discovering **user-facing text**. It can process **C#**, **C++**, and **XAML** (for WPF UI) files:

- **C#**: Detects literal strings in code (e.g., `Console.WriteLine("Save also the compare requests?")`).
- **C++**: Finds string literals in C++ source files (e.g., `std::cout << "Hello World";`).
- **XAML**: Finds hard-coded text in attributes like `Content="Cancel"`, `Text="Hello"`, etc. (only for C# projects).
- **Stores** extracted texts in `ProjectTextExtractorConfig.json`.
- **Generates a detailed report** of what was found.

The tool supports **multiple project types** (`cs`, `cpp`, or `all`), making it a flexible way to keep user-facing text centralized for localization.

## Features
- **Automatic detection of hard-coded text** in UI (XAML), C#, and C++ files.
- **Skips duplications** by checking whether the discovered text is already in the configuration.
- **Selective folder processing** with configurable skip patterns to exclude specific folders/subfolders.
- **LLM-powered verification** with caching support for efficient reuse of verification results.
- **Generates a single consolidated report** with the results of the extraction process.
- **Extensible** for new project types or new scanning approaches.
- **Multiple processing strategies**:
  - C#: Roslyn, Regex, or LLM-based approaches
  - C++: Regex or Consolidated (combines regex with LLM verification)
- **String interpolation handling** to convert C# string interpolations like `$"Hello {name}"` to `string.Format("Hello {0}", name)`.

## Installation

### Prerequisites
- .NET 6.0+ installed on your system
- (Optional for WPF) A WPF project containing `.xaml` files
- (Optional for backend scanning) `.cs` files for your business logic
- CMake (if you're building from source)

### Build (if applicable)
```bash
# Clone the repository (if needed)
git clone <repo-url>
cd localization-extractor

# Create a build directory
mkdir build && cd build

# Generate build files using CMake
cmake ..

# Build the project
cmake --build .
```

## Usage

### Command Syntax
```bash
localization_extractor.exe 
    -p <ProjectPath> 
    [-r <ReportFile>] 
    [-t <ProjectTypes>]
```

### Parameters

| Short | Long               | Required | Default         | Description                                                                                                                                  |
|-------|--------------------|----------|-----------------|----------------------------------------------------------------------------------------------------------------------------------------------|
| `-p`  | `--projectPath`    | **Yes**  | —               | Path to the folder containing files to scan (C#, C++, XAML).                                                                                 |
| `-r`  | `--reportFile`     | No       | `Report.txt`    | Path to the output report. Defaults to `Report.txt` in the tool's directory.                                                                 |
| `-t`  | `--projectTypes`   | No       | `cs`            | Which project types to process. Valid values: `cs` (C#), `cpp` (C++), or `all` (both C# and C++). Comma-separated values supported.          |
|       | `--csProcessor`    | No       | `consolidated`  | Which C# processor to use. Valid values: `regex`, `roslyn`, `consolidated` (default, combines both with LLM verification), or `llm`.         |
|       | `--cppProcessor`   | No       | `consolidated`  | Which C++ processor to use. Valid values: `regex` or `consolidated` (default, combines regex with LLM verification).                         |
|       | `--convertStringInterpolation` | No | `true`        | Convert C# string interpolations (`$"Hello {name}"`) to `string.Format` calls before extraction.                                            |
|       | `--convertStringConcatenation` | No | `true`        | Convert C# string concatenations (e.g. `"Hello " + name`) to `string.Format` calls before extraction.                                       |
|       | `--convertXamlTextToCodeBehind` | No | `false`       | Move non-dependency property text values from XAML to code-behind files (C# projects only). For root elements (Window, UserControl), properties like Title are moved directly to the constructor. |
| `-m`  | `--extractionMode` | No       | `RestructureAndExtract` | Extraction mode: `RestructureOnly` (only run code restructuring), `ExtractionOnly` (only extract, skip restructuring), or `RestructureAndExtract` (run both; default). |

### Examples

#### 1. Run both code restructuring and extraction (default)
```bash
localization_extractor.exe \
  -p "C:\Projects\MyWpfApp"
```

#### 2. Run only code restructuring (no extraction)
```bash
localization_extractor.exe \
  -p "C:\Projects\MyWpfApp" \
  -m RestructureOnly
```

#### 3. Run only extraction (skip restructuring)
```bash
localization_extractor.exe \
  -p "C:\Projects\MyWpfApp" \
  -m ExtractionOnly
```

#### 4. Disable a specific code restructuring step
```bash
localization_extractor.exe ^
  -p "C:\Projects\MyWpfApp" ^
  --convertStringConcatenation false ^
  --convertStringInterpolation false ^
  --convertXamlTextToCodeBehind false
```

- By default, all three restructuring options are enabled:
  - `--convertStringInterpolation` (converts `$"Hello {name}"` to `string.Format`)
  - `--convertStringConcatenation` (converts `"Hello " + name` to `string.Format`)
  - `--convertXamlTextToCodeBehind` (moves XAML text to code-behind)
- Use `false` to disable any option individually
- These options work in combination with the `-m` mode parameter:
  - `RestructureOnly`: Only run code restructuring
  - `ExtractionOnly`: Skip all restructuring
  - `RestructureAndExtract`: Run both (default)

#### 5. Processing C# project (default)
```bash
localization_extractor.exe ^
  -p "C:\Projects\MyCSharpApp" ^
  -t cs
```
- Scans all `.cs` and `.xaml` files (XAML is part of C# projects)
- Stores extracted texts in `ProjectTextExtractorConfig.json`
- Lists missing translation texts in `Report.txt`

#### 6. Processing C++ project
```bash
localization_extractor.exe ^
  -p "C:\Projects\MyCppApp" ^
  -t cpp
```
- Finds string literals in `.cpp`, `.h`, `.hpp` files
- Stores extracted texts in `ProjectTextExtractorConfig.json`
- Generates a final report indicating which files were processed

#### 7. Processing both C# and C++ projects
```bash
localization_extractor.exe ^
  -p "C:\Projects\MyMixedApp" ^
  -r "C:\Projects\MyMixedApp\Localization\AllReport.txt" ^
  -t all
```
- Scans both C# (`.cs`, `.xaml`) and C++ (`.cpp`, `.h`, `.hpp`) files
- Stores texts in `ProjectTextExtractorConfig.json`
- Produces a consolidated `AllReport.txt`

#### 8. Using specific processors for each project type
```bash
localization_extractor.exe ^
  -p "C:\Projects\MyMixedApp" ^
  -t "cs,cpp" ^
  --csProcessor roslyn ^
  --cppProcessor consolidated
```
- Uses Roslyn for C# files
- Uses consolidated processor (regex + LLM) for C++ files
- Processes both project types in a single run

#### 9. Regex-based scanning of `.cs`:
```bash
localization_extractor.exe ^
  -p "C:\Projects\MyBackend" ^
  -t cs ^
  --csProcessor regex
```
- Scans `.cs` files using a basic regex.

#### 10. Roslyn-based scanning of `.cs`:
```bash
localization_extractor.exe ^
  -p "C:\Projects\MyBackend" ^
  -t both ^
  --csProcessor roslyn
```
- Uses the more robust Roslyn approach for parsing C# files.
- Provides better accuracy for complex code structures.

#### 11. Consolidated scanning of `.cs` (default):
```bash
localization_extractor.exe ^
  -p "C:\Projects\MyBackend" ^
  -t cs ^
  --csProcessor consolidated
```
- Combines both Roslyn and Regex processors
- Uses LLM to verify each finding
- Caches verification results for reuse
- Requires `llmconfig.json` and running LLM server
- Falls back to non-LLM mode if server unavailable

#### 12. C++ Processing Options
```bash
# Using regex-based C++ processor
localization_extractor.exe ^
  -p "C:\Projects\MyCppApp" ^
  -t cpp ^
  --cppProcessor regex

# Using consolidated C++ processor (default)
localization_extractor.exe ^
  -p "C:\Projects\MyCppApp" ^
  -t cpp ^
  --cppProcessor consolidated
```
- Regex processor: Basic string literal detection
- Consolidated processor: Combines regex with LLM verification
- Both processors support all C++ string types (char*, wchar_t*, etc.)

#### 13. Using Skip Patterns to exclude specific folders:
```json
{
  "SkipPatterns": [
    "\\Apps\\InstallSetup\\*",
    "\\Apps\\ServerTools\\*",
    "\\TestApps\\*",
    "\\Docs\\*"
  ]
}
```
- Create or modify `ProjectTextExtractorConfig.json` in your project root
- Add `SkipPatterns` array with folder patterns to exclude
- Patterns support wildcards (`*`) and are case-insensitive
- All subfolders under matching patterns will be skipped
- Useful for excluding test apps, documentation, or other non-relevant folders

---

## How It Works

### 1. Processing Files

#### C#
- Searches for **hard-coded string literals**, e.g.:
  ```csharp
  Console.WriteLine("Hello World");
  var msg = "Save also the compare requests?";
  ```
- Records the location and context of each text for translation.

#### C++
- Finds string literals in C++ source files, e.g.:
  ```cpp
  std::cout << "Hello World";
  const char* msg = "Save the changes?";
  std::wstring title = L"Main Window";
  ```
- Supports various string types:
  - Basic strings (`char*`, `std::string`)
  - Wide strings (`wchar_t*`, `std::wstring`)
  - UTF-8 strings (`char8_t*`, `std::u8string`)
  - Raw string literals (`R"(text)"`)
- Records location, string type, and context for translation.

#### XAML (C# projects only)
- Finds attributes like `Content="Cancel"`, `Text="Hello"`, `Header="MyHeader"`, `ToolTip="Hover"`, `Title="Main Window"`.
- Records the location and context of each text for translation.
- When `--convertXamlTextToCodeBehind` is enabled:
  - Moves non-dependency property text values to code-behind files
  - For root elements (Window, UserControl), properties like Title are moved directly to the constructor
  - Example:
    ```xaml
    <!-- Before -->
    <Window Title="Main Window">
        <Button Content="Click Me" />
    </Window>

    <!-- After -->
    <Window>
        <Button Content="Click Me" />
    </Window>
    ```
    ```csharp
    // Code-behind
    public MainWindow()
    {
        InitializeComponent();
        Title = "Main Window";
    }
    ```

### 2. Updating the Configuration
- Loads (or creates) the `ProjectTextExtractorConfig.json`.
- For each **new** text not yet present, adds an entry with a unique `id`, `EnglishText`, and other metadata.
- Skips duplicates automatically.
- Respects `SkipPatterns` configuration to exclude specified folders.
- When using the **consolidated** processor:
  - Combines results from both Roslyn and Regex processors
  - Removes duplicate findings based on file, line, and column
  - If LLM verification is enabled:
    - Verifies each finding to ensure it's truly user-facing
    - Caches verification results for future use
    - Reuses cached results in subsequent runs
    - Provides detailed reasoning in the report
    - Skips any text that the LLM determines doesn't need translation

### 3. Generating a Single Report
Regardless of which project types you choose (`cs`, `cpp`, or `all`), the tool outputs **one** consolidated report, listing:

- Each file scanned and whether it was **processed**.
- All **missing translation** texts found.
- Any **notes** (e.g. if it had to skip certain lines).

#### Example
```
=== Localization Tool Report ===
Date: 1/1/2025 5:14:32 PM
Files Processed: 4

File: MainWindow.xaml  [PROCESSED]
   - Content: "Cancel" (Line 12, Column 18)
   - Text: "Hello World" (Line 15, Column 22)

File: Program.cs  [PROCESSED]
   - (string literal): "Log in to system?" (Line 20, Column 23)
   - (string interpolation): $"Hello {name}" (Line 25, Column 15)

Total texts found: 4
```

---

## Summary
The **Localization Extractor** provides a **unified** approach for discovering user-facing text in **C#**, **C++**, and **XAML** files:
- **Scan** your code for hard-coded user-facing text
- **Store** the findings in `ProjectTextExtractorConfig.json`
- **Generate** a detailed report of what needs translation
- **Scale** to any combination of project types with the `--projectTypes` parameter
- **Convert** string interpolations and concatenations to standard `string.Format` calls (C# only)

Use this tool to identify what needs translation in your codebase. With support for both C# and C++ projects, you can:
- Process C# projects with XAML UI
- Process C++ projects with various string types
- Process mixed projects containing both C# and C++ code
- Choose appropriate processors for each project type
- Balance between simple regex-based processing and more thorough LLM-verified approaches

## LLM Setup and Configuration

### Setting up LLM Server
The tool supports LLM-based verification through any OpenAI-compatible API server. You can use tools like LMStudio which provide OpenAI-compatible endpoints:

1. Install and set up an LLM server (e.g., LMStudio)
2. Configure it to expose an OpenAI-compatible API endpoint
3. Create `llmconfig.json` in one of these locations (in order of precedence):
   - In the same directory as the extractor executable (`localization_extractor.exe`)
   - Two levels up from the assembly location in the `etc` folder (`../../etc/llmconfig.json`)

The tool will automatically search for the config file in these locations. If not found, LLM-based features will be disabled.

Example `llmconfig.json`:
```json
{
    "llmProvider": "LocalLlmProvider",
    "promptTemplate": "...",  // Custom prompt for text analysis
    "localLlmProviderConfig": {
        "serviceUrl": "http://your-llm-server:1234",
        "modelId": "your-model-name",
        "temperature": 0.0,
        "maxRetries": 3,
        "timeoutSeconds": 1000,
        "useChunking": false,
        "chunkSize": 300
    }
}
```

### Verification Results Caching
The tool automatically caches LLM verification results in `ProjectTextExtractorConfig.json`:

- Results are stored in the `VerificationResults` dictionary
- Each result includes the text, context, and verification decision
- Cached results are reused in subsequent runs
- Moving/copying the config file preserves all verification results

This means:
1. First run with LLM verification may take longer
2. Subsequent runs will be faster by reusing cached results
3. You can share verification results by sharing the config file
4. Different team members can reuse the same verifications

### Using LLM Features

#### Consolidated Mode (Default)
```bash
localization_extractor.exe ^
  -p "C:\Projects\MyBackend" ^
  -t cs ^
  --csProcessor consolidated
```
- Combines Roslyn and Regex processors
- Uses LLM to verify each finding
- Caches verification results for reuse
- Requires `llmconfig.json` and running LLM server
- Falls back to non-LLM mode if server unavailable

#### LLM-Only Mode
```bash
localization_extractor.exe ^
  -p "C:\MyProject" ^
  -t cs ^
  --csProcessor llm
```
- Uses only LLM for text discovery
- Most thorough but slowest approach
- Requires `llmconfig.json` and running LLM server
- Benefits from verification result caching
- Best for initial project setup