enable_language(CSharp)

set(TARGET ${PROJECT_NAME})
list(APPEND ${PROJECT_NAME}_SOURCES
    "${CMAKE_CURRENT_SOURCE_DIR}/src/Converters/TranslateConverter.cs")

add_library(${TARGET} SHARED "${${PROJECT_NAME}_SOURCES}")

set_target_properties(${TARGET} PROPERTIES
    DOTNET_SDK "${ROOT_DOTNET_SDK}"
    DOTNET_TARGET_FRAMEWORK "${ROOT_DOTNET_TARGET_FRAMEWORK_WINDOWS}"
    VS_GLOBAL_UseWPF "true"    
    VS_PACKAGE_REFERENCES "NETSDK1004 workaround" 
)