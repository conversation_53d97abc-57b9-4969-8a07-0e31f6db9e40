/*
 * Copyright 2021-present Alcon. All rights reserved.
 * Alcon proprietary/confidential.
 * Use is subject to license terms.
 */

using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using LocalizationLib.Llm;
using System.Text;

namespace LocalizationLib;

public class ConsolidatedCsProcessor : IExtractor
{
    private readonly IExtractor _roslynExtractor;
    private readonly IExtractor _regexExtractor;
    private readonly ILlmClient _llmClient;
    private readonly bool _useLlmVerification;
    private readonly string _projectPath;
    private readonly LlmCacheManager _llmCacheManager;

    // Config for file chunking during LLM verification
    private readonly int _contextLines = 50; // Default context size
    private readonly bool _useChunking = false;
    private readonly LLMConfig _llmConfig;

    public ConsolidatedCsProcessor(string projectPath = null, ILlmClient llmClient = null)
    {
        _roslynExtractor = new CsExtractor(csProcessorOption:"roslyn");
        _regexExtractor = new CsExtractor(csProcessorOption: "regex");
        _llmClient = llmClient;
        _useLlmVerification = llmClient != null;
        _projectPath = projectPath;
        _llmCacheManager = new LlmCacheManager(projectPath ?? ".");
        
        // Get config from the LLM client if available
        if (_llmClient != null)
        {
            _llmConfig = _llmClient.GetConfig();
            
            // Initialize chunking parameters from config
            if (_llmConfig?.localLlmProviderConfig != null)
            {
                if (_llmConfig.localLlmProviderConfig.chunkSize > 0)
                {
                    // Use chunkSize for contextLines - divide by 2 since we use context before and after
                    _contextLines = _llmConfig.localLlmProviderConfig.chunkSize / 2;
                }
                
                _useChunking = _llmConfig.localLlmProviderConfig.useChunking;
            }
        }
    }

    private bool ValidateLocation(ReplacementRecord record)
    {
        try
        {
            var fileContent = GetFileContent(record.FilePath);
            if (string.IsNullOrEmpty(fileContent))
                return false;

            var lines = fileContent.Split('\n');
            if (record.LineNumber <= 0 || record.LineNumber > lines.Length)
            {
                Console.WriteLine($"Warning: Invalid line number {record.LineNumber} for file {record.FilePath}");
                return false;
            }

            // If the text contains newlines, handle it as a multi-line string first
            if (record.OriginalValue.Contains('\n'))
            {
                return ValidateMultiLineLocation(record, lines);
            }

            var line = lines[record.LineNumber - 1];
            
            // Set LineContent if not already set
            if (string.IsNullOrEmpty(record.LineContent))
            {
                record.LineContent = line;
            }
            
            // First try to find the text in the line, handling escaped characters
            var textIndex = -1;
            var searchText = record.OriginalValue;
            var lineText = line;

            // Try different variations of the text
            if (searchText.Contains("\\"))
            {
                // Try with single backslashes first
                textIndex = lineText.IndexOf(searchText);
                if (textIndex == -1)
                {
                    // Try with double backslashes (escaped format)
                    var escapedText = searchText.Replace("\\", "\\\\");
                    textIndex = lineText.IndexOf(escapedText);
                }
            }
            else
            {
                textIndex = lineText.IndexOf(searchText);
            }

            if (textIndex == -1)
            {
                Console.WriteLine($"Warning: Could not find text \"{record.OriginalValue}\" in line {record.LineNumber} of {record.FilePath}");
                return false;
            }

            // Look for the opening quote before the text
            var beforeText = line.Substring(0, textIndex);
            var lastQuoteIndex = beforeText.LastIndexOf('"');
            
            if (lastQuoteIndex == -1)
            {
                Console.WriteLine($"Warning: Could not find opening quote for text \"{record.OriginalValue}\" in line {record.LineNumber} of {record.FilePath}");
                return false;
            }

            // Update the column to point to the opening quote
            var newColumn = lastQuoteIndex + 1; // Convert to 1-based column
            if (newColumn != record.Column)
            {
                Console.WriteLine($"Info: Column position adjusted from {record.Column} to {newColumn} (opening quote) for text \"{record.OriginalValue}\" in {record.FilePath}:{record.LineNumber}");
                record.Column = newColumn;
                record.Notes.Add($"Column position adjusted to opening quote position: {newColumn}");
            }

            return true;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Warning: Could not validate location for {record.FilePath}:{record.LineNumber}:{ex.Message}");
            return false;
        }
    }

    private bool ValidateMultiLineLocation(ReplacementRecord record, string[] lines)
    {
        try
        {
            // Find the line containing the first part of the text
            var firstLineText = record.OriginalValue.TrimStart('\n').Split('\n')[0];
            var startLineIndex = -1;
            var startColumnIndex = -1;

            // Search from the current line number backwards first
            for (int i = record.LineNumber - 1; i >= 0; i--)
            {
                var ln = lines[i];
                var index = ln.IndexOf(firstLineText);
                if (index != -1)
                {
                    startLineIndex = i;
                    startColumnIndex = index;
                    break;
                }
            }

            // If not found, search forward
            if (startLineIndex == -1)
            {
                for (int i = record.LineNumber - 1; i < lines.Length; i++)
                {
                    var ln = lines[i];
                    var index = ln.IndexOf(firstLineText);
                    if (index != -1)
                    {
                        startLineIndex = i;
                        startColumnIndex = index;
                        break;
                    }
                }
            }

            if (startLineIndex == -1)
            {
                Console.WriteLine($"Warning: Could not find start of multi-line text \"{record.OriginalValue}\" in file {record.FilePath}");
                return false;
            }

            // Look for the opening quote before the text
            var line = lines[startLineIndex];
            var beforeText = line.Substring(0, startColumnIndex);
            var lastQuoteIndex = beforeText.LastIndexOf('"');
            
            if (lastQuoteIndex == -1)
            {
                Console.WriteLine($"Warning: Could not find opening quote for multi-line text \"{record.OriginalValue}\" in line {startLineIndex + 1} of {record.FilePath}");
                return false;
            }

            // Update the record with the correct line and column
            var newColumn = lastQuoteIndex + 1; // Convert to 1-based column
            if (startLineIndex + 1 != record.LineNumber || newColumn != record.Column)
            {
                Console.WriteLine($"Info: Location adjusted for multi-line text \"{record.OriginalValue}\" in {record.FilePath}");
                Console.WriteLine($"      From: Line {record.LineNumber}, Column {record.Column}");
                Console.WriteLine($"      To: Line {startLineIndex + 1}, Column {newColumn}");
                
                record.LineNumber = startLineIndex + 1;
                record.Column = newColumn;
                record.Notes.Add($"Location adjusted for multi-line text: Line {startLineIndex + 1}, Column {newColumn}");
            }

            // Set LineContent to include all lines of the multi-line string
            if (string.IsNullOrEmpty(record.LineContent))
            {
                var multiLineContent = new StringBuilder();
                var textLines = record.OriginalValue.Split('\n');
                var currentLineIndex = startLineIndex;
                
                for (int i = 0; i < textLines.Length; i++)
                {
                    if (currentLineIndex >= lines.Length)
                        break;
                        
                    var currentLine = lines[currentLineIndex];
                    if (i == 0)
                    {
                        // For the first line, only include from the opening quote
                        multiLineContent.Append(currentLine.Substring(lastQuoteIndex));
                    }
                    else
                    {
                        multiLineContent.Append('\n').Append(currentLine);
                    }
                    currentLineIndex++;
                }
                
                record.LineContent = multiLineContent.ToString();
            }

            return true;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Warning: Could not validate multi-line location for {record.FilePath}:{record.LineNumber}:{ex.Message}");
            return false;
        }
    }

    public (List<FileProcessingResult> fileResults, List<ReplacementRecord> replacements) Extract(string projectPath)
    {
        Console.WriteLine("Starting consolidated CS processing...");
        
        // 1. Run both processors
        Console.WriteLine("Running Roslyn processor...");
        var (roslynFileResults, roslynReplacements) = _roslynExtractor.Extract(projectPath);
        
        Console.WriteLine("Running Regex processor...");
        var (regexFileResults, regexReplacements) = _regexExtractor.Extract(projectPath);

        // 2. Consolidate results, removing duplicates
        var consolidatedResults = ConsolidateResults(roslynFileResults, regexFileResults);
        var consolidatedReplacements = ConsolidateReplacements(roslynReplacements, regexReplacements);

        // 3. If LLM verification is available, verify each replacement
        if (_useLlmVerification)
        {
            Console.WriteLine("Starting LLM verification of consolidated results...");
            VerifyReplacementsWithLlm(consolidatedReplacements);
        }

        return (consolidatedResults, consolidatedReplacements);
    }

    private List<FileProcessingResult> ConsolidateResults(
        List<FileProcessingResult> roslynResults,
        List<FileProcessingResult> regexResults)
    {
        var consolidated = new List<FileProcessingResult>();
        var processedFiles = new HashSet<string>();

        foreach (var result in roslynResults.Concat(regexResults))
        {
            if (!processedFiles.Contains(result.FilePath))
            {
                processedFiles.Add(result.FilePath);
                consolidated.Add(result);
            }
            else
            {
                // Merge results for the same file
                var existing = consolidated.First(r => r.FilePath == result.FilePath);
                existing.Replacements.AddRange(result.Replacements);
                existing.Notes.AddRange(result.Notes);
                existing.Changed |= result.Changed;
            }
        }

        return consolidated;
    }

    private List<ReplacementRecord> ConsolidateReplacements(
        List<ReplacementRecord> roslynReplacements,
        List<ReplacementRecord> regexReplacements)
    {
        var consolidated = new List<ReplacementRecord>();
        var processedKeys = new HashSet<string>();

        foreach (var replacement in roslynReplacements.Concat(regexReplacements))
        {
            var key = $"{replacement.FilePath}:{replacement.LineNumber}:{replacement.Column}";
            if (!processedKeys.Contains(key))
            {
                processedKeys.Add(key);
                consolidated.Add(replacement);
            }
        }

        return consolidated;
    }

    private void VerifyReplacementsWithLlm(List<ReplacementRecord> replacements)
    {
        // Use the singleton instance
        var configManager = ProjectTextExtractorConfigManager.Instance;
        
        Console.WriteLine($"Verifying {replacements.Count} replacements with LLM...");
        
        foreach (var replacement in replacements.ToList())
        {
            try
            {
                // Validate location first to ensure correct column position
                if (!ValidateLocation(replacement))
                {
                    Console.WriteLine($"\nSkipping verification for text: \"{replacement.OriginalValue}\"");
                    Console.WriteLine($"Location validation failed: {replacement.FilePath}:{replacement.LineNumber}");
                    replacement.Notes.Add("Skipped verification: Location validation failed");
                    replacement.Notes.Add("Marked for removal");
                    continue;
                }

                // Check if we have cached results from the LLM cache (after location validation)
                var cachedResult = _llmCacheManager.GetVerificationResult(
                    replacement.OriginalValue,
                    replacement.FilePath,
                    replacement.LineNumber,
                    replacement.Column,
                    _llmClient,
                    _llmConfig);

                if (cachedResult != null)
                {
                    Console.WriteLine($"\nUsing cached verification for text: \"{replacement.OriginalValue}\"");
                    Console.WriteLine($"Location: {replacement.FilePath}:{replacement.LineNumber}");
                    Console.WriteLine($"Cached Decision: {(cachedResult.NeedsTranslation ? "Needs Translation" : "Skip")}");
                    Console.WriteLine($"Cached Reason: {cachedResult.Reason}");

                    // Add cached verification details to notes
                    replacement.Notes.Add($"LLM Verification (Cached): {(cachedResult.NeedsTranslation ? "Needs Translation" : "Skip")}");
                    replacement.Notes.Add($"LLM Reason (Cached): {cachedResult.Reason}");
                    replacement.Notes.Add($"LLM Context (Cached): {cachedResult.Context}");
                    replacement.NeedsTranslation = cachedResult.NeedsTranslation;
                    continue;
                }

                Console.WriteLine($"\nVerifying text: \"{replacement.OriginalValue}\"");
                Console.WriteLine($"Location: {replacement.FilePath}:{replacement.LineNumber}");
                
                // Get only relevant portion of file content instead of the entire file
                // to avoid exceeding token limits for large files
                var verificationResult = _llmClient.VerifyTranslationNeeded(
                    text: replacement.OriginalValue,
                    lineNumber: replacement.LineNumber,
                    lineContent: replacement.LineContent,
                    fileContent: GetRelevantFileContext(replacement.FilePath, replacement.LineNumber),
                    groupId: replacement.GroupId,
                    filePath: replacement.FilePath
                );

                Console.WriteLine($"LLM Decision: {(verificationResult.NeedsTranslation ? "Needs Translation" : "Skip")}");
                Console.WriteLine($"Reason: {verificationResult.Reason}");
                Console.WriteLine($"Context: {verificationResult.Context}");

                // Store the verification result in the LLM cache
                _llmCacheManager.AddVerificationResult(
                    replacement.OriginalValue,
                    replacement.FilePath,
                    replacement.LineNumber,
                    replacement.Column,
                    verificationResult.NeedsTranslation,
                    verificationResult.Reason,
                    verificationResult.Context,
                    replacement.Source,
                    _llmClient,
                    _llmConfig,
                    replacement.LineContent
                );

                // Add verification details to notes
                replacement.Notes.Add($"LLM Verification: {(verificationResult.NeedsTranslation ? "Needs Translation" : "Skip")}");
                replacement.Notes.Add($"LLM Reason: {verificationResult.Reason}");
                replacement.Notes.Add($"LLM Context: {verificationResult.Context}");
                replacement.NeedsTranslation = verificationResult.NeedsTranslation;

            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error during LLM verification: {ex.Message}");
                replacement.Notes.Add($"LLM Verification Error: {ex.Message}");
            }
        }

        // Remove replacements that were marked for removal
        replacements.RemoveAll(r => r.Notes.Any(n => n.Contains("Marked for removal")));
        Console.WriteLine($"LLM verification complete. {replacements.Count} replacements remaining.");
    }

    private string GetFileContent(string filePath)
    {
        try
        {
            return System.IO.File.ReadAllText(filePath);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Warning: Could not read file content for {filePath}: {ex.Message}");
            return string.Empty;
        }
    }

    /// <summary>
    /// Extracts a relevant portion of the file content around the specified line number
    /// to avoid sending the entire file to the LLM and exceeding token limits.
    /// Uses the LLMConfig settings for chunking when available.
    /// </summary>
    /// <param name="filePath">Path to the file</param>
    /// <param name="lineNumber">Target line number to focus on</param>
    /// <returns>A relevant portion of the file with the target line and surrounding context</returns>
    private string GetRelevantFileContext(string filePath, int lineNumber)
    {
        try
        {
            string[] allLines = System.IO.File.ReadAllLines(filePath);
            if (allLines.Length == 0)
            {
                return string.Empty;
            }

            // If chunking is disabled or the file is small enough, return the whole file
            if (!_useChunking || allLines.Length <= _contextLines * 2 + 1)
            {
                return string.Join("\n", allLines);
            }

            // Calculate the start and end lines to extract
            int startLine = Math.Max(0, lineNumber - _contextLines);
            int endLine = Math.Min(allLines.Length - 1, lineNumber + _contextLines);

            // Extract the relevant lines
            var relevantLines = new List<string>();
            
            // Add a header to indicate this is a file excerpt
            relevantLines.Add($"// File excerpt from {Path.GetFileName(filePath)} - Lines {startLine+1} to {endLine+1}");
            relevantLines.Add($"// The line containing the text to validate is at line {lineNumber+1} in the original file");
            relevantLines.Add("");
            
            // Add the relevant code lines
            for (int i = startLine; i <= endLine; i++)
            {
                relevantLines.Add(allLines[i]);
            }

            return string.Join("\n", relevantLines);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Warning: Could not extract relevant context for {filePath}: {ex.Message}");
            return string.Empty;
        }
    }
}