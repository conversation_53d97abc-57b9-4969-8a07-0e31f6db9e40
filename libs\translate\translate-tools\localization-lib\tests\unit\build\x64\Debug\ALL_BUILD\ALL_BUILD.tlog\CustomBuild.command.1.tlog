^D:\KIBISOFT\WORKAREA\GIT\INTEROP\LIBS\TRANSLATE\TRANSLATE-TOOLS\LOCALIZATION-LIB\TESTS\UNIT\CMAKELISTS.TXT
setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SD:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-lib/tests/unit -BD:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-lib/tests/unit/build --check-stamp-file D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-lib/tests/unit/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
