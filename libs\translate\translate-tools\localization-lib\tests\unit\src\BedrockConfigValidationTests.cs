/*
 * Copyright 2021-present Alcon. All rights reserved.
 * Alcon proprietary/confidential.
 * Use is subject to license terms.
 */

using NUnit.Framework;
using System;
using LocalizationLib;

namespace LocalizationLib.Tests
{
    [TestFixture]
    public class BedrockConfigValidationTests
    {
        private LLMConfig CreateValidBedrockConfig()
        {
            return new LLMConfig
            {
                llmProvider = "BedrockProvider",
                bedrockProviderConfig = new BedrockProviderConfig
                {
                    region = "us-east-1",
                    modelId = "anthropic.claude-3-sonnet-20240229-v1:0",
                    temperature = 0.0,
                    maxRetries = 3,
                    timeoutSeconds = 120,
                    chunkSize = 3000,
                    maxTokens = 4096,
                    accessKey = "AKIAIOSFODNN7EXAMPLE",
                    secretKey = "wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY"
                }
            };
        }

        [Test]
        public void CreateLlmClient_WithValidBedrockConfig_Succeeds()
        {
            // Arrange
            var config = CreateValidBedrockConfig();

            // Act & Assert
            Assert.DoesNotThrow(() => LlmClientFactory.CreateLlmClient(config));
        }

        [Test]
        public void CreateLlmClient_WithMissingRegion_ThrowsInvalidOperationException()
        {
            // Arrange
            var config = CreateValidBedrockConfig();
            config.bedrockProviderConfig.region = "";

            // Act & Assert
            var ex = Assert.Throws<InvalidOperationException>(() => LlmClientFactory.CreateLlmClient(config));
            Assert.That(ex.Message, Contains.Substring("region is required"));
        }

        [Test]
        public void CreateLlmClient_WithMissingModelId_ThrowsInvalidOperationException()
        {
            // Arrange
            var config = CreateValidBedrockConfig();
            config.bedrockProviderConfig.modelId = "";

            // Act & Assert
            var ex = Assert.Throws<InvalidOperationException>(() => LlmClientFactory.CreateLlmClient(config));
            Assert.That(ex.Message, Contains.Substring("modelId is required"));
        }

        [Test]
        public void CreateLlmClient_WithInvalidTimeoutSeconds_ThrowsInvalidOperationException()
        {
            // Arrange
            var config = CreateValidBedrockConfig();
            config.bedrockProviderConfig.timeoutSeconds = 0;

            // Act & Assert
            var ex = Assert.Throws<InvalidOperationException>(() => LlmClientFactory.CreateLlmClient(config));
            Assert.That(ex.Message, Contains.Substring("timeoutSeconds must be greater than 0"));
        }

        [Test]
        public void CreateLlmClient_WithNegativeMaxRetries_ThrowsInvalidOperationException()
        {
            // Arrange
            var config = CreateValidBedrockConfig();
            config.bedrockProviderConfig.maxRetries = -1;

            // Act & Assert
            var ex = Assert.Throws<InvalidOperationException>(() => LlmClientFactory.CreateLlmClient(config));
            Assert.That(ex.Message, Contains.Substring("maxRetries must be greater than or equal to 0"));
        }

        [Test]
        public void CreateLlmClient_WithInvalidChunkSize_ThrowsInvalidOperationException()
        {
            // Arrange
            var config = CreateValidBedrockConfig();
            config.bedrockProviderConfig.chunkSize = 0;

            // Act & Assert
            var ex = Assert.Throws<InvalidOperationException>(() => LlmClientFactory.CreateLlmClient(config));
            Assert.That(ex.Message, Contains.Substring("chunkSize must be greater than 0"));
        }

        [Test]
        public void CreateLlmClient_WithInvalidMaxTokens_ThrowsInvalidOperationException()
        {
            // Arrange
            var config = CreateValidBedrockConfig();
            config.bedrockProviderConfig.maxTokens = 0;

            // Act & Assert
            var ex = Assert.Throws<InvalidOperationException>(() => LlmClientFactory.CreateLlmClient(config));
            Assert.That(ex.Message, Contains.Substring("maxTokens must be greater than 0"));
        }

        [Test]
        public void CreateLlmClient_WithInvalidTemperatureTooLow_ThrowsInvalidOperationException()
        {
            // Arrange
            var config = CreateValidBedrockConfig();
            config.bedrockProviderConfig.temperature = -0.1;

            // Act & Assert
            var ex = Assert.Throws<InvalidOperationException>(() => LlmClientFactory.CreateLlmClient(config));
            Assert.That(ex.Message, Contains.Substring("temperature must be between 0.0 and 1.0"));
        }

        [Test]
        public void CreateLlmClient_WithInvalidTemperatureTooHigh_ThrowsInvalidOperationException()
        {
            // Arrange
            var config = CreateValidBedrockConfig();
            config.bedrockProviderConfig.temperature = 1.1;

            // Act & Assert
            var ex = Assert.Throws<InvalidOperationException>(() => LlmClientFactory.CreateLlmClient(config));
            Assert.That(ex.Message, Contains.Substring("temperature must be between 0.0 and 1.0"));
        }

        [Test]
        public void CreateLlmClient_WithInvalidRegionFormat_ThrowsInvalidOperationException()
        {
            // Arrange
            var config = CreateValidBedrockConfig();
            config.bedrockProviderConfig.region = "invalid-region";

            // Act & Assert
            var ex = Assert.Throws<InvalidOperationException>(() => LlmClientFactory.CreateLlmClient(config));
            Assert.That(ex.Message, Contains.Substring("Invalid AWS region format"));
        }

        [Test]
        public void CreateLlmClient_WithValidRegionFormats_Succeeds()
        {
            // Arrange
            var config = CreateValidBedrockConfig();
            var validRegions = new[] { "us-east-1", "eu-west-1", "ap-southeast-2", "ca-central-1" };

            // Act & Assert
            foreach (var region in validRegions)
            {
                config.bedrockProviderConfig.region = region;
                Assert.DoesNotThrow(() => LlmClientFactory.CreateLlmClient(config), $"Region {region} should be valid");
            }
        }

        [Test]
        public void CreateLlmClient_WithAccessKeyButNoSecretKey_ThrowsInvalidOperationException()
        {
            // Arrange
            var config = CreateValidBedrockConfig();
            config.bedrockProviderConfig.accessKey = "AKIAIOSFODNN7EXAMPLE";
            config.bedrockProviderConfig.secretKey = "";

            // Act & Assert
            var ex = Assert.Throws<InvalidOperationException>(() => LlmClientFactory.CreateLlmClient(config));
            Assert.That(ex.Message, Contains.Substring("AWS credentials are required for Bedrock REST API access"));
        }

        [Test]
        public void CreateLlmClient_WithSecretKeyButNoAccessKey_ThrowsInvalidOperationException()
        {
            // Arrange
            var config = CreateValidBedrockConfig();
            config.bedrockProviderConfig.accessKey = "";
            config.bedrockProviderConfig.secretKey = "wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY";

            // Act & Assert
            var ex = Assert.Throws<InvalidOperationException>(() => LlmClientFactory.CreateLlmClient(config));
            Assert.That(ex.Message, Contains.Substring("AWS credentials are required for Bedrock REST API access"));
        }

        [Test]
        public void CreateLlmClient_WithSessionTokenButNoCredentials_ThrowsInvalidOperationException()
        {
            // Arrange
            var config = CreateValidBedrockConfig();
            config.bedrockProviderConfig.accessKey = "";
            config.bedrockProviderConfig.secretKey = "";
            config.bedrockProviderConfig.sessionToken = "session-token";

            // Act & Assert
            var ex = Assert.Throws<InvalidOperationException>(() => LlmClientFactory.CreateLlmClient(config));
            Assert.That(ex.Message, Contains.Substring("AWS credentials are required for Bedrock REST API access"));
        }

        [Test]
        public void CreateLlmClient_WithMissingCredentials_ThrowsInvalidOperationException()
        {
            // Arrange
            var config = CreateValidBedrockConfig();
            config.bedrockProviderConfig.accessKey = "";
            config.bedrockProviderConfig.secretKey = "";

            // Act & Assert
            var ex = Assert.Throws<InvalidOperationException>(() => LlmClientFactory.CreateLlmClient(config));
            Assert.That(ex.Message, Contains.Substring("AWS credentials are required for Bedrock REST API access"));
        }

        [Test]
        public void CreateLlmClient_WithValidCredentials_Succeeds()
        {
            // Arrange
            var config = CreateValidBedrockConfig();
            config.bedrockProviderConfig.accessKey = "AKIAIOSFODNN7EXAMPLE";
            config.bedrockProviderConfig.secretKey = "wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY";

            // Act & Assert
            Assert.DoesNotThrow(() => LlmClientFactory.CreateLlmClient(config));
        }

        [Test]
        public void CreateLlmClient_WithValidCredentialsAndSessionToken_Succeeds()
        {
            // Arrange
            var config = CreateValidBedrockConfig();
            config.bedrockProviderConfig.accessKey = "AKIAIOSFODNN7EXAMPLE";
            config.bedrockProviderConfig.secretKey = "wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY";
            config.bedrockProviderConfig.sessionToken = "session-token";

            // Act & Assert
            Assert.DoesNotThrow(() => LlmClientFactory.CreateLlmClient(config));
        }


    }
}
