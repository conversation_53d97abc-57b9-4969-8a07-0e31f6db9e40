/*
 * Copyright 2021-present Alcon. All rights reserved.
 * Alcon proprietary/confidential.
 * Use is subject to license terms.
 */

using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;

namespace LocalizationLib;

public class CsExtractor : IExtractor
{
    private readonly ICsProcessor _csProcessor;
    private static readonly HashSet<string> _ignoredFileNames = new HashSet<string>(
    StringComparer.OrdinalIgnoreCase)
    {
        "AssemblyInfo.cs",
        // Add more file names in the future if needed
        // "SomeOtherFileToIgnore.cs"
    };

    public CsExtractor(string csProcessorOption = "roslyn")
    {
        if (string.Equals(csProcessorOption, "regex", StringComparison.OrdinalIgnoreCase))
        {
            _csProcessor = new RegexCsProcessor();
            Console.WriteLine("[INFO] Using RegexCsProcessor for .cs extraction.");
        }
        else if (string.Equals(csProcessorOption, "llm", StringComparison.OrdinalIgnoreCase))
        {
            string configPath = LLMConfigPathResolver.ResolveLLMConfigPath();
            if (configPath == null)
            {
                Console.WriteLine("Error: LLM config not found in any of the expected locations. Cannot run LLMCsProcessor.");
                return;
            }

            var config = LLMConfigLoader.LoadLlmConfig(configPath);
            if (config == null)
            {
                Console.WriteLine("Error: Failed to load LLM config. Cannot run LLMCsProcessor.");
                return;
            }

            // Create the local LLM client
            var localClient = new LocalLlmClient(config);
            _csProcessor = new LLMCsProcessor(localClient, config);
            Console.WriteLine("[INFO] Using LLMCsProcessor for .cs extraction.");

        }
        else
        {
            // Default to Roslyn
            _csProcessor = new RoslynCsProcessor();
            Console.WriteLine("[INFO] Using RoslynCsProcessor for .cs extraction.");
        }
    }

    public (List<FileProcessingResult> fileResults, List<ReplacementRecord> replacements) Extract(string projectPath)
    {
        // Get config manager to access skip patterns
        var config = ProjectTextExtractorConfigManager.GetConfigSafe(projectPath);
        var skipPatterns = config?.SkipPatterns ?? new List<string>();

        // 1. Enumerate all .cs files 
        var allCsFiles = Directory
            .GetFiles(projectPath, "*.cs", SearchOption.AllDirectories)
            .Where(path =>
            {
                // Skip ignored directories and patterns
                if (PathUtils.IsInIgnoredDirectory(path))
                    return false;
                if (PathUtils.ShouldSkipPath(path, skipPatterns))
                    return false;

                // Skip anything in the ignore list (like AssemblyInfo.cs)
                var fileNameOnly = Path.GetFileName(path);
                if (_ignoredFileNames.Contains(fileNameOnly))
                    return false;

                // Keep everything else
                return true;
            })
            .ToArray();

        Console.WriteLine($"[C#] Found {allCsFiles.Length} C# files under '{projectPath}',"
            + " excluding obj folder, ignored files, and skip patterns.");

        var fileResults = new List<FileProcessingResult>();

        // 2. Process each .cs file that remains
        foreach (var csPath in allCsFiles)
        {
            string groupId = LocalizationUtils.GetGroupIdFromCs(csPath);
            try
            {
                var result = _csProcessor.ProcessCsFile(csPath, groupId);
                fileResults.Add(result);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error processing {csPath}: {ex.Message}");
            }
        }

        // 3. Return the results
        return (fileResults, _csProcessor.AllReplacementRecords);
    }

}


