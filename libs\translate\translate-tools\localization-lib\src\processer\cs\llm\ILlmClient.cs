/*
 * Copyright 2021-present Alcon. All rights reserved.
 * Alcon proprietary/confidential.
 * Use is subject to license terms.
 */

using System.Collections.Generic;

namespace LocalizationLib;

/// <summary>
/// Interface for Large Language Model (LLM) clients used in localization text analysis.
///
/// This interface provides intelligent text analysis capabilities to determine whether
/// strings in source code files need translation for internationalization purposes.
///
/// Supported Implementations:
///
/// 1. **LocalLlmClient** - Local AI with LM Studio
///    - Privacy-focused: All processing happens locally
///    - Cost-effective: No per-token charges after initial setup
///    - Offline capable: Works without internet connection
///    - Model flexibility: Support for CodeLlama, Llama 2, Mistral, etc.
///    - Setup: Requires LM Studio installation and model download
///    - Configuration: Set llmProvider to "LocalLlmProvider"
///    - Documentation: See localization-lib/docs/LM-Studio-Integration.md
///
/// 2. **BedrockLlmClient** - Enterprise Cloud AI with Amazon Bedrock
///    - Enterprise-grade: AWS-managed foundation models
///    - High quality: Access to Claude 3, Titan, and other advanced models
///    - Scalable: Handles large workloads with automatic scaling
///    - Zero maintenance: Fully managed service
///    - Setup: Requires AWS account and Bedrock model access
///    - Configuration: Set llmProvider to "BedrockProvider"
///    - Documentation: See localization-lib/docs/Amazon-Bedrock-Integration.md
///
/// Usage:
/// Configure via LLMConfig with appropriate provider settings. The client will
/// analyze code context, string content, and usage patterns to make intelligent
/// decisions about which strings require translation.
///
/// Example Configuration:
/// ```json
/// {
///   "llmProvider": "LocalLlmProvider",  // or "BedrockProvider"
///   "promptTemplate": "Analyze this code for user-facing strings...",
///   "localLlmProviderConfig": {         // or "bedrockProviderConfig"
///     "baseUrl": "http://localhost:1234",
///     "modelName": "CodeLlama-7B-Instruct"
///   }
/// }
/// ```
///
/// The interface supports analysis of C#, XAML, and C++ files with context-aware
/// decision making that considers factors like:
/// - String content and formatting
/// - Code context and usage patterns
/// - UI vs. technical string classification
/// - Logging and debug message detection
/// - Configuration and system string identification
/// </summary>
public interface ILlmClient
{
    /// <summary>
    /// Given the entire source code (or some text) plus a groupId,
    /// returns a list of ReplacementRecord objects indicating
    /// which lines/columns in the code need localization.
    /// </summary>
    List<ReplacementRecord> DiscoverAndProposeReplacements(string fileContent, string groupId, string fileName, string filePath);

    /// <summary>
    /// Verifies if a specific text in a C# file needs to be translated.
    /// </summary>
    /// <param name="text">The text to verify</param>
    /// <param name="lineNumber">The line number where the text appears</param>
    /// <param name="lineContent">The entire line content</param>
    /// <param name="fileContent">The entire file content for context</param>
    /// <param name="groupId">The group ID for the translation</param>
    /// <param name="filePath">The path to the file</param>
    /// <returns>A TranslationVerificationResult containing the decision and reasoning</returns>
    TranslationVerificationResult VerifyTranslationNeeded(
        string text,
        int lineNumber,
        string lineContent,
        string fileContent,
        string groupId,
        string filePath);

    /// <summary>
    /// Verifies if a specific text in a XAML file needs to be translated.
    /// </summary>
    /// <param name="text">The text to verify</param>
    /// <param name="propertyName">The XAML property containing the text (Content, Text, Header, etc.)</param>
    /// <param name="lineNumber">The line number where the text appears</param>
    /// <param name="lineContent">The entire line content</param>
    /// <param name="xamlContent">The entire XAML content for context</param>
    /// <param name="filePath">The path to the file</param>
    /// <param name="groupId">The group ID for the translation</param>
    /// <returns>A TranslationVerificationResult containing the decision and reasoning</returns>
    TranslationVerificationResult VerifyXamlTranslationNeeded(
        string text,
        string propertyName,
        int lineNumber,
        string lineContent,
        string xamlContent,
        string filePath,
        string groupId);

    /// <summary>
    /// Verifies if a specific text in a C++ file needs to be translated.
    /// </summary>
    /// <param name="text">The text to verify</param>
    /// <param name="stringType">The C++ string type (e.g., "char*", "wchar_t*", "u8string", etc.)</param>
    /// <param name="lineNumber">The line number where the text appears</param>
    /// <param name="lineContent">The entire line content</param>
    /// <param name="fileContent">The entire file content for context</param>
    /// <param name="filePath">The path to the file</param>
    /// <param name="groupId">The group ID for the translation</param>
    /// <returns>A TranslationVerificationResult containing the decision and reasoning</returns>
    TranslationVerificationResult VerifyCppTranslationNeeded(
        string text,
        string stringType,
        int lineNumber,
        string lineContent,
        string fileContent,
        string filePath,
        string groupId);

    /// <summary>
    /// Gets the LLM configuration used by this client
    /// </summary>
    /// <returns>The LLM configuration</returns>
    LLMConfig GetConfig();
}
