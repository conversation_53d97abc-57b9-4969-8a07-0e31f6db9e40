
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.30/Modules/CMakeDetermineSystem.cmake:205 (message)"
      - "CMakeLists.txt:3 (project)"
    message: |
      The system is: Windows - 10.0.22631 - AMD64
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-3.30/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-3.30/Modules/CMakeDetermineCSharpCompiler.cmake:23 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Compiling the CSharp compiler identification source file "CMakeCSharpCompilerId.cs" succeeded.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      0
      MSBuild version 17.11.9+a69bbaaf5 for .NET Framework
      Build started 2/3/2025 9:27:25 AM.
      
      Project "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\build\\CMakeFiles\\3.30.1\\CompilerIdCSharp\\CompilerIdCSharp.csproj" on node 1 (default targets).
      PrepareForBuild:
        Creating directory "obj\\x64\\Debug\\".
      CoreCompile:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Current\\Bin\\Roslyn\\csc.exe /noconfig /nowarn:1701,1702 /fullpaths /nostdlib+ /platform:anycpu32bitpreferred /define:Platformx64;PlatformToolsetv143 /highentropyva+ /reference:"C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\mscorlib.dll" /reference:"C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Core.dll" /debug+ /out:obj\\x64\\Debug\\CompilerIdCSharp.exe /subsystemversion:6.00 /target:exe /utf8output /langversion:7.3 CMakeCSharpCompilerId.cs "obj\\x64\\Debug\\.NETFramework,Version=v4.7.2.AssemblyAttributes.cs"
        CompilerServer: server - server processed compilation - CompilerIdCSharp
      CopyFilesToOutputDirectory:
        Copying file from "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\build\\CMakeFiles\\3.30.1\\CompilerIdCSharp\\obj\\x64\\Debug\\CompilerIdCSharp.exe" to "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\build\\CMakeFiles\\3.30.1\\CompilerIdCSharp\\CompilerIdCSharp.exe".
        CompilerIdCSharp -> D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\build\\CMakeFiles\\3.30.1\\CompilerIdCSharp\\CompilerIdCSharp.exe
        Copying file from "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\build\\CMakeFiles\\3.30.1\\CompilerIdCSharp\\obj\\x64\\Debug\\CompilerIdCSharp.pdb" to "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\build\\CMakeFiles\\3.30.1\\CompilerIdCSharp\\CompilerIdCSharp.pdb".
      PostBuildEvent:
        if not "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Current\\Bin\\Roslyn"=="" if exist "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Current\\Bin\\Roslyn\\csc.exe" set _CSC=C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Current\\Bin\\Roslyn
        if exist "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Current\\Bin\\amd64\\csc.exe" set _CSC=C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Current\\Bin\\amd64
        if "%_CSC%"=="" exit -1
        @echo CMAKE_CSharp_COMPILER=%_CSC%\\csc.exe
        CMAKE_CSharp_COMPILER=C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Current\\Bin\\Roslyn\\csc.exe
      Done Building Project "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\build\\CMakeFiles\\3.30.1\\CompilerIdCSharp\\CompilerIdCSharp.csproj" (default targets).
      
      Build succeeded.
          0 Warning(s)
          0 Error(s)
      
      Time Elapsed 00:00:00.49
      
      
      Compilation of the CSharp compiler identification source "CMakeCSharpCompilerId.cs" produced "CompilerIdCSharp.csproj"
      
      Compilation of the CSharp compiler identification source "CMakeCSharpCompilerId.cs" produced "CompilerIdCSharp.exe"
      
      Compilation of the CSharp compiler identification source "CMakeCSharpCompilerId.cs" produced "CompilerIdCSharp.pdb"
      
      The CSharp compiler identification could not be found in:
        D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-lib/build/CMakeFiles/3.30.1/CompilerIdCSharp/CompilerIdCSharp.csproj
      
      The CSharp compiler identification is Microsoft Visual Studio, found in:
        D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-lib/build/CMakeFiles/3.30.1/CompilerIdCSharp/CompilerIdCSharp.exe
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.30/Modules/CMakeTestCSharpCompiler.cmake:34 (try_compile)"
      - "CMakeLists.txt:3 (project)"
    checks:
      - "Check for working C# compiler: C:/Program Files/Microsoft Visual Studio/2022/Professional/MSBuild/Current/Bin/Roslyn/csc.exe"
    directories:
      source: "D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-lib/build/CMakeFiles/CMakeScratch/TryCompile-l4l1gd"
      binary: "D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-lib/build/CMakeFiles/CMakeScratch/TryCompile-l4l1gd"
    cmakeVariables:
      CMAKE_CSharp_FLAGS: "/platform:x64 /define:TRACE"
      CMAKE_CSharp_FLAGS_DEBUG: "/debug:full /optimize- /warn:3 /errorreport:prompt /define:DEBUG"
      CMAKE_EXE_LINKER_FLAGS: ""
    buildResult:
      variable: "CMAKE_CSharp_COMPILER_WORKS"
      cached: true
      stdout: |
        Change Dir: 'D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-lib/build/CMakeFiles/CMakeScratch/TryCompile-l4l1gd'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_6cba6.csproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.11.9+a69bbaaf5 for .NET Framework
        Build started 2/3/2025 9:27:25 AM.
        
        Project "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\build\\CMakeFiles\\CMakeScratch\\TryCompile-l4l1gd\\cmTC_6cba6.csproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\build\\CMakeFiles\\CMakeScratch\\TryCompile-l4l1gd\\Debug\\".
          Creating directory "obj\\x64\\Debug\\".
        CoreCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Current\\Bin\\Roslyn\\csc.exe /noconfig /nowarn:1701,1702 /fullpaths /nostdlib+ /platform:x64 /errorreport:prompt /warn:3 /define:TRACE;DEBUG /highentropyva+ /reference:"C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\mscorlib.dll" /reference:"C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Core.dll" /debug+ /debug:full /optimize- /out:obj\\x64\\Debug\\cmTC_6cba6.exe /subsystemversion:6.00 /target:exe /utf8output /langversion:7.3 D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\build\\CMakeFiles\\CMakeScratch\\TryCompile-l4l1gd\\testCSharpCompiler.cs "obj\\x64\\Debug\\.NETFramework,Version=v4.7.2.AssemblyAttributes.cs"
          CompilerServer: server - server processed compilation - cmTC_6cba6
        CopyFilesToOutputDirectory:
          Copying file from "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\build\\CMakeFiles\\CMakeScratch\\TryCompile-l4l1gd\\obj\\x64\\Debug\\cmTC_6cba6.exe" to "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\build\\CMakeFiles\\CMakeScratch\\TryCompile-l4l1gd\\Debug\\cmTC_6cba6.exe".
          cmTC_6cba6 -> D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\build\\CMakeFiles\\CMakeScratch\\TryCompile-l4l1gd\\Debug\\cmTC_6cba6.exe
          Copying file from "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\build\\CMakeFiles\\CMakeScratch\\TryCompile-l4l1gd\\obj\\x64\\Debug\\cmTC_6cba6.pdb" to "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\build\\CMakeFiles\\CMakeScratch\\TryCompile-l4l1gd\\Debug\\cmTC_6cba6.pdb".
        Done Building Project "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\build\\CMakeFiles\\CMakeScratch\\TryCompile-l4l1gd\\cmTC_6cba6.csproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:00.18
        
      exitCode: 0
...

---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineSystem.cmake:205 (message)"
      - "CMakeLists.txt:3 (project)"
    message: |
      The system is: Windows - 10.0.26100 - AMD64
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeDetermineCSharpCompiler.cmake:23 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Compiling the CSharp compiler identification source file "CMakeCSharpCompilerId.cs" succeeded.
      Compiler:  
      Build flags: /platform:x64;/define:TRACE
      Id flags:  
      
      The output was:
      0
      MSBuild version 17.11.9+a69bbaaf5 for .NET Framework
      Build started 3/26/2025 4:43:09 PM.
      
      Project "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\build\\CMakeFiles\\4.0.0-rc4\\CompilerIdCSharp\\CompilerIdCSharp.csproj" on node 1 (default targets).
      PrepareForBuild:
        Creating directory "obj\\x64\\Debug\\".
      CoreCompile:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Current\\Bin\\Roslyn\\csc.exe /noconfig /nowarn:1701,1702 /fullpaths /nostdlib+ /platform:anycpu32bitpreferred /define:Platformx64;PlatformToolsetv143 /highentropyva+ /reference:"C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\mscorlib.dll" /reference:"C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Core.dll" /debug+ /out:obj\\x64\\Debug\\CompilerIdCSharp.exe /subsystemversion:6.00 /target:exe /utf8output /langversion:7.3 CMakeCSharpCompilerId.cs "obj\\x64\\Debug\\.NETFramework,Version=v4.7.2.AssemblyAttributes.cs"
        CompilerServer: server - server processed compilation - CompilerIdCSharp
      CopyFilesToOutputDirectory:
        Copying file from "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\build\\CMakeFiles\\4.0.0-rc4\\CompilerIdCSharp\\obj\\x64\\Debug\\CompilerIdCSharp.exe" to "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\build\\CMakeFiles\\4.0.0-rc4\\CompilerIdCSharp\\CompilerIdCSharp.exe".
        CompilerIdCSharp -> D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\build\\CMakeFiles\\4.0.0-rc4\\CompilerIdCSharp\\CompilerIdCSharp.exe
        Copying file from "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\build\\CMakeFiles\\4.0.0-rc4\\CompilerIdCSharp\\obj\\x64\\Debug\\CompilerIdCSharp.pdb" to "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\build\\CMakeFiles\\4.0.0-rc4\\CompilerIdCSharp\\CompilerIdCSharp.pdb".
      PostBuildEvent:
        if not "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Current\\Bin\\Roslyn"=="" if exist "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Current\\Bin\\Roslyn\\csc.exe" set _CSC=C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Current\\Bin\\Roslyn
        if exist "C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Current\\Bin\\amd64\\csc.exe" set _CSC=C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Current\\Bin\\amd64
        if "%_CSC%"=="" exit -1
        @echo CMAKE_CSharp_COMPILER=%_CSC%\\csc.exe
        CMAKE_CSharp_COMPILER=C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Current\\Bin\\Roslyn\\csc.exe
      Done Building Project "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\build\\CMakeFiles\\4.0.0-rc4\\CompilerIdCSharp\\CompilerIdCSharp.csproj" (default targets).
      
      Build succeeded.
          0 Warning(s)
          0 Error(s)
      
      Time Elapsed 00:00:00.36
      
      
      Compilation of the CSharp compiler identification source "CMakeCSharpCompilerId.cs" produced "CompilerIdCSharp.csproj"
      
      Compilation of the CSharp compiler identification source "CMakeCSharpCompilerId.cs" produced "CompilerIdCSharp.exe"
      
      Compilation of the CSharp compiler identification source "CMakeCSharpCompilerId.cs" produced "CompilerIdCSharp.pdb"
      
      The CSharp compiler identification could not be found in:
        D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-lib/build/CMakeFiles/4.0.0-rc4/CompilerIdCSharp/CompilerIdCSharp.csproj
      
      The CSharp compiler identification is Microsoft Visual Studio, found in:
        D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-lib/build/CMakeFiles/4.0.0-rc4/CompilerIdCSharp/CompilerIdCSharp.exe
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-4.0/Modules/CMakeTestCSharpCompiler.cmake:34 (try_compile)"
      - "CMakeLists.txt:3 (project)"
    checks:
      - "Check for working C# compiler: C:/Program Files/Microsoft Visual Studio/2022/Professional/MSBuild/Current/Bin/Roslyn/csc.exe"
    directories:
      source: "D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-lib/build/CMakeFiles/CMakeScratch/TryCompile-azgt7m"
      binary: "D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-lib/build/CMakeFiles/CMakeScratch/TryCompile-azgt7m"
    cmakeVariables:
      CMAKE_CSharp_FLAGS: "/platform:x64 /define:TRACE"
      CMAKE_CSharp_FLAGS_DEBUG: "/debug:full /optimize- /warn:3 /errorreport:prompt /define:DEBUG"
      CMAKE_EXE_LINKER_FLAGS: ""
    buildResult:
      variable: "CMAKE_CSharp_COMPILER_WORKS"
      cached: true
      stdout: |
        Change Dir: 'D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-lib/build/CMakeFiles/CMakeScratch/TryCompile-azgt7m'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Professional/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_90708.csproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.11.9+a69bbaaf5 for .NET Framework
        Build started 3/26/2025 4:43:10 PM.
        
        Project "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\build\\CMakeFiles\\CMakeScratch\\TryCompile-azgt7m\\cmTC_90708.csproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\build\\CMakeFiles\\CMakeScratch\\TryCompile-azgt7m\\Debug\\".
          Creating directory "obj\\x64\\Debug\\".
        CoreCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Professional\\MSBuild\\Current\\Bin\\Roslyn\\csc.exe /noconfig /nowarn:1701,1702 /fullpaths /nostdlib+ /platform:x64 /errorreport:prompt /warn:3 /define:TRACE;DEBUG /highentropyva+ /reference:"C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\mscorlib.dll" /reference:"C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.7.2\\System.Core.dll" /debug+ /debug:full /optimize- /out:obj\\x64\\Debug\\cmTC_90708.exe /subsystemversion:6.00 /target:exe /utf8output /langversion:7.3 D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\build\\CMakeFiles\\CMakeScratch\\TryCompile-azgt7m\\testCSharpCompiler.cs "obj\\x64\\Debug\\.NETFramework,Version=v4.7.2.AssemblyAttributes.cs"
          CompilerServer: server - server processed compilation - cmTC_90708
        CopyFilesToOutputDirectory:
          Copying file from "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\build\\CMakeFiles\\CMakeScratch\\TryCompile-azgt7m\\obj\\x64\\Debug\\cmTC_90708.exe" to "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\build\\CMakeFiles\\CMakeScratch\\TryCompile-azgt7m\\Debug\\cmTC_90708.exe".
          cmTC_90708 -> D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\build\\CMakeFiles\\CMakeScratch\\TryCompile-azgt7m\\Debug\\cmTC_90708.exe
          Copying file from "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\build\\CMakeFiles\\CMakeScratch\\TryCompile-azgt7m\\obj\\x64\\Debug\\cmTC_90708.pdb" to "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\build\\CMakeFiles\\CMakeScratch\\TryCompile-azgt7m\\Debug\\cmTC_90708.pdb".
        Done Building Project "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\build\\CMakeFiles\\CMakeScratch\\TryCompile-azgt7m\\cmTC_90708.csproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:00.18
        
      exitCode: 0
...
