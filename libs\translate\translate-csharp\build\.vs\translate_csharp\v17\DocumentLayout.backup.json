{"Version": 1, "WorkspaceRootPath": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-csharp\\build\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{D4859805-07F9-3863-BD39-0B33241A7E06}|translate_csharp.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-csharp\\src\\models\\languagetranslation.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{D4859805-07F9-3863-BD39-0B33241A7E06}|translate_csharp.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-csharp\\src\\translator.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 1, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{e1b7d1f8-9b3c-49b1-8f4f-bfc63a88835d}"}, {"$type": "Document", "DocumentIndex": 0, "Title": "LanguageTranslation.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-csharp\\src\\Models\\LanguageTranslation.cs", "RelativeDocumentMoniker": "..\\src\\Models\\LanguageTranslation.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-csharp\\src\\Models\\LanguageTranslation.cs", "RelativeToolTip": "..\\src\\Models\\LanguageTranslation.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA4AAAAoAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-02T11:33:01.049Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "Translator.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-csharp\\src\\Translator.cs", "RelativeDocumentMoniker": "..\\src\\Translator.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-csharp\\src\\Translator.cs", "RelativeToolTip": "..\\src\\Translator.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABYAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-01-24T06:02:45.21Z", "EditorCaption": ""}]}]}]}