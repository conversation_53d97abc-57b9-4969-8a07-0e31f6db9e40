/*
 * Copyright 2021-present Alcon. All rights reserved.
 * Alcon proprietary/confidential.
 * Use is subject to license terms.
 */

using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using LocalizationLib.Llm;

namespace LocalizationLib;

/// <summary>
/// A consolidated processor that combines regex-based string extraction with LLM validation
/// to determine which strings need translation in C++ files.
/// </summary>
public class ConsolidatedCppProcessor : ICppProcessor
{
    private readonly ICppProcessor _regexExtractor;
    private readonly ILlmClient _llmClient;
    private readonly bool _useLlmVerification;
    private readonly string _projectPath;
    private readonly LLMConfig _llmConfig;
    private readonly LlmCacheManager _llmCacheManager;
    
    // Config for file chunking during LLM verification
    private readonly int _contextLines = 50; // Default context size
    private readonly bool _useChunking = false;

    public List<ReplacementRecord> AllReplacementRecords { get; } = new List<ReplacementRecord>();

    public ConsolidatedCppProcessor(string projectPath = null, ILlmClient llmClient = null)
    {
        _regexExtractor = new RegexCppProcessor();
        _llmClient = llmClient;
        _useLlmVerification = llmClient != null;
        _projectPath = projectPath;
        _llmCacheManager = new LlmCacheManager(projectPath ?? ".");
        
        // Get config from the LLM client if available
        if (_llmClient != null)
        {
            _llmConfig = _llmClient.GetConfig();
            
            // Initialize chunking parameters from config
            if (_llmConfig?.localLlmProviderConfig != null)
            {
                if (_llmConfig.localLlmProviderConfig.chunkSize > 0)
                {
                    // Use chunkSize for contextLines - divide by 2 since we use context before and after
                    _contextLines = _llmConfig.localLlmProviderConfig.chunkSize / 2;
                }
                
                _useChunking = _llmConfig.localLlmProviderConfig.useChunking;
            }
        }
    }

    public FileProcessingResult ProcessCppFile(string cppPath, string groupId)
    {
        // First use regex processor to find all string literals
        var result = _regexExtractor.ProcessCppFile(cppPath, groupId);
        
        if (!_useLlmVerification)
        {
            // If no LLM client, just return regex results
            AllReplacementRecords.AddRange(result.Replacements);
            return result;
        }

        // Get file content for context
        string fileContent = File.ReadAllText(cppPath);
        
        // Verify each string with LLM
        var verifiedReplacements = new List<ReplacementRecord>();
        foreach (var record in result.Replacements)
        {
            // Validate location before LLM verification
            /*
            if (!ValidateLocation(record))
            {
                Console.WriteLine($"\nSkipping verification for text: \"{record.OriginalValue}\"");
                Console.WriteLine($"Location validation failed: {cppPath}:{record.LineNumber}");
                record.Notes.Add("Skipped verification: Location validation failed");
                record.Notes.Add("Marked for removal");
                continue;
            }
            */

            var stringType = _regexExtractor.GetStringType(record);
            
            // Check if we have cached results
            var cachedResult = _llmCacheManager.GetVerificationResult(
                record.OriginalValue,
                cppPath,
                record.LineNumber,
                record.Column,
                _llmClient,
                _llmConfig);

            if (cachedResult != null)
            {
                Console.WriteLine($"\nUsing cached verification for text: \"{record.OriginalValue}\"");
                Console.WriteLine($"Location: {cppPath}:{record.LineNumber}");
                Console.WriteLine($"Cached Decision: {(cachedResult.NeedsTranslation ? "Needs Translation" : "Skip")}");
                Console.WriteLine($"Cached Reason: {cachedResult.Reason}");

                // Add cached verification details to notes
                record.Notes.Add($"LLM Verification (Cached): {(cachedResult.NeedsTranslation ? "Needs Translation" : "Skip")}");
                record.Notes.Add($"LLM Reason (Cached): {cachedResult.Reason}");
                record.Notes.Add($"LLM Context (Cached): {cachedResult.Context}");
                record.NeedsTranslation = cachedResult.NeedsTranslation;

                if (cachedResult.NeedsTranslation)
                {
                    verifiedReplacements.Add(record);
                }
                continue;
            }

            Console.WriteLine($"\nVerifying string: {record.OriginalValue}");
            Console.WriteLine($"String type: {stringType}");
            Console.WriteLine($"Location: {cppPath}:{record.LineNumber}");

            var verificationResult = _llmClient.VerifyCppTranslationNeeded(
                text: record.OriginalValue,
                stringType: stringType,
                lineNumber: record.LineNumber,
                lineContent: record.LineContent,
                fileContent: GetRelevantFileContext(cppPath, record.LineNumber),
                filePath: cppPath,
                groupId: groupId
            );

            Console.WriteLine($"LLM Verification Result:");
            Console.WriteLine($"Decision: {(verificationResult.NeedsTranslation ? "Needs Translation" : "Skip")}");
            Console.WriteLine($"Reason: {verificationResult.Reason}");
            Console.WriteLine($"Context: {verificationResult.Context}");

            // Store the verification result in the LLM cache
            _llmCacheManager.AddVerificationResult(
                record.OriginalValue,
                cppPath,
                record.LineNumber,
                record.Column,
                verificationResult.NeedsTranslation,
                verificationResult.Reason,
                verificationResult.Context,
                record.Source,
                _llmClient,
                _llmConfig,
                record.LineContent
            );

            // Add verification details to notes
            record.Notes.Add($"LLM Verification: {(verificationResult.NeedsTranslation ? "Needs Translation" : "Skip")}");
            record.Notes.Add($"LLM Reason: {verificationResult.Reason}");
            record.Notes.Add($"LLM Context: {verificationResult.Context}");
            record.NeedsTranslation = verificationResult.NeedsTranslation;

            // Only keep strings that need translation
            if (verificationResult.NeedsTranslation)
            {
                verifiedReplacements.Add(record);
            }
        }

        // Update result with verified replacements
        result.Replacements.Clear();
        result.Replacements.AddRange(verifiedReplacements);
        
        // Add to all records
        AllReplacementRecords.AddRange(verifiedReplacements);

        return result;
    }

    private bool ValidateLocation(ReplacementRecord record)
    {
        try
        {
            var fileContent = GetFileContent(record.FilePath);
            if (string.IsNullOrEmpty(fileContent))
                return false;

            var lines = fileContent.Split('\n');
            if (record.LineNumber <= 0 || record.LineNumber > lines.Length)
            {
                Console.WriteLine($"Warning: Invalid line number {record.LineNumber} for file {record.FilePath}");
                return false;
            }

            // If the text contains newlines, handle it as a multi-line string first
            if (record.OriginalValue.Contains('\n'))
            {
                return ValidateMultiLineLocation(record, lines);
            }

            var line = lines[record.LineNumber - 1];
            
            // Set LineContent if not already set
            if (string.IsNullOrEmpty(record.LineContent))
            {
                record.LineContent = line;
            }
            
            // First try to find the text in the line, handling escaped characters
            var textIndex = -1;
            var searchText = record.OriginalValue;
            var lineText = line;

            // Try different variations of the text
            if (searchText.Contains("\\"))
            {
                // Try with single backslashes first
                textIndex = lineText.IndexOf(searchText);
                if (textIndex == -1)
                {
                    // Try with double backslashes (escaped format)
                    var escapedText = searchText.Replace("\\", "\\\\");
                    textIndex = lineText.IndexOf(escapedText);
                }
            }
            else
            {
                textIndex = lineText.IndexOf(searchText);
            }

            if (textIndex == -1)
            {
                Console.WriteLine($"Warning: Could not find text \"{record.OriginalValue}\" in line {record.LineNumber} of {record.FilePath}");
                return false;
            }

            // Look for the opening quote before the text
            var beforeText = line.Substring(0, textIndex);
            var lastQuoteIndex = beforeText.LastIndexOf('"');
            
            if (lastQuoteIndex == -1)
            {
                Console.WriteLine($"Warning: Could not find opening quote for text \"{record.OriginalValue}\" in line {record.LineNumber} of {record.FilePath}");
                return false;
            }

            // Update the column to point to the opening quote
            var newColumn = lastQuoteIndex + 1; // Convert to 1-based column
            if (newColumn != record.Column)
            {
                Console.WriteLine($"Info: Column position adjusted from {record.Column} to {newColumn} (opening quote) for text \"{record.OriginalValue}\" in {record.FilePath}:{record.LineNumber}");
                record.Column = newColumn;
                record.Notes.Add($"Column position adjusted to opening quote position: {newColumn}");
            }

            return true;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Warning: Could not validate location for {record.FilePath}:{record.LineNumber}:{ex.Message}");
            return false;
        }
    }

    private bool ValidateMultiLineLocation(ReplacementRecord record, string[] lines)
    {
        try
        {
            // Find the line containing the first part of the text
            var firstLineText = record.OriginalValue.TrimStart('\n').Split('\n')[0];
            var startLineIndex = -1;
            var startColumnIndex = -1;

            // Search from the current line number backwards first
            for (int i = record.LineNumber - 1; i >= 0; i--)
            {
                var ln = lines[i];
                var index = ln.IndexOf(firstLineText);
                if (index != -1)
                {
                    startLineIndex = i;
                    startColumnIndex = index;
                    break;
                }
            }

            // If not found, search forward
            if (startLineIndex == -1)
            {
                for (int i = record.LineNumber - 1; i < lines.Length; i++)
                {
                    var ln = lines[i];
                    var index = ln.IndexOf(firstLineText);
                    if (index != -1)
                    {
                        startLineIndex = i;
                        startColumnIndex = index;
                        break;
                    }
                }
            }

            if (startLineIndex == -1)
            {
                Console.WriteLine($"Warning: Could not find start of multi-line text \"{record.OriginalValue}\" in file {record.FilePath}");
                return false;
            }

            // Look for the opening quote before the text
            var line = lines[startLineIndex];
            var beforeText = line.Substring(0, startColumnIndex);
            var lastQuoteIndex = beforeText.LastIndexOf('"');
            
            if (lastQuoteIndex == -1)
            {
                Console.WriteLine($"Warning: Could not find opening quote for multi-line text \"{record.OriginalValue}\" in line {startLineIndex + 1} of {record.FilePath}");
                return false;
            }

            // Update the record with the correct line and column
            var newColumn = lastQuoteIndex + 1; // Convert to 1-based column
            if (startLineIndex + 1 != record.LineNumber || newColumn != record.Column)
            {
                Console.WriteLine($"Info: Location adjusted for multi-line text \"{record.OriginalValue}\" in {record.FilePath}");
                Console.WriteLine($"      From: Line {record.LineNumber}, Column {record.Column}");
                Console.WriteLine($"      To: Line {startLineIndex + 1}, Column {newColumn}");
                
                record.LineNumber = startLineIndex + 1;
                record.Column = newColumn;
                record.Notes.Add($"Location adjusted for multi-line text: Line {startLineIndex + 1}, Column {newColumn}");
            }

            // Set LineContent to include all lines of the multi-line string
            if (string.IsNullOrEmpty(record.LineContent))
            {
                var multiLineContent = new StringBuilder();
                var textLines = record.OriginalValue.Split('\n');
                var currentLineIndex = startLineIndex;
                
                for (int i = 0; i < textLines.Length; i++)
                {
                    if (currentLineIndex >= lines.Length)
                        break;
                        
                    var currentLine = lines[currentLineIndex];
                    if (i == 0)
                    {
                        // For the first line, only include from the opening quote
                        multiLineContent.Append(currentLine.Substring(lastQuoteIndex));
                    }
                    else
                    {
                        multiLineContent.Append('\n').Append(currentLine);
                    }
                    currentLineIndex++;
                }
                
                record.LineContent = multiLineContent.ToString();
            }

            return true;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Warning: Could not validate multi-line location for {record.FilePath}:{record.LineNumber}:{ex.Message}");
            return false;
        }
    }

    private string GetFileContent(string filePath)
    {
        try
        {
            return File.ReadAllText(filePath);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Warning: Could not read file content for {filePath}: {ex.Message}");
            return string.Empty;
        }
    }

    /// <summary>
    /// Extracts a relevant portion of the file content around the specified line number
    /// to avoid sending the entire file to the LLM and exceeding token limits.
    /// Uses the LLMConfig settings for chunking when available.
    /// </summary>
    private string GetRelevantFileContext(string filePath, int lineNumber)
    {
        try
        {
            string[] allLines = File.ReadAllLines(filePath);
            if (allLines.Length == 0)
            {
                return string.Empty;
            }

            // If chunking is disabled or the file is small enough, return the whole file
            if (!_useChunking || allLines.Length <= _contextLines * 2 + 1)
            {
                return string.Join("\n", allLines);
            }

            // Calculate the start and end lines to extract
            int startLine = Math.Max(0, lineNumber - _contextLines);
            int endLine = Math.Min(allLines.Length - 1, lineNumber + _contextLines);

            // Extract the relevant lines
            var relevantLines = new List<string>();
            
            // Add a header to indicate this is a file excerpt
            relevantLines.Add($"// File excerpt from {Path.GetFileName(filePath)} - Lines {startLine+1} to {endLine+1}");
            relevantLines.Add($"// The line containing the text to validate is at line {lineNumber+1} in the original file");
            relevantLines.Add("");
            
            // Add the relevant code lines
            for (int i = startLine; i <= endLine; i++)
            {
                relevantLines.Add(allLines[i]);
            }

            return string.Join("\n", relevantLines);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Warning: Could not extract relevant context for {filePath}: {ex.Message}");
            return string.Empty;
        }
    }

    public string GetStringType(ReplacementRecord record)
    {
        return _regexExtractor.GetStringType(record);
    }
} 