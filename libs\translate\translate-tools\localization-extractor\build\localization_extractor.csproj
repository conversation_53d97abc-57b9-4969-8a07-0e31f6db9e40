﻿
<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup Label="Globals">
    <ProjectGuid>{7010611E-5491-3CA3-BC86-93F184DB937B}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <Configurations>Debug;Release</Configurations>
    <EnableDefaultItems>false</EnableDefaultItems>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
    <ManagedAssembly>true</ManagedAssembly>
    <TargetFramework>net8.0</TargetFramework>
    <AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
    <OutputType>Exe</OutputType>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)' == 'Debug'">
    <OutputPath>D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-extractor\build\Debug\</OutputPath>
    <AssemblyName>localization_extractor</AssemblyName>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    <DebugType>full</DebugType>
    <DefineConstants>TRACE;DEBUG;_DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <LangVersion>latest</LangVersion>
    <Optimize>false</Optimize>
    <Platform>x64</Platform>
    <WarningLevel>3</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)' == 'Release'">
    <OutputPath>D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-extractor\build\Release\</OutputPath>
    <AssemblyName>localization_extractor</AssemblyName>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    <DebugType>none</DebugType>
    <DefineConstants>TRACE;_DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR</DefineConstants>
    <ErrorReport>queue</ErrorReport>
    <LangVersion>latest</LangVersion>
    <Optimize>true</Optimize>
    <Platform>x64</Platform>
    <WarningLevel>1</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <None Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-extractor\CMakeLists.txt">
      <Link>CMakeLists.txt</Link>
    </None>
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeCSharpInformation.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeGenericSystem.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeInitializeConfigs.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeSystemSpecificInformation.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeSystemSpecificInitialize.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\FindGit.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\FindPackageHandleStandardArgs.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\FindPackageMessage.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Windows-Initialize.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Windows.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\WindowsPaths.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="D:\kibisoft\workarea\git\interop\cmake\Globals.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="D:\kibisoft\workarea\git\interop\cmake\VsPackageReferences.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-extractor\build\CMakeFiles\4.0.0-rc4\CMakeCSharpCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-extractor\build\CMakeFiles\4.0.0-rc4\CMakeSystem.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-extractor\build\CMakeFiles\cmake.verify_globs" />
  </ItemGroup>
  <Target Condition="'$(Configuration)' == 'Debug'"
    Name="CustomCommand_Debug_31fa44c25cf4b032854f283fd5fe5f95"
    Inputs="D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-extractor/CMakeLists.txt;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeCSharpInformation.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeGenericSystem.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\FindGit.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Windows-Initialize.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Windows.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\WindowsPaths.cmake;D:\kibisoft\workarea\git\interop\cmake\Globals.cmake;D:\kibisoft\workarea\git\interop\cmake\VsPackageReferences.cmake;D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-extractor\build\CMakeFiles\4.0.0-rc4\CMakeCSharpCompiler.cmake;D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-extractor\build\CMakeFiles\4.0.0-rc4\CMakeSystem.cmake;D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-extractor\build\CMakeFiles\cmake.verify_globs"
    Outputs="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-extractor\build\CMakeFiles\generate.stamp"
    BeforeTargets="CoreCompile"
    DependsOnTargets="PrepareForBuild">
    <Exec Command="echo Building Custom Rule D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-extractor/CMakeLists.txt" />
    <Exec Command="setlocal&#10;&quot;C:\Program Files\CMake\bin\cmake.exe&quot; -SD:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-extractor -BD:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-extractor/build --check-stamp-file D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-extractor/build/CMakeFiles/generate.stamp&#10;if %errorlevel% neq 0 goto :cmEnd&#10;:cmEnd&#10;endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone&#10;:cmErrorLevel&#10;exit /b %1&#10;:cmDone&#10;if %errorlevel% neq 0 goto :VCEnd&#10;:VCEnd" />
  </Target>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeCSharpInformation.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeGenericSystem.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeInitializeConfigs.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeSystemSpecificInformation.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeSystemSpecificInitialize.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\FindGit.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\FindPackageHandleStandardArgs.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\FindPackageMessage.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Windows-Initialize.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Windows.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\WindowsPaths.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="D:\kibisoft\workarea\git\interop\cmake\Globals.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="D:\kibisoft\workarea\git\interop\cmake\VsPackageReferences.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-extractor\build\CMakeFiles\4.0.0-rc4\CMakeCSharpCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-extractor\build\CMakeFiles\4.0.0-rc4\CMakeSystem.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-extractor\build\CMakeFiles\cmake.verify_globs" />
  </ItemGroup>
  <Target Condition="'$(Configuration)' == 'Release'"
    Name="CustomCommand_Release_31fa44c25cf4b032854f283fd5fe5f95"
    Inputs="D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-extractor/CMakeLists.txt;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeCSharpInformation.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeGenericSystem.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\FindGit.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Windows-Initialize.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Windows.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\WindowsPaths.cmake;D:\kibisoft\workarea\git\interop\cmake\Globals.cmake;D:\kibisoft\workarea\git\interop\cmake\VsPackageReferences.cmake;D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-extractor\build\CMakeFiles\4.0.0-rc4\CMakeCSharpCompiler.cmake;D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-extractor\build\CMakeFiles\4.0.0-rc4\CMakeSystem.cmake;D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-extractor\build\CMakeFiles\cmake.verify_globs"
    Outputs="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-extractor\build\CMakeFiles\generate.stamp"
    BeforeTargets="CoreCompile"
    DependsOnTargets="PrepareForBuild">
    <Exec Command="echo Building Custom Rule D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-extractor/CMakeLists.txt" />
    <Exec Command="setlocal&#10;&quot;C:\Program Files\CMake\bin\cmake.exe&quot; -SD:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-extractor -BD:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-extractor/build --check-stamp-file D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-extractor/build/CMakeFiles/generate.stamp&#10;if %errorlevel% neq 0 goto :cmEnd&#10;:cmEnd&#10;endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone&#10;:cmErrorLevel&#10;exit /b %1&#10;:cmDone&#10;if %errorlevel% neq 0 goto :VCEnd&#10;:VCEnd" />
  </Target>
  <ItemGroup>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-extractor\src\Program.cs">
      <Link>src\Program.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-extractor\src\ToolOptions.cs">
      <Link>src\ToolOptions.cs</Link>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="CommandLineParser" Version="2.9.1" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-extractor\build\ZERO_CHECK.vcxproj">
      <Project>{7F9B09FD-5CC1-3BDC-A12A-861A9185E941}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-extractor\build\trtralocali\localizationlib.csproj">
      <Project>{45258CA8-B215-3C81-B480-6857ECC13998}</Project>
      <Name>localizationlib</Name>
      <SkipGetTargetFrameworkProperties>true</SkipGetTargetFrameworkProperties>
    </ProjectReference>
  </ItemGroup>
</Project>