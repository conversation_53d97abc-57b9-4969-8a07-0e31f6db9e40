# C++ Text Extraction Processor Specification

## Overview

The C++ Text Extraction Processor is designed to extract ALL string literals from C++ source files for potential localization. The processor focuses on accurate string detection and location tracking, while LLM validation determines which strings need translation.

## Goals

1. Extract ALL string literals from C++ source files
2. Support various C++ string literal types and formats
3. Provide accurate location information for extracted texts
4. Maintain consistency with existing C# extraction system
5. Integrate with LLM validation for translation need verification
6. Handle C++-specific string formats and contexts

## Non-Goals

1. Determining which strings need translation (LLM's responsibility)
2. Modifying the source files directly
3. Handling binary files or non-text resources
4. Supporting C++ template metaprogramming text extraction
5. Handling preprocessor macro expansion
6. Supporting C++20 modules (initial version)

## Architecture

### Components

1. **Base Interface**
   - `ITextProcessor` interface for common text processing operations
   - Shared types and utilities for both C# and C++ processors

2. **RegexCppProcessor**
   - Primary implementation for C++ string extraction
   - Focuses on accurate string literal detection
   - Tracks string location and context
   - Extracts ALL string literals regardless of context

3. **ConsolidatedCppProcessor**
   - Combines string extraction with LLM validation
   - Uses LLM to determine which strings need translation
   - Applies C++-specific validation rules
   - Handles result consolidation and reporting

4. **LLM Integration**
   - Determines which strings need translation
   - Provides context-aware validation
   - Applies C++-specific validation rules

### Class Diagram

```mermaid
classDiagram
    class ITextProcessor {
        <<interface>>
        +ProcessFile(string filePath, string groupId) FileProcessingResult
        +AllReplacementRecords List~ReplacementRecord~
    }
    
    class RegexCppProcessor {
        -_stringLiteralRegex Regex
        -_rawStringLiteralRegex Regex
        +ProcessFile(string filePath, string groupId) FileProcessingResult
        -ShouldSkip(string content, int matchIndex) bool
        -ValidateLocation(ReplacementRecord record) bool
    }
    
    class ConsolidatedCppProcessor {
        -_regexExtractor IExtractor
        -_llmClient ILlmClient
        +Extract(string projectPath) (List~FileProcessingResult~, List~ReplacementRecord~)
        -VerifyReplacementsWithLlm(List~ReplacementRecord~ replacements)
    }
    
    ITextProcessor <|.. RegexCppProcessor
    IExtractor <|.. ConsolidatedCppProcessor
```

## Implementation Details

### 1. String Literal Detection

#### Supported String Types
- Basic string literals: `"text"`
- Wide string literals: `L"text"`
- UTF-8 string literals: `u8"text"`
- UTF-16 string literals: `u"text"`
- UTF-32 string literals: `U"text"`
- Raw string literals: `R"(text)"`, `LR"(text)"`, etc.
- String concatenation: `"text" "more text"`

#### Regex Patterns

```csharp
// Basic string literal pattern - captures ALL string literals
private static readonly Regex _stringLiteralRegex = new Regex(
    @"(?<prefix>[Lu8U]?)""(?<txt>(?:\\.|[^""\\])*)""",
    RegexOptions.Compiled
);

// Raw string literal pattern - captures ALL raw string literals
private static readonly Regex _rawStringLiteralRegex = new Regex(
    @"R""\((?<delimiter>[^)]*)\)(?<txt>.*?)\)\k<delimiter>""",
    RegexOptions.Compiled | RegexOptions.Singleline
);
```

### 2. String Context Tracking

#### Context Information
1. **Location Information**
   - File path
   - Line number
   - Column position
   - Surrounding code context

2. **String Properties**
   - String literal type (char*, wchar_t*, etc.)
   - String content
   - Raw/processed format
   - Encoding information

3. **Code Context**
   - Function/method context
   - Class/namespace context
   - Preprocessor context
   - Comment context

### 3. LLM Validation

#### Validation Focus
1. **Translation Need Assessment**
   - Is this string user-facing?
   - Is this string part of the UI?
   - Is this string a message or label?
   - Is this string a system/internal string?

2. **Context Analysis**
   - String usage context
   - String formatting patterns
   - String type implications
   - Surrounding code context

3. **Validation Results**
   - Needs translation flag
   - Confidence score
   - Context explanation
   - Suggested translation key

### 4. Location Validation

#### Validation Rules
1. **Basic Validation**
   - Verify file exists
   - Verify line number is valid
   - Verify column position is valid

2. **Context Validation**
   - Verify string is in valid context
   - Verify string is not in skipped context
   - Verify string is not part of a larger expression

3. **Multi-line String Handling**
   - Handle raw string literals
   - Handle concatenated strings
   - Handle string literals with line continuations

## Testing Strategy

### 1. Unit Tests

#### Test Framework
- Using NUnit 3.x for all unit tests
- Following existing test patterns from `RegexCsProcessorTests.cs`
- Using NUnit attributes: `[TestFixture]`, `[Test]`, `[SetUp]`, `[TearDown]`
- Using NUnit assertions: `Assert.AreEqual`, `Assert.IsTrue`, etc.

#### RegexCppProcessor Tests
```csharp
[TestFixture]
public class RegexCppProcessorTests
{
    private RegexCppProcessor _processor;
    private string _tempFolder;

    [SetUp]
    public void Setup()
    {
        _processor = new RegexCppProcessor();
        _tempFolder = Path.Combine(Path.GetTempPath(), "RegexCppProcessorTests_" + Guid.NewGuid());
        Directory.CreateDirectory(_tempFolder);
    }

    [TearDown]
    public void TearDown()
    {
        if (Directory.Exists(_tempFolder))
        {
            Directory.Delete(_tempFolder, true);
        }
    }

    [Test]
    public void ProcessCppFile_WithRegularStringLiterals_FindsAllStrings()
    {
        // Test implementation
    }
}
```

#### Test Categories
1. **Basic String Detection**
   - Regular string literals
   - Wide string literals
   - UTF-8/16/32 string literals
   - Raw string literals

2. **Skip Pattern Tests**
   - Comments (both // and /* */)
   - Preprocessor directives
   - Logging macros
   - Special contexts (const, static, enum, etc.)

3. **String Type Tests**
   - String type detection
   - Replacement text generation
   - Location validation

4. **File Processing Tests**
   - File existence validation
   - File content reading
   - Result generation
   - Error handling

#### Test Organization
- Tests organized by feature/functionality
- Each test class in its own file
- Test methods named using pattern: `MethodName_Scenario_ExpectedBehavior`
- Common setup/teardown in base class or using `[SetUp]`/`[TearDown]`
- Temporary file handling in `[SetUp]`/`[TearDown]`

### 2. Integration Tests

#### Real-world Scenarios
- Test with actual C++ projects
- Test with different C++ standards
- Test with different build systems
- Test with different IDE configurations

#### Performance Tests
- Large file handling
- Multiple file processing
- LLM validation performance
- Memory usage monitoring

### 3. Test Cases

#### Basic String Literals
```cpp
const char* msg = "Hello World";
const wchar_t* wmsg = L"Hello World";
const char8_t* u8msg = u8"Hello World";
```

#### Raw String Literals
```cpp
const char* raw = R"(Multi-line
string with
special characters)";
```

#### Skip Patterns
```cpp
#define LOG_MSG(msg) // Skip strings in macros
#pragma message("Skip this") // Skip in pragmas
assert(strcmp(s, "Skip this") == 0); // Skip in assertions
```

## Configuration

### 1. Processor Configuration

```json
{
  "cppProcessor": {
    "enabled": true,
    "fileExtensions": [".cpp", ".h", ".hpp", ".cxx", ".hxx"],
    "skipPatterns": {
      "preprocessor": true,
      "logging": true,
      "assertions": true,
      "templates": true
    },
    "stringTypes": {
      "basic": true,
      "wide": true,
      "utf8": true,
      "utf16": true,
      "utf32": true,
      "raw": true
    }
  }
}
```

### 2. LLM Configuration

```json
{
  "llmValidation": {
    "enabled": true,
    "contextLines": 50,
    "useChunking": true,
    "cppSpecificRules": {
      "considerStringType": true,
      "considerContext": true,
      "considerFormatting": true
    }
  }
}
```

## Error Handling

### 1. Processing Errors
- Invalid file paths
- File access errors
- Malformed string literals
- Regex matching errors

### 2. Validation Errors
- LLM service errors
- Context extraction errors
- Location validation errors

### 3. Recovery Strategies
- Skip problematic files
- Log errors with context
- Continue processing other files
- Provide detailed error reports

## Performance Considerations

### 1. Optimization Strategies
- Compiled regex patterns
- Efficient string handling
- Caching of validation results
- Parallel processing of files

### 2. Resource Usage
- Memory usage monitoring
- File handle management
- LLM token usage optimization
- Disk I/O optimization

## Future Enhancements

### 1. Planned Features
- C++20 modules support
- Template metaprogramming text extraction
- Preprocessor macro expansion handling
- Binary resource extraction

### 2. Potential Improvements
- AST-based extraction
- Machine learning-based validation
- Custom skip pattern support
- Enhanced context analysis

## Migration and Compatibility

### 1. Integration with Existing System
- Shared interface implementation
- Common validation rules
- Unified configuration
- Consistent reporting

### 2. Backward Compatibility
- Support for existing file formats
- Compatibility with current validation rules
- Migration of existing configurations
- Support for legacy C++ standards

## Documentation

### 1. Code Documentation
- XML documentation comments
- Usage examples
- Configuration guide
- API reference

### 2. User Documentation
- Installation guide
- Configuration guide
- Usage examples
- Troubleshooting guide

## Security Considerations

### 1. File Access
- Safe file path handling
- Access control validation
- Resource cleanup
- Error logging

### 2. LLM Integration
- API key security
- Request validation
- Response validation
- Rate limiting

## LLM Integration Details

### 1. Existing LLM Infrastructure

The C++ text extraction processor will leverage the existing LLM infrastructure:

#### ILlmClient Interface
```csharp
public interface ILlmClient
{
    // Existing methods for C# and XAML
    List<ReplacementRecord> DiscoverAndProposeReplacements(...);
    TranslationVerificationResult VerifyTranslationNeeded(...);
    TranslationVerificationResult VerifyXamlTranslationNeeded(...);
    LLMConfig GetConfig();
}
```

#### Required Extensions
1. **New Method for C++ Verification**
   ```csharp
   TranslationVerificationResult VerifyCppTranslationNeeded(
       string text,
       string stringType,  // e.g., "char*", "wchar_t*", "u8string", etc.
       int lineNumber,
       string lineContent,
       string fileContent,
       string filePath,
       string groupId);
   ```

2. **C++-Specific Context**
   - File type (header/source)
   - String literal type
   - Surrounding code context
   - Preprocessor context
   - Comment context

### 2. LLM Configuration

#### Configuration Structure
```json
{
  "llmProvider": "LocalLlmProvider",
  "promptTemplate": "...",
  "localLlmProviderConfig": {
    "serviceUrl": "...",
    "modelId": "...",
    "temperature": 0.7,
    "maxRetries": 3,
    "timeoutSeconds": 30,
    "chunkSize": 300,
    "useChunking": true,
    "cppSpecificRules": {
      "considerStringType": true,
      "considerPreprocessor": true,
      "considerComments": true,
      "skipPatterns": [
        "LOG_*",
        "TRACE_*",
        "DEBUG_*",
        "assert",
        "ASSERT"
      ]
    }
  }
}
```

#### C++-Specific Prompt Templates
1. **Basic Verification**
   ```
   Analyze the following C++ code snippet and determine if the text needs translation:
   
   File: {filePath}
   Type: {fileType}
   String Type: {stringType}
   Line: {lineNumber}
   Text: "{text}"
   
   Context:
   {context}
   
   Consider:
   1. Is this user-facing text?
   2. Is this a system/internal string?
   3. Is this in a skipped context (logging, debug, etc.)?
   4. Is this a format string or template?
   ```

2. **Multi-line String Verification**
   ```
   Analyze the following C++ raw string literal and determine if it needs translation:
   
   File: {filePath}
   Type: {fileType}
   String Type: {stringType}
   Line: {lineNumber}
   Text: {rawString}
   
   Context:
   {context}
   
   Consider:
   1. Is this user-facing text?
   2. Is this a multi-line message or documentation?
   3. Is this a template or format string?
   4. Are there any special formatting requirements?
   ```

### 3. LLM Integration Points

#### 1. Text Discovery
- Use existing `DiscoverAndProposeReplacements` for initial text discovery
- Add C++-specific context to the discovery process
- Consider string literal types during discovery

#### 2. Text Verification
- Implement `VerifyCppTranslationNeeded` for C++-specific verification
- Use C++-specific prompt templates
- Include C++-specific context in verification requests

#### 3. Result Processing
- Handle C++-specific verification results
- Process string type information
- Handle multi-line string results
- Process preprocessor context

### 4. C++-Specific LLM Rules

#### 1. String Type Rules
- Consider string literal type (char*, wchar_t*, etc.)
- Handle different character encodings
- Consider string usage context

#### 2. Context Rules
- Preprocessor directive context
- Template context
- Macro context
- Comment context

#### 3. Skip Pattern Rules
- Logging macro patterns
- Debug assertion patterns
- System string patterns
- Configuration key patterns

### 5. LLM Integration Implementation

#### 1. LocalLlmClient Extension
```csharp
public class LocalLlmClient : ILlmClient
{
    // Existing implementation...

    public TranslationVerificationResult VerifyCppTranslationNeeded(
        string text,
        string stringType,
        int lineNumber,
        string lineContent,
        string fileContent,
        string filePath,
        string groupId)
    {
        // Implementation using existing LLM infrastructure
        // with C++-specific context and rules
    }
}
```

#### 2. C++ Processor Integration
```csharp
public class ConsolidatedCppProcessor
{
    private readonly ILlmClient _llmClient;
    
    private void VerifyReplacementsWithLlm(List<ReplacementRecord> replacements)
    {
        foreach (var replacement in replacements)
        {
            var result = _llmClient.VerifyCppTranslationNeeded(
                text: replacement.OriginalValue,
                stringType: replacement.StringType,
                lineNumber: replacement.LineNumber,
                lineContent: replacement.LineContent,
                fileContent: GetFileContent(replacement.FilePath),
                filePath: replacement.FilePath,
                groupId: replacement.GroupId
            );
            
            // Process verification result
        }
    }
}
```

### 6. LLM Performance Considerations

#### 1. Chunking Strategy
- Use existing chunking configuration
- Optimize chunk size for C++ files
- Handle multi-line strings in chunks

#### 2. Caching Strategy
- Cache verification results by string type
- Cache common skip patterns
- Cache preprocessor context

#### 3. Batch Processing
- Batch similar string types
- Batch by file context
- Batch by verification type

## Integration with Localization Extractor

### 1. ToolOptions Extension

```csharp
public class ToolOptions
{
    // ... existing options ...

    [Option('t', "fileTypes", Required = false, Default = "xaml",
        HelpText = "Which file types to extract. Valid values: 'xaml', 'cs', 'cpp', 'all'")]
    public string FileTypes { get; set; }

    [Option("cppProcessor", Required = false, Default = "consolidated",
        HelpText = "Which CppProcessor to use: 'regex', 'consolidated' (default, combines regex with LLM verification if config available), or 'llm'.")]
    public string CppProcessorOption { get; set; }

    [Option("cppSkipPatterns", Required = false,
        HelpText = "Additional C++ specific skip patterns (comma-separated).")]
    public string CppSkipPatterns { get; set; }

    [Option("cppStringTypes", Required = false, Default = "all",
        HelpText = "Which C++ string types to process: 'basic', 'wide', 'utf8', 'utf16', 'utf32', 'raw', 'all'.")]
    public string CppStringTypes { get; set; }

    // ... existing examples ...
    [Usage(ApplicationAlias = "LocalizeExtractor.exe")]
    public static IEnumerable<Example> Examples
    {
        get
        {
            // ... existing examples ...
            yield return new Example("Process C++ project with consolidated processor", new ToolOptions
            {
                ProjectPath = @"C:\Projects\MyCppApp",
                FileTypes = "cpp",
                CppProcessorOption = "consolidated",
                CppStringTypes = "all"
            });
        }
    }
}
```

### 2. ProjectTextExtractorConfig.json Structure

```json
{
  "projectInfo": {
    "name": "MyCppProject",
    "type": "cpp",
    "version": "1.0.0"
  },
  "extractionConfig": {
    "cppProcessor": {
      "type": "consolidated",
      "skipPatterns": [
        "LOG_*",
        "TRACE_*",
        "DEBUG_*",
        "assert",
        "ASSERT"
      ],
      "stringTypes": ["basic", "wide", "utf8", "utf16", "utf32", "raw"],
      "fileExtensions": [".cpp", ".h", ".hpp", ".cxx", ".hxx"]
    },
    "llmValidation": {
      "enabled": true,
      "config": {
        // ... existing LLM config ...
      }
    }
  },
  "extractedTexts": [
    {
      "filePath": "src/main.cpp",
      "lineNumber": 42,
      "column": 15,
      "text": "Hello World",
      "stringType": "char*",
      "context": "User message",
      "needsTranslation": true,
      "verificationResult": {
        "confidence": 0.95,
        "reason": "User-facing message in UI code",
        "suggestedKey": "main.hello_world"
      }
    }
  ]
}
```

## Implementation Plan

### Phase 1: Core Infrastructure (Week 1)

#### 1.1 Base Interface and Types
- [ ] Create `ITextProcessor` interface
- [ ] Create `ReplacementRecord` with enhanced context tracking
- [ ] Create `FileProcessingResult` with detailed location info
- [ ] Unit Tests:
  - [ ] Interface contract tests
  - [ ] Record type tests
  - [ ] Location tracking tests

#### 1.2 String Extraction
- [ ] Implement `RegexCppProcessor` for ALL string literals
- [ ] Add comprehensive string literal patterns
- [ ] Add context tracking
- [ ] Unit Tests:
  - [ ] String detection tests
  - [ ] Location tracking tests
  - [ ] Context capture tests

### Phase 2: LLM Integration (Week 2)

#### 2.1 LLM Client Extension
- [ ] Extend `ILlmClient` for C++
- [ ] Implement `VerifyCppTranslationNeeded`
- [ ] Add C++-specific prompt templates
- [ ] Unit Tests:
  - [ ] LLM client interface tests
  - [ ] Verification result tests
  - [ ] Prompt template tests

#### 2.2 LLM Configuration
- [ ] Extend `LLMConfig` for C++
- [ ] Add C++-specific rules
- [ ] Implement configuration loading
- [ ] Unit Tests:
  - [ ] Config loading tests
  - [ ] Rule validation tests
  - [ ] Configuration merge tests

### Phase 3: Consolidated Processor (Week 3)

#### 3.1 Processor Implementation
- [ ] Implement `ConsolidatedCppProcessor`
- [ ] Add result consolidation logic
- [ ] Implement LLM verification integration
- [ ] Unit Tests:
  - [ ] Processor integration tests
  - [ ] Result consolidation tests
  - [ ] Verification integration tests

#### 3.2 Configuration Generation
- [ ] Implement `ProjectTextExtractorConfig` generation
- [ ] Add C++-specific configuration
- [ ] Add validation result storage
- [ ] Unit Tests:
  - [ ] Config generation tests
  - [ ] Validation storage tests
  - [ ] Config validation tests

### Phase 4: Integration and Testing (Week 4)

#### 4.1 Extractor Integration
- [ ] Integrate with `LocalizationExtractor`
- [ ] Add C++ project support
- [ ] Implement file type detection
- [ ] Integration Tests:
  - [ ] End-to-end extraction tests
  - [ ] Project processing tests
  - [ ] Config generation tests

#### 4.2 Performance Testing
- [ ] Implement performance benchmarks
- [ ] Add memory usage monitoring
- [ ] Add processing time tracking
- [ ] Performance Tests:
  - [ ] Large file processing
  - [ ] Memory usage tests
  - [ ] Processing speed tests

### Phase 5: Documentation and Examples (Week 5)

#### 5.1 Documentation
- [ ] Update user documentation
- [ ] Add API documentation
- [ ] Create usage examples
- [ ] Documentation Tests:
  - [ ] Example validation
  - [ ] Documentation completeness
  - [ ] API coverage

#### 5.2 Example Projects
- [ ] Create sample C++ projects
- [ ] Add test scenarios
- [ ] Create migration examples
- [ ] Example Tests:
  - [ ] Sample project tests
  - [ ] Migration tests
  - [ ] Edge case tests

## Test Coverage Requirements

### 1. Unit Test Coverage
- Minimum 90% code coverage using NUnit test coverage tools
- All public interfaces and methods
- All error conditions and edge cases
- All configuration options and variations

### 2. Test Categories and NUnit Attributes
```csharp
[TestFixture]
public class RegexCppProcessorTests
{
    // Basic setup and teardown
    [SetUp]
    public void Setup() { ... }

    [TearDown]
    public void TearDown() { ... }

    // Basic functionality tests
    [Test]
    public void ProcessCppFile_WithRegularStringLiterals_FindsAllStrings() { ... }

    // Skip pattern tests
    [Test]
    public void ProcessCppFile_SkipsStringsInComments() { ... }

    // String type tests
    [Test]
    public void GetStringType_ReturnsCorrectTypeForDifferentStringLiterals() { ... }

    // Error handling tests
    [Test]
    public void ProcessCppFile_WithNonExistentFile_ThrowsFileNotFoundException() { ... }
}
```

## Success Criteria

### 1. Functional Requirements
- [ ] Successfully extracts ALL string literals from C++ files
- [ ] Accurately tracks string locations and context
- [ ] Properly identifies string types and encodings
- [ ] Integrates with LLM validation
- [ ] Generates comprehensive extraction reports

### 2. Non-Functional Requirements
- [ ] Meets performance benchmarks
- [ ] Maintains memory usage limits
- [ ] Provides clear error messages
- [ ] Generates detailed context reports

### 3. Quality Requirements
- [ ] Meets test coverage targets
- [ ] Passes all integration tests
- [ ] Follows coding standards
- [ ] Complete documentation

## Conclusion

The C++ Text Extraction Processor provides a robust solution for extracting ALL string literals from C++ source files, with accurate location tracking and context information. The LLM validation layer then determines which strings need translation, providing a more accurate and maintainable approach to localization.

The implementation follows a modular design that separates concerns:
1. String extraction (RegexCppProcessor)
2. Translation need validation (LLM)
3. Result consolidation (ConsolidatedCppProcessor)

This separation ensures accurate string detection while leveraging LLM for intelligent translation need assessment. 