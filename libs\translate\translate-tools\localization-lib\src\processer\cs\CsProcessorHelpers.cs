/*
 * Copyright 2021-present Alcon. All rights reserved.
 * Alcon proprietary/confidential.
 * Use is subject to license terms.
 */

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace LocalizationLib;

public static class CsProcessorHelpers
{
    /// <summary>
    /// Build line-indexed code from an array of lines, e.g.:
    /// 1: using System;
    /// 2: namespace Demo
    /// ...
    /// </summary>
    public static string BuildLineIndexedCode(string[] lines)
    {
        var sb = new StringBuilder();
        for (int i = 0; i < lines.Length; i++)
        {
            sb.Append(i + 1).Append(": ").AppendLine(lines[i]);
        }
        return sb.ToString();
    }

    /// <summary>
    /// For each ReplacementRecord, we use rec.LineContent to find the best matching line
    /// in allRealLines, then fix rec.LineNumber, rec.Column accordingly.
    /// </summary>
    public static void FixLineAndColumn(List<ReplacementRecord> recs, string[] allRealLines)
    {
        foreach (var rec in recs)
        {
            string pureLine = ExtractPureLine(rec.LineContent);
            int bestLineIdx = FindBestMatchingLine(allRealLines, pureLine);
            if (bestLineIdx < 0) continue;

            rec.LineNumber = bestLineIdx + 1; // 1-based
            string toFind = $"\"{rec.OriginalValue}\"";
            int col = allRealLines[bestLineIdx].IndexOf(toFind);
            if (col >= 0) rec.Column = col + 1;
        }
    }

    /// <summary>
    /// Actually modifies the text, inserting e.g. Translator.Instance.TranslateM("en:originalValue").
    /// Uses the corrected rec.LineNumber and rec.Column (if you choose).
    /// By default, it just finds rec.OriginalValue in the line (like your naive approach).
    /// </summary>
    public static string DoReplace(
        string originalContent, 
        List<ReplacementRecord> recs)
    {
        var lines = originalContent.Split('\n').ToList();
        // sort descending lineNumber so we don't shift indexes
        var sorted = recs.OrderByDescending(r => r.LineNumber).ToList();

        foreach (var rec in sorted)
        {
            int lineIndex = rec.LineNumber - 1;
            if (lineIndex < 0 || lineIndex >= lines.Count) continue;

            string lineText = lines[lineIndex];
            string toFind = $"\"{rec.OriginalValue}\"";
            int idx = lineText.IndexOf(toFind);
            if (idx >= 0)
            {
                string translatorCall = $"Translator.Instance.TranslateM(\"{rec.NewKey}\")";
                lineText = lineText.Remove(idx, toFind.Length)
                                   .Insert(idx, translatorCall);
                lines[lineIndex] = lineText;
            }
        }

        return string.Join('\n', lines);
    }

    // private helpers
    private static string ExtractPureLine(string lineTextFromLlm)
    {
        int idx = lineTextFromLlm.IndexOf(':');
        if (idx >= 0 && idx + 2 < lineTextFromLlm.Length)
        {
            return lineTextFromLlm.Substring(idx + 2);
        }
        return lineTextFromLlm;
    }

    private static int FindBestMatchingLine(string[] realLines, string candidateLine)
    {
        double bestScore = 0.0;
        int bestIndex = -1;
        for (int i = 0; i < realLines.Length; i++)
        {
            double score = ComputeSimilarity(realLines[i], candidateLine);
            if (score > bestScore)
            {
                bestScore = score;
                bestIndex = i;
            }
        }
        if (bestScore < 0.5) return -1;
        return bestIndex;
    }

    private static double ComputeSimilarity(string a, string b)
    {
        int lcs = LongestCommonSubsequence(a, b);
        double denom = (a.Length + b.Length) / 2.0;
        if (denom == 0) return 1.0;
        return (lcs / denom);
    }

    private static int LongestCommonSubsequence(string s1, string s2)
    {
        var dp = new int[s1.Length + 1, s2.Length + 1];
        for (int i = 1; i <= s1.Length; i++)
        {
            for (int j = 1; j <= s2.Length; j++)
            {
                if (s1[i - 1] == s2[j - 1])
                    dp[i, j] = dp[i - 1, j - 1] + 1;
                else
                    dp[i, j] = Math.Max(dp[i - 1, j], dp[i, j - 1]);
            }
        }
        return dp[s1.Length, s2.Length];
    }
}
