/*
 * Copyright 2021-present Alcon. All rights reserved.
 * Alcon proprietary/confidential.
 * Use is subject to license terms.
 */

using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using Newtonsoft.Json;

namespace LocalizationLib.Llm
{
    /// <summary>
    /// Simplified LLM verification cache system that stores results based on relative paths
    /// and tracks LLM server specifications for cache validation.
    /// </summary>
    public class LlmVerificationCache
    {
        private readonly string _projectPath;
        private readonly string _cacheFile;
        private Dictionary<string, CachedVerificationResult> _cache;
        private readonly object _cacheLock = new object();

        public LlmVerificationCache(string projectPath)
        {
            _projectPath = projectPath;
            
            // Single cache file in project directory
            _cacheFile = Path.Combine(projectPath, ".llmcache.json");
            
            LoadCache();
        }

        /// <summary>
        /// Gets a verification result from cache
        /// </summary>
        public CachedVerificationResult GetVerificationResult(string text, string filePath, int lineNumber, int columnNumber, LlmServerSpec llmSpec)
        {
            string key = GenerateCacheKey(text, filePath, lineNumber, columnNumber, llmSpec);

            lock (_cacheLock)
            {
                return _cache.TryGetValue(key, out var result) ? result : null;
            }
        }

        /// <summary>
        /// Adds a verification result to cache
        /// </summary>
        public void AddVerificationResult(string text, string filePath, int lineNumber, int columnNumber,
            bool needsTranslation, string reason, string context, ExtractionSource source, LlmServerSpec llmSpec, string lineContent = null)
        {
            string key = GenerateCacheKey(text, filePath, lineNumber, columnNumber, llmSpec);
            string relativePath = GetRelativePath(filePath);

            var cachedResult = new CachedVerificationResult
            {
                Text = NormalizeTextForCaching(text), // Store normalized text to match key generation
                NeedsTranslation = needsTranslation,
                Reason = reason,
                Context = context,
                Timestamp = DateTime.UtcNow,
                Source = source,
                RelativeFilePath = relativePath,
                LineNumber = lineNumber,
                ColumnNumber = columnNumber,
                LineContent = lineContent,
                LlmServerSpec = llmSpec,
                CacheVersion = GetCurrentCacheVersion()
            };

            lock (_cacheLock)
            {
                _cache[key] = cachedResult;
                SaveCache();
            }
        }

        /// <summary>
        /// Clears expired cache entries (older than specified days)
        /// </summary>
        public void CleanExpiredEntries(int maxAgeDays = 30)
        {
            var cutoffDate = DateTime.UtcNow.AddDays(-maxAgeDays);
            
            lock (_cacheLock)
            {
                var keysToRemove = new List<string>();
                foreach (var kvp in _cache)
                {
                    if (kvp.Value.Timestamp < cutoffDate)
                    {
                        keysToRemove.Add(kvp.Key);
                    }
                }

                foreach (var key in keysToRemove)
                {
                    _cache.Remove(key);
                }

                if (keysToRemove.Count > 0)
                {
                    SaveCache();
                }
            }
        }

        /// <summary>
        /// Gets cache statistics
        /// </summary>
        public CacheStatistics GetStatistics()
        {
            lock (_cacheLock)
            {
                var stats = new CacheStatistics
                {
                    TotalEntries = _cache.Count,
                    EntriesNeedingTranslation = 0,
                    EntriesSkippingTranslation = 0,
                    OldestEntry = DateTime.MaxValue,
                    NewestEntry = DateTime.MinValue
                };

                foreach (var result in _cache.Values)
                {
                    if (result.NeedsTranslation)
                        stats.EntriesNeedingTranslation++;
                    else
                        stats.EntriesSkippingTranslation++;

                    if (result.Timestamp < stats.OldestEntry)
                        stats.OldestEntry = result.Timestamp;
                    if (result.Timestamp > stats.NewestEntry)
                        stats.NewestEntry = result.Timestamp;

                    // Track by provider and model
                    if (result.LlmServerSpec != null)
                    {
                        var provider = result.LlmServerSpec.Provider ?? "unknown";
                        var model = result.LlmServerSpec.Model ?? "unknown";
                        
                        stats.EntriesByProvider[provider] = stats.EntriesByProvider.GetValueOrDefault(provider, 0) + 1;
                        stats.EntriesByModel[model] = stats.EntriesByModel.GetValueOrDefault(model, 0) + 1;
                    }
                }

                return stats;
            }
        }

        private void LoadCache()
        {
            _cache = new Dictionary<string, CachedVerificationResult>();

            if (File.Exists(_cacheFile))
            {
                try
                {
                    var json = File.ReadAllText(_cacheFile);
                    var loadedCache = JsonConvert.DeserializeObject<Dictionary<string, CachedVerificationResult>>(json);
                    if (loadedCache != null)
                    {
                        _cache = loadedCache;
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"[WARNING] Failed to load LLM cache: {ex.Message}");
                }
            }
        }

        private void SaveCache()
        {
            try
            {
                var json = JsonConvert.SerializeObject(_cache, Formatting.Indented);
                File.WriteAllText(_cacheFile, json);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[WARNING] Failed to save LLM cache: {ex.Message}");
            }
        }

        private string GenerateCacheKey(string text, string filePath, int lineNumber, int columnNumber, LlmServerSpec llmSpec)
        {
            string relativePath = GetRelativePath(filePath);
            return GenerateCacheKeyFromRelativePath(text, relativePath, lineNumber, columnNumber, llmSpec);
        }

        /// <summary>
        /// Generates a cache key for LLM verification results using relative path (static version for external use)
        /// </summary>
        public static string GenerateCacheKeyFromRelativePath(string text, string relativePath, int lineNumber, int columnNumber, LlmServerSpec llmSpec)
        {
            // Normalize text to handle XML encoding differences
            string normalizedText = NormalizeTextForCaching(text);
            var keyData = $"{normalizedText}|{relativePath}|{lineNumber}|{columnNumber}|{llmSpec.Provider}|{llmSpec.Model}|{llmSpec.Version}";
            using (var sha256 = SHA256.Create())
            {
                var hashBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(keyData));
                return Convert.ToBase64String(hashBytes).Replace("/", "_").Replace("+", "-").Substring(0, 16);
            }
        }

        /// <summary>
        /// Normalizes text for consistent cache key generation by handling XML encoding differences
        /// </summary>
        private static string NormalizeTextForCaching(string text)
        {
            if (string.IsNullOrEmpty(text))
                return text;

            // Use standard XML/HTML entity decoding to handle ALL XML entities automatically
            // Ensures consistent text representation for cache key generation and lookup
            // Covers all named entities (&lt;, &gt;, &amp;, etc.) and numeric entities (&#8592;, &#038;, etc.)
            return System.Net.WebUtility.HtmlDecode(text);
        }

        private string GetRelativePath(string filePath)
        {
            return GetRelativePath(filePath, _projectPath);
        }

        /// <summary>
        /// Gets relative path from project path (static version for external use)
        /// </summary>
        public static string GetRelativePath(string filePath, string projectPath)
        {
            if (string.IsNullOrEmpty(filePath))
                return "";

            try
            {
                var fullProjectPath = Path.GetFullPath(projectPath);
                var fullFilePath = Path.GetFullPath(filePath);

                if (fullFilePath.StartsWith(fullProjectPath, StringComparison.OrdinalIgnoreCase))
                {
                    return Path.GetRelativePath(fullProjectPath, fullFilePath);
                }

                return Path.GetFileName(filePath); // Fallback to filename only
            }
            catch
            {
                return Path.GetFileName(filePath);
            }
        }

        private string GetCurrentCacheVersion()
        {
            // Version can be updated when prompt changes significantly
            return "v2.1";
        }
    }

    /// <summary>
    /// LLM server specification for cache validation
    /// </summary>
    public class LlmServerSpec
    {
        public string Provider { get; set; } // "local", "bedrock", etc.
        public string Model { get; set; } // Model name/ID
        public string Version { get; set; } // API version or model version
        public string Endpoint { get; set; } // Server endpoint (for local)
        
        public override string ToString()
        {
            return $"{Provider}:{Model}:{Version}";
        }
    }

    /// <summary>
    /// Cached verification result with LLM server specification
    /// </summary>
    public class CachedVerificationResult
    {
        public string Text { get; set; }
        public bool NeedsTranslation { get; set; }
        public string Reason { get; set; }
        public string Context { get; set; }
        public DateTime Timestamp { get; set; }
        public ExtractionSource Source { get; set; }
        public string RelativeFilePath { get; set; } // Relative to project root
        public int LineNumber { get; set; }
        public int ColumnNumber { get; set; }
        public string LineContent { get; set; } // The full line content where the text was found
        public LlmServerSpec LlmServerSpec { get; set; }
        public string CacheVersion { get; set; }
    }

    /// <summary>
    /// Cache statistics for monitoring and management
    /// </summary>
    public class CacheStatistics
    {
        public int TotalEntries { get; set; }
        public int EntriesNeedingTranslation { get; set; }
        public int EntriesSkippingTranslation { get; set; }
        public DateTime OldestEntry { get; set; }
        public DateTime NewestEntry { get; set; }
        public Dictionary<string, int> EntriesByProvider { get; set; } = new();
        public Dictionary<string, int> EntriesByModel { get; set; } = new();
    }
}
