﻿
<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup Label="Globals">
    <ProjectGuid>{D4859805-07F9-3863-BD39-0B33241A7E06}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <UseWPF>true</UseWPF>
    <Configurations>Debug;Release</Configurations>
    <EnableDefaultItems>false</EnableDefaultItems>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
    <ManagedAssembly>true</ManagedAssembly>
    <TargetFramework>net48</TargetFramework>
    <AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
    <OutputType>Library</OutputType>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)' == 'Debug'">
    <OutputPath>D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\build\Debug\</OutputPath>
    <AssemblyName>translate_csharp</AssemblyName>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    <DebugType>full</DebugType>
    <DefineConstants>DEBUG;_DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <LangVersion>latest</LangVersion>
    <Optimize>false</Optimize>
    <WarningLevel>3</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)' == 'Release'">
    <OutputPath>D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\build\Release\</OutputPath>
    <AssemblyName>translate_csharp</AssemblyName>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    <DebugType>none</DebugType>
    <DefineConstants>_DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR</DefineConstants>
    <ErrorReport>queue</ErrorReport>
    <LangVersion>latest</LangVersion>
    <Optimize>true</Optimize>
    <WarningLevel>1</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <None Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\CMakeLists.txt">
      <Link>CMakeLists.txt</Link>
    </None>
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeCCompiler.cmake.in" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeCCompilerABI.c" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeCInformation.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeCSharpCompiler.cmake.in" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeCSharpInformation.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeCXXCompiler.cmake.in" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeCXXCompilerABI.cpp" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeCXXInformation.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeCommonLanguageInclude.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeCompilerIdDetection.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeDetermineCCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeDetermineCSharpCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeDetermineCXXCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeDetermineCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeDetermineCompilerABI.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeDetermineCompilerId.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeDetermineCompilerSupport.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeDetermineRCCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeDetermineSystem.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeFindBinUtils.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeGenericSystem.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeInitializeConfigs.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeLanguageInformation.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeParseImplicitIncludeInfo.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeParseImplicitLinkInfo.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeParseLibraryArchitecture.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeRCCompiler.cmake.in" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeRCInformation.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeSystem.cmake.in" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeSystemSpecificInformation.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeSystemSpecificInitialize.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeTestCCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeTestCSharpCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeTestCXXCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeTestCompilerCommon.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeTestRCCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\ADSP-DetermineCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\ARMCC-DetermineCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\ARMClang-DetermineCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\AppleClang-DetermineCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\Borland-DetermineCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\Bruce-C-DetermineCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\CMakeCommonCompilerMacros.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\Clang-DetermineCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\Clang-DetermineCompilerInternal.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\Compaq-C-DetermineCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\Compaq-CXX-DetermineCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\Cray-DetermineCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\CrayClang-DetermineCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\Embarcadero-DetermineCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\Fujitsu-DetermineCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\FujitsuClang-DetermineCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\GHS-DetermineCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\GNU-C-DetermineCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\GNU-CXX-DetermineCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\HP-C-DetermineCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\HP-CXX-DetermineCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\IAR-DetermineCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\IBMCPP-C-DetermineVersionInternal.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\IBMCPP-CXX-DetermineVersionInternal.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\IBMClang-C-DetermineCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\IBMClang-CXX-DetermineCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\Intel-DetermineCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\IntelLLVM-DetermineCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\LCC-C-DetermineCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\LCC-CXX-DetermineCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\MSVC-C.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\MSVC-CXX.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\MSVC-DetermineCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\MSVC.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\NVHPC-DetermineCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\NVIDIA-DetermineCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\OpenWatcom-DetermineCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\OrangeC-DetermineCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\PGI-DetermineCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\PathScale-DetermineCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\SCO-DetermineCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\SDCC-C-DetermineCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\SunPro-C-DetermineCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\SunPro-CXX-DetermineCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\TI-DetermineCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\TIClang-DetermineCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\Tasking-DetermineCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\TinyCC-C-DetermineCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\VisualAge-C-DetermineCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\VisualAge-CXX-DetermineCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\Watcom-DetermineCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\XL-C-DetermineCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\XL-CXX-DetermineCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\XLClang-C-DetermineCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\XLClang-CXX-DetermineCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\zOS-C-DetermineCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\zOS-CXX-DetermineCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CompilerId\VS-10.csproj.in" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CompilerId\VS-10.vcxproj.in" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\FindGit.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\FindPackageHandleStandardArgs.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\FindPackageMessage.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Internal\CMakeCLinkerInformation.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Internal\CMakeCXXLinkerInformation.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Internal\CMakeCommonLinkerInformation.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Internal\CMakeDetermineLinkerId.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Internal\FeatureTesting.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Linker\MSVC-C.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Linker\MSVC-CXX.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Linker\MSVC.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC-C.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC-CXX.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Windows-Determine-CXX.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Windows-Initialize.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Windows-MSVC-C.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Windows-MSVC-CXX.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Windows-MSVC.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Windows.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\WindowsPaths.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="D:\kibisoft\workarea\git\interop\cmake\Globals.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="D:\kibisoft\workarea\git\interop\cmake\SetupNugetEpilogue.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="D:\kibisoft\workarea\git\interop\cmake\SetupNugetPrologue.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="D:\kibisoft\workarea\git\interop\cmake\VsPackageReferences.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\CMakeLists.txt.win" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\build\CMakeFiles\4.0.0-rc4\CMakeCCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\build\CMakeFiles\4.0.0-rc4\CMakeCSharpCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\build\CMakeFiles\4.0.0-rc4\CMakeCXXCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\build\CMakeFiles\4.0.0-rc4\CMakeRCCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\build\CMakeFiles\4.0.0-rc4\CMakeSystem.cmake" />
  </ItemGroup>
  <Target Condition="'$(Configuration)' == 'Debug'"
    Name="CustomCommand_Debug_10c1b7447a19b6e31ea60b6bfe7d94ab"
    Inputs="D:/kibisoft/workarea/git/interop/libs/translate/translate-csharp/CMakeLists.txt;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeCCompiler.cmake.in;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeCCompilerABI.c;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeCInformation.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeCSharpCompiler.cmake.in;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeCSharpInformation.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeCXXCompiler.cmake.in;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeCXXCompilerABI.cpp;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeCXXInformation.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeCompilerIdDetection.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeDetermineCCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeDetermineCSharpCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeDetermineCXXCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeDetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeDetermineCompilerABI.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeDetermineCompilerId.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeDetermineCompilerSupport.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeDetermineRCCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeDetermineSystem.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeFindBinUtils.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeGenericSystem.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeLanguageInformation.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeParseImplicitIncludeInfo.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeParseImplicitLinkInfo.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeParseLibraryArchitecture.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeRCCompiler.cmake.in;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeRCInformation.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeSystem.cmake.in;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeTestCCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeTestCSharpCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeTestCXXCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeTestCompilerCommon.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeTestRCCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\ADSP-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\ARMCC-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\ARMClang-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\AppleClang-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\Borland-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\Bruce-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\Clang-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\Clang-DetermineCompilerInternal.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\Compaq-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\Compaq-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\Cray-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\CrayClang-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\Embarcadero-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\Fujitsu-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\FujitsuClang-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\GHS-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\GNU-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\GNU-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\HP-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\HP-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\IAR-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\IBMCPP-C-DetermineVersionInternal.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\IBMCPP-CXX-DetermineVersionInternal.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\IBMClang-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\IBMClang-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\Intel-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\IntelLLVM-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\LCC-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\LCC-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\MSVC-C.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\MSVC-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\MSVC.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\NVHPC-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\NVIDIA-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\OpenWatcom-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\OrangeC-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\PGI-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\PathScale-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\SCO-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\SDCC-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\SunPro-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\SunPro-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\TI-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\TIClang-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\Tasking-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\TinyCC-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\VisualAge-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\VisualAge-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\Watcom-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\XL-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\XL-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\XLClang-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\XLClang-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\zOS-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\zOS-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CompilerId\VS-10.csproj.in;C:\Program Files\CMake\share\cmake-4.0\Modules\CompilerId\VS-10.vcxproj.in;C:\Program Files\CMake\share\cmake-4.0\Modules\FindGit.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Internal\CMakeCLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Internal\CMakeCXXLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Internal\CMakeCommonLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Internal\CMakeDetermineLinkerId.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Internal\FeatureTesting.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Linker\MSVC-C.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Linker\MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Linker\MSVC.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC-C.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Windows-Determine-CXX.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Windows-Initialize.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Windows-MSVC-C.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Windows.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\WindowsPaths.cmake;D:\kibisoft\workarea\git\interop\cmake\Globals.cmake;D:\kibisoft\workarea\git\interop\cmake\SetupNugetEpilogue.cmake;D:\kibisoft\workarea\git\interop\cmake\SetupNugetPrologue.cmake;D:\kibisoft\workarea\git\interop\cmake\VsPackageReferences.cmake;D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\CMakeLists.txt.win;D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\build\CMakeFiles\4.0.0-rc4\CMakeCCompiler.cmake;D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\build\CMakeFiles\4.0.0-rc4\CMakeCSharpCompiler.cmake;D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\build\CMakeFiles\4.0.0-rc4\CMakeCXXCompiler.cmake;D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\build\CMakeFiles\4.0.0-rc4\CMakeRCCompiler.cmake;D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\build\CMakeFiles\4.0.0-rc4\CMakeSystem.cmake"
    Outputs="D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\build\CMakeFiles\generate.stamp"
    BeforeTargets="CoreCompile"
    DependsOnTargets="PrepareForBuild">
    <Exec Command="echo Building Custom Rule D:/kibisoft/workarea/git/interop/libs/translate/translate-csharp/CMakeLists.txt" />
    <Exec Command="setlocal&#10;&quot;C:\Program Files\CMake\bin\cmake.exe&quot; -SD:/kibisoft/workarea/git/interop/libs/translate/translate-csharp -BD:/kibisoft/workarea/git/interop/libs/translate/translate-csharp/build --check-stamp-file D:/kibisoft/workarea/git/interop/libs/translate/translate-csharp/build/CMakeFiles/generate.stamp&#10;if %errorlevel% neq 0 goto :cmEnd&#10;:cmEnd&#10;endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone&#10;:cmErrorLevel&#10;exit /b %1&#10;:cmDone&#10;if %errorlevel% neq 0 goto :VCEnd&#10;:VCEnd" />
  </Target>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeCCompiler.cmake.in" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeCCompilerABI.c" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeCInformation.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeCSharpCompiler.cmake.in" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeCSharpInformation.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeCXXCompiler.cmake.in" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeCXXCompilerABI.cpp" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeCXXInformation.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeCommonLanguageInclude.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeCompilerIdDetection.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeDetermineCCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeDetermineCSharpCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeDetermineCXXCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeDetermineCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeDetermineCompilerABI.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeDetermineCompilerId.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeDetermineCompilerSupport.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeDetermineRCCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeDetermineSystem.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeFindBinUtils.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeGenericSystem.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeInitializeConfigs.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeLanguageInformation.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeParseImplicitIncludeInfo.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeParseImplicitLinkInfo.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeParseLibraryArchitecture.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeRCCompiler.cmake.in" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeRCInformation.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeSystem.cmake.in" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeSystemSpecificInformation.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeSystemSpecificInitialize.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeTestCCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeTestCSharpCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeTestCXXCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeTestCompilerCommon.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeTestRCCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\ADSP-DetermineCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\ARMCC-DetermineCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\ARMClang-DetermineCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\AppleClang-DetermineCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\Borland-DetermineCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\Bruce-C-DetermineCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\CMakeCommonCompilerMacros.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\Clang-DetermineCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\Clang-DetermineCompilerInternal.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\Compaq-C-DetermineCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\Compaq-CXX-DetermineCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\Cray-DetermineCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\CrayClang-DetermineCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\Embarcadero-DetermineCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\Fujitsu-DetermineCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\FujitsuClang-DetermineCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\GHS-DetermineCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\GNU-C-DetermineCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\GNU-CXX-DetermineCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\HP-C-DetermineCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\HP-CXX-DetermineCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\IAR-DetermineCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\IBMCPP-C-DetermineVersionInternal.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\IBMCPP-CXX-DetermineVersionInternal.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\IBMClang-C-DetermineCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\IBMClang-CXX-DetermineCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\Intel-DetermineCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\IntelLLVM-DetermineCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\LCC-C-DetermineCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\LCC-CXX-DetermineCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\MSVC-C.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\MSVC-CXX.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\MSVC-DetermineCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\MSVC.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\NVHPC-DetermineCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\NVIDIA-DetermineCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\OpenWatcom-DetermineCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\OrangeC-DetermineCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\PGI-DetermineCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\PathScale-DetermineCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\SCO-DetermineCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\SDCC-C-DetermineCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\SunPro-C-DetermineCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\SunPro-CXX-DetermineCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\TI-DetermineCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\TIClang-DetermineCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\Tasking-DetermineCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\TinyCC-C-DetermineCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\VisualAge-C-DetermineCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\VisualAge-CXX-DetermineCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\Watcom-DetermineCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\XL-C-DetermineCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\XL-CXX-DetermineCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\XLClang-C-DetermineCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\XLClang-CXX-DetermineCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\zOS-C-DetermineCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\zOS-CXX-DetermineCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CompilerId\VS-10.csproj.in" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CompilerId\VS-10.vcxproj.in" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\FindGit.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\FindPackageHandleStandardArgs.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\FindPackageMessage.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Internal\CMakeCLinkerInformation.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Internal\CMakeCXXLinkerInformation.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Internal\CMakeCommonLinkerInformation.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Internal\CMakeDetermineLinkerId.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Internal\FeatureTesting.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Linker\MSVC-C.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Linker\MSVC-CXX.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Linker\MSVC.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC-C.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC-CXX.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Windows-Determine-CXX.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Windows-Initialize.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Windows-MSVC-C.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Windows-MSVC-CXX.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Windows-MSVC.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Windows.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\WindowsPaths.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="D:\kibisoft\workarea\git\interop\cmake\Globals.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="D:\kibisoft\workarea\git\interop\cmake\SetupNugetEpilogue.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="D:\kibisoft\workarea\git\interop\cmake\SetupNugetPrologue.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="D:\kibisoft\workarea\git\interop\cmake\VsPackageReferences.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\CMakeLists.txt.win" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\build\CMakeFiles\4.0.0-rc4\CMakeCCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\build\CMakeFiles\4.0.0-rc4\CMakeCSharpCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\build\CMakeFiles\4.0.0-rc4\CMakeCXXCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\build\CMakeFiles\4.0.0-rc4\CMakeRCCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\build\CMakeFiles\4.0.0-rc4\CMakeSystem.cmake" />
  </ItemGroup>
  <Target Condition="'$(Configuration)' == 'Release'"
    Name="CustomCommand_Release_10c1b7447a19b6e31ea60b6bfe7d94ab"
    Inputs="D:/kibisoft/workarea/git/interop/libs/translate/translate-csharp/CMakeLists.txt;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeCCompiler.cmake.in;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeCCompilerABI.c;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeCInformation.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeCSharpCompiler.cmake.in;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeCSharpInformation.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeCXXCompiler.cmake.in;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeCXXCompilerABI.cpp;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeCXXInformation.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeCompilerIdDetection.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeDetermineCCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeDetermineCSharpCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeDetermineCXXCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeDetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeDetermineCompilerABI.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeDetermineCompilerId.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeDetermineCompilerSupport.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeDetermineRCCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeDetermineSystem.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeFindBinUtils.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeGenericSystem.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeLanguageInformation.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeParseImplicitIncludeInfo.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeParseImplicitLinkInfo.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeParseLibraryArchitecture.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeRCCompiler.cmake.in;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeRCInformation.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeSystem.cmake.in;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeTestCCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeTestCSharpCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeTestCXXCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeTestCompilerCommon.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeTestRCCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\ADSP-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\ARMCC-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\ARMClang-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\AppleClang-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\Borland-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\Bruce-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\Clang-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\Clang-DetermineCompilerInternal.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\Compaq-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\Compaq-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\Cray-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\CrayClang-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\Embarcadero-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\Fujitsu-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\FujitsuClang-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\GHS-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\GNU-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\GNU-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\HP-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\HP-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\IAR-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\IBMCPP-C-DetermineVersionInternal.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\IBMCPP-CXX-DetermineVersionInternal.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\IBMClang-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\IBMClang-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\Intel-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\IntelLLVM-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\LCC-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\LCC-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\MSVC-C.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\MSVC-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\MSVC.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\NVHPC-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\NVIDIA-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\OpenWatcom-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\OrangeC-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\PGI-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\PathScale-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\SCO-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\SDCC-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\SunPro-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\SunPro-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\TI-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\TIClang-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\Tasking-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\TinyCC-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\VisualAge-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\VisualAge-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\Watcom-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\XL-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\XL-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\XLClang-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\XLClang-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\zOS-C-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Compiler\zOS-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CompilerId\VS-10.csproj.in;C:\Program Files\CMake\share\cmake-4.0\Modules\CompilerId\VS-10.vcxproj.in;C:\Program Files\CMake\share\cmake-4.0\Modules\FindGit.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Internal\CMakeCLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Internal\CMakeCXXLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Internal\CMakeCommonLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Internal\CMakeDetermineLinkerId.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Internal\FeatureTesting.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Linker\MSVC-C.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Linker\MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Linker\MSVC.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC-C.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Linker\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Windows-Determine-CXX.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Windows-Initialize.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Windows-MSVC-C.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Windows.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\WindowsPaths.cmake;D:\kibisoft\workarea\git\interop\cmake\Globals.cmake;D:\kibisoft\workarea\git\interop\cmake\SetupNugetEpilogue.cmake;D:\kibisoft\workarea\git\interop\cmake\SetupNugetPrologue.cmake;D:\kibisoft\workarea\git\interop\cmake\VsPackageReferences.cmake;D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\CMakeLists.txt.win;D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\build\CMakeFiles\4.0.0-rc4\CMakeCCompiler.cmake;D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\build\CMakeFiles\4.0.0-rc4\CMakeCSharpCompiler.cmake;D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\build\CMakeFiles\4.0.0-rc4\CMakeCXXCompiler.cmake;D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\build\CMakeFiles\4.0.0-rc4\CMakeRCCompiler.cmake;D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\build\CMakeFiles\4.0.0-rc4\CMakeSystem.cmake"
    Outputs="D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\build\CMakeFiles\generate.stamp"
    BeforeTargets="CoreCompile"
    DependsOnTargets="PrepareForBuild">
    <Exec Command="echo Building Custom Rule D:/kibisoft/workarea/git/interop/libs/translate/translate-csharp/CMakeLists.txt" />
    <Exec Command="setlocal&#10;&quot;C:\Program Files\CMake\bin\cmake.exe&quot; -SD:/kibisoft/workarea/git/interop/libs/translate/translate-csharp -BD:/kibisoft/workarea/git/interop/libs/translate/translate-csharp/build --check-stamp-file D:/kibisoft/workarea/git/interop/libs/translate/translate-csharp/build/CMakeFiles/generate.stamp&#10;if %errorlevel% neq 0 goto :cmEnd&#10;:cmEnd&#10;endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone&#10;:cmErrorLevel&#10;exit /b %1&#10;:cmDone&#10;if %errorlevel% neq 0 goto :VCEnd&#10;:VCEnd" />
  </Target>
  <ItemGroup>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\src\Translator.cs">
      <Link>src\Translator.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\src\Helper\LanguageHelper.cs">
      <Link>src\Helper\LanguageHelper.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\src\Helper\Serializer.cs">
      <Link>src\Helper\Serializer.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\src\Helper\TranslateMetadata.cs">
      <Link>src\Helper\TranslateMetadata.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\src\Helper\TranslateUtils.cs">
      <Link>src\Helper\TranslateUtils.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\src\Models\LanguageModel.cs">
      <Link>src\Models\LanguageModel.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\src\Models\LanguageTranslation.cs">
      <Link>src\Models\LanguageTranslation.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\src\Models\LocalizedString.cs">
      <Link>src\Models\LocalizedString.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\src\Attributes\LocalizedDescriptionAttribute.cs">
      <Link>src\Attributes\LocalizedDescriptionAttribute.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\src\Converters\TranslateConverter.cs">
      <Link>src\Converters\TranslateConverter.cs</Link>
    </Compile>
  </ItemGroup>
  <ItemGroup />
  <ItemGroup>
    <ProjectReference Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\build\ZERO_CHECK.vcxproj">
      <Project>{F0AEDFA8-0806-3B7C-BF34-C4180DF012B8}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
  </ItemGroup>
</Project>