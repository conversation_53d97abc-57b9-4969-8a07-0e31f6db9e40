/*
 * Copyright 2021-present Alcon. All rights reserved.
 * Alcon proprietary/confidential.
 * Use is subject to license terms.
 */

using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;

namespace LocalizationLib;
public class ProjectLocalizationReplacer
{
    public void Run(
        string projectPath,
        string fileTypes)
    {
        Dictionary<string, int> csChangedFiles = new Dictionary<string, int>();
        Dictionary<string, int> xamlChangedFiles = new Dictionary<string, int>();

        //1) Initialize the config manager once
        var configManager = ProjectTextExtractorConfigManager.Initialize(projectPath, false);

        //2) Create a list of IReplacers based on fileTypes
        var replacers = CreateReplacers(fileTypes);

        // 3) Replace the extracted text
        foreach (var (key, extractedText) in configManager.GetConfig().ExtractedTexts)
        {
            switch (extractedText.Status)
            {
                case TextStatus.Open:
                    {
                        Console.WriteLine($"Extracted text \"{extractedText.OriginalText.Replace("\n", "\\n")}\" is open but not approved for replacement. No processing..");
                        continue;
                    }
                case TextStatus.Rejected:
                    {
                        Console.WriteLine($"Extracted text \"{extractedText.OriginalText.Replace("\n", "\\n")}\" is rejected. No processing..");
                        continue;
                    }
                case TextStatus.Resolved:
                    {
                        Console.WriteLine($"Extracted text \"{extractedText.OriginalText.Replace("\n", "\\n")}\" is already resolved. No processing..");
                        continue;
                    }
            }

            // only approved text reaches this line
            if (string.IsNullOrEmpty(extractedText.OriginalText) || !extractedText.NeedsTranslation)
                continue;

            var filePath = Path.Combine(projectPath, extractedText.FilePath);
            var replacer = replacers.FirstOrDefault(r => r.SupportedFileTypes.Contains(Path.GetExtension(extractedText.FilePath)));

            if (replacer != null)
            {
                var replaceStatus = replacer.Replace(filePath, extractedText);
                switch (replaceStatus)
                {
                    case ReplaceStatus.Replaced:
                        {
                            configManager.UpdateExtractedTextStatus(key, TextStatus.Resolved);
                            configManager.SetUpdatedColumn(key, extractedText.UpdatedColumn);
                            configManager.SetUpdatedLineNumber(key, extractedText.UpdatedLineNumber);

                            var extension = Path.GetExtension(filePath);
                            switch (extension)
                            {
                                case ".cs":
                                    if (!csChangedFiles.ContainsKey(filePath))
                                    {
                                        csChangedFiles[filePath] = 1;
                                    }
                                    else
                                    {
                                        csChangedFiles[filePath]++;
                                    }
                                    break;
                                case ".xaml":
                                    if (!xamlChangedFiles.ContainsKey(filePath))
                                    {
                                        xamlChangedFiles[filePath] = 1;
                                    }
                                    else
                                    {
                                        xamlChangedFiles[filePath]++;
                                    }

                                    break;
                            }
                            break;
                        }
                    case ReplaceStatus.AlreadyReplaced:
                        {
                            configManager.UpdateExtractedTextStatus(key, TextStatus.Resolved);
                            break;
                        }
                    case ReplaceStatus.NotReplaced:
                        {
                            Console.WriteLine($"Extracted text \"{extractedText.OriginalText.Replace("\n", "\\n")}\" couldn't be replaced! Notes updated.\"");
                            break;
                        }
                }
            }
        }

        configManager.SaveConfig();

        if (fileTypes == "both" || fileTypes == "cs")
        {
            Console.WriteLine($"{csChangedFiles.Count} c# files changed:");
            foreach (var (file, replacementsCount) in csChangedFiles)
            {
                Console.WriteLine($"Inside {file} has been added {replacementsCount} replacements.");
            }
        }

        if (fileTypes == "both" || fileTypes == "xaml")
        {
            Console.WriteLine($"\n{xamlChangedFiles.Count} XAML files changed:");
            foreach (var (file, replacementsCount) in xamlChangedFiles)
            {
                Console.WriteLine($"Inside {file} has been added {replacementsCount} replacements.");
            }
        }
    }

    /// <summary>
    /// Creates a list of IReplacers based on the fileTypes provided
    /// </summary>
    /// <param name="fileTypes"></param>
    /// <returns></returns>
    public List<IReplacer> CreateReplacers(string fileTypes)
    {
        var replacers = new List<IReplacer>();
        switch (fileTypes?.ToLowerInvariant())
        {
            case "cs":
                replacers.Add(new CsReplacer());
                break;
            case "xaml":
                replacers.Add(new XamlReplacer());
                break;
            case "both":
                replacers.Add(new CsReplacer());
                replacers.Add(new XamlReplacer());
                break;
            default:
                Console.WriteLine($"[WARNING] Unknown fileTypes '{fileTypes}', default to c# files...");
                replacers.Add(new CsReplacer());
                break;
        }
        return replacers;
    }
}