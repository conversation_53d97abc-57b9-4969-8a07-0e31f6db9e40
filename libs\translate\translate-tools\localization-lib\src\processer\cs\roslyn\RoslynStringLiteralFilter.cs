/*
 * Copyright 2021-present Alcon. All rights reserved.
 * Alcon proprietary/confidential.
 * Use is subject to license terms.
 */


using Microsoft.CodeAnalysis;
using Microsoft.CodeAnalysis.CSharp.Syntax;

namespace LocalizationLib;

/// <summary>
/// Example filter implementing your skip logic:
/// - Already translated
/// - Algorithm = ...
/// - e.PropertyName
/// - FindResource(...)
/// - new Uri(...)
/// - bracket-only strings, etc.
/// </summary>
public class RoslynStringLiteralFilter : ILiteralFilter
{
    public bool ShouldSkip(LiteralExpressionSyntax node, string literalValue)
    {
        // 1) skip empty or whitespace
        if (string.IsNullOrWhiteSpace(literalValue))
        {
            return true;
        }

        // 2) skip if it's not user-facing text (like "[", "][", ":", etc.)
        if (!IsLikelyUserFacingText(literalValue))
        {
            return true;
        }

        // 3) skip if it's already in a Translator call
        if (IsAlreadyTranslated(node))
        {
            return true;
        }

        // 4) skip if in "Algorithm = <...>"
        if (IsInsideAlgorithmAssignment(node))
        {
            return true;
        }

        // 5) skip if in "Notify(" or "NotifyPropertyChanged(" 
        if (IsInsideNotifyCall(node, "Notify") ||
            IsInsideNotifyCall(node, "NotifyPropertyChanged"))
        {
            return true;
        }

        // 6) skip if in "FindResource("
        if (IsInsideMethodCall(node, "FindResource"))
        {
            return true;
        }

        // 7) skip if used in e.PropertyName comparison
        if (IsComparingToPropertyName(node))
        {
            return true;
        }

        // 8) skip if used in new Uri("...")
        if (IsInsideNewUri(node))
        {
            return true;
        }

        // If none triggered, it's presumably a legit candidate
        return false;
    }

    private bool IsLikelyUserFacingText(string text)
    {
        // Must have at least one letter
        bool hasLetter = false;
        foreach (char c in text)
        {
            if (char.IsLetter(c))
            {
                hasLetter = true;
                break;
            }
        }
        if (!hasLetter) return false;

        // skip if < 2 in length 
        if (text.Length < 2)
            return false;

        return true;
    }

    private bool IsAlreadyTranslated(SyntaxNode node)
    {
        // Climb up for an InvocationExpression whose expression is "Translator.Instance.TranslateM"
        var current = node.Parent;
        while (current != null)
        {
            if (current is InvocationExpressionSyntax invocation)
            {
                var exprText = invocation.Expression.ToString();
                if (exprText.Contains("Translator.Instance.TranslateM"))
                    return true;
            }
            current = current.Parent;
        }
        return false;
    }

    private bool IsInsideAlgorithmAssignment(SyntaxNode node)
    {
        // Climb up to an AssignmentExpression
        var current = node.Parent;
        while (current != null)
        {
            if (current is AssignmentExpressionSyntax assignExpr)
            {
                string leftText = assignExpr.Left.ToString();
                if (leftText.Contains("Algorithm"))
                    return true;
            }
            current = current.Parent;
        }
        return false;
    }

    private bool IsInsideNotifyCall(SyntaxNode node, string methodName)
    {
        // Check if parent's chain has an InvocationExpression named methodName
        var current = node.Parent;
        while (current != null)
        {
            if (current is InvocationExpressionSyntax invoke)
            {
                var exprName = invoke.Expression.ToString();
                // e.g. "Notify" or "NotifyPropertyChanged"
                if (exprName.EndsWith(methodName))
                    return true;
            }
            current = current.Parent;
        }
        return false;
    }

    private bool IsInsideMethodCall(SyntaxNode node, string methodName)
    {
        var current = node.Parent;
        while (current != null)
        {
            if (current is InvocationExpressionSyntax invoke)
            {
                var exprName = invoke.Expression.ToString();
                // e.g. "FindResource", "this.FindResource", ...
                if (exprName.EndsWith(methodName))
                    return true;
            }
            current = current.Parent;
        }
        return false;
    }

    private bool IsComparingToPropertyName(SyntaxNode node)
    {
        // For example: if (e.PropertyName == "IsDummyImg")
        // This is a BinaryExpressionSyntax with left side "e.PropertyName" or right side "e.PropertyName".
        // We'll see if the parent or ancestor is a BinaryExpression, check if left or right side is "e.PropertyName".
        var current = node.Parent;
        while (current != null)
        {
            if (current is BinaryExpressionSyntax binaryExpr)
            {
                // e.PropertyName == "SomeValue"
                string left = binaryExpr.Left.ToString();
                string right = binaryExpr.Right.ToString();
                if (left.EndsWith("PropertyName") || right.EndsWith("PropertyName"))
                    return true;
            }
            current = current.Parent;
        }
        return false;
    }

    private bool IsInsideNewUri(SyntaxNode node)
    {
        // If the literal is part of: new Uri("...")
        // We'll check for an ObjectCreationExpression whose type is "Uri"
        var current = node.Parent;
        while (current != null)
        {
            if (current is ObjectCreationExpressionSyntax creation)
            {
                // e.g. "new Uri(...)"
                var typeName = creation.Type.ToString();
                if (typeName == "Uri")
                    return true;
            }
            current = current.Parent;
        }
        return false;
    }
}
