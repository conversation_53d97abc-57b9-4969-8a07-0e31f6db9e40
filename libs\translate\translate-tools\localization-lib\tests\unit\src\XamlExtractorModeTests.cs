/*
 * Copyright 2021-present Alcon. All rights reserved.
 * Alcon proprietary/confidential.
 * Use is subject to license terms.
 */

using NUnit.Framework;
using System;
using System.IO;
using System.Linq;

namespace LocalizationLib.Tests;

[TestFixture]
public class XamlExtractorModeTests
{
    private string _tempFolder;
    private string _projectFolder;
    private string _reportFile;

    [SetUp]
    public void SetUp()
    {
        // Reset the config manager before each test
        ProjectTextExtractorConfigManager.Reset();
        
        // Create a unique temporary folder for these tests.
        _tempFolder = Path.Combine(Path.GetTempPath(), "XamlExtractorModeTests_" + Guid.NewGuid());
        Directory.CreateDirectory(_tempFolder);

        // Create subfolders for the project files.
        _projectFolder = Path.Combine(_tempFolder, "Project");
        Directory.CreateDirectory(_projectFolder);

        // Define file paths for the report.
        _reportFile = Path.Combine(_tempFolder, "report.txt");
    }

    [TearDown]
    public void TearDown()
    {
        // Clean up the temporary folder and its contents.
        if (Directory.Exists(_tempFolder))
        {
            Directory.Delete(_tempFolder, true);
        }
    }

    /// <summary>
    /// Tests that when running in FindOnly mode the tool only reports findings
    /// (i.e. missing translation texts) without modifying the XAML file.
    /// </summary>
    [Test]
    public void Extractor_Run_FindOnlyMode_DoesNotModifyFiles()
    {
        // Arrange: Create a sample XAML file with a hard-coded text.
        string xamlContent =
@"<UserControl xmlns=""http://schemas.microsoft.com/winfx/2006/xaml/presentation""
    xmlns:x=""http://schemas.microsoft.com/winfx/2006/xaml""
    Title=""Sample"">
    <Grid>
        <Button Content=""Cancel"" />
    </Grid>
</UserControl>";
        string xamlFile = Path.Combine(_projectFolder, "Test.xaml");
        File.WriteAllText(xamlFile, xamlContent);

        // Act: Run the extractor in find-only mode
        var extractor = new ProjectLocalizationExtractor();
        extractor.Run(
            projectPath: _projectFolder,
            reportFile: _reportFile,
            "both"
        );

        // Assert: The XAML file should remain unchanged.
        string postXaml = File.ReadAllText(xamlFile);
        Assert.AreEqual(
            xamlContent,
            postXaml,
            "XAML file should remain unchanged in find-only mode."
        );

        // The report file should exist and include a notice about 'FindOnly' and 'Missing translation'.
        Assert.IsTrue(File.Exists(_reportFile), "Report file should be created in find-only mode.");
        string reportContent = File.ReadAllText(_reportFile);
        StringAssert.Contains("FindOnly", reportContent, "Report should indicate FindOnly mode.");
        StringAssert.Contains("Missing translation", reportContent, "Report should list 'Missing translation' text.");

        // Verify that the extracted text was stored in the config
        var config = ProjectTextExtractorConfigManager.GetConfigSafe(_projectFolder);
        Assert.IsNotNull(config, "Config should be created");
        Assert.IsTrue(config.ExtractedTexts.Count > 0, "Extracted texts should be stored in config");
        Assert.IsTrue(config.ExtractedTexts.Values.Any(x => x.OriginalText == "Cancel"), 
            "Config should contain the extracted text 'Cancel'");
    }
}
