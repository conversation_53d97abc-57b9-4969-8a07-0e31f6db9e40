/*
 * Copyright 2021-present Alcon. All rights reserved.
 * Alcon proprietary/confidential.
 * Use is subject to license terms.
 */

using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;

namespace LocalizationLib;
public class ProjectLocalizationExtractor
{
    private readonly ReportGenerator _reportGenerator;

    public ProjectLocalizationExtractor()
    {
        _reportGenerator = new ReportGenerator();
    }

    public void Run(
        string projectPath,
        string reportFile,
        string projectTypes = "cs", // e.g. "cs", "cpp", "all"
        string csProcessorOption = "roslyn",
        string cppProcessorOption = "consolidated",
        CodeRestructureOptions restructureOptions = null,
        ExtractionMode extractionMode = ExtractionMode.RestructureAndExtract
    )
    {
        var configManager = ProjectTextExtractorConfigManager.EnsureInitialized(projectPath, true);

        // Parse project types into a set for easier checking
        var types = new HashSet<string>(
            (projectTypes ?? "cs").ToLowerInvariant().Split(',', ';', ' ').Select(t => t.Trim()),
            StringComparer.OrdinalIgnoreCase
        );

        // Branch based on extraction mode
        if (extractionMode == ExtractionMode.RestructureOnly)
        {
            if (restructureOptions != null)
            {
                // Handle C# specific restructuring (including XAML)
                if (types.Contains("cs") || types.Contains("all"))
                {
                    if (restructureOptions.ConvertStringInterpolation)
                        ProcessStringInterpolations(projectPath);
                    if (restructureOptions.ConvertStringConcatenation)
                        ProcessStringConcatenations(projectPath);
                    if (restructureOptions.ConvertXamlTextToCodeBehind)
                        ProcessXamlTextToCodeBehind(projectPath);
                }

                // TODO: Add C++ specific restructuring options here when needed
                // if (types.Contains("cpp") || types.Contains("all"))
                // {
                //     // Future C++ restructuring options
                // }
            }
            return;
        }
        else if (extractionMode == ExtractionMode.RestructureAndExtract)
        {
            if (restructureOptions != null)
            {
                // Handle C# specific restructuring (including XAML)
                if (types.Contains("cs") || types.Contains("all"))
                {
                    if (restructureOptions.ConvertStringInterpolation)
                        ProcessStringInterpolations(projectPath);
                    if (restructureOptions.ConvertStringConcatenation)
                        ProcessStringConcatenations(projectPath);
                    if (restructureOptions.ConvertXamlTextToCodeBehind)
                        ProcessXamlTextToCodeBehind(projectPath);
                }

                // TODO: Add C++ specific restructuring options here when needed
                // if (types.Contains("cpp") || types.Contains("all"))
                // {
                //     // Future C++ restructuring options
                // }
            }
        }
        // else ExtractionOnly: skip restructuring

        // Extraction logic
        var extractors = CreateExtractors(projectTypes, csProcessorOption, cppProcessorOption, projectPath);
        var allFileResults = new List<FileProcessingResult>();
        var allReplacements = new List<ReplacementRecord>();
        foreach (var extractor in extractors)
        {
            var (fileResults, replacements) = extractor.Extract(projectPath);
            allFileResults.AddRange(fileResults);
            allReplacements.AddRange(replacements);
        }
        // Save all extracted texts to config in one batch
        configManager.AddExtractedTexts(allReplacements);

        // Generate single combined report
        _reportGenerator.GenerateReport(reportFile, allFileResults);
    }

    private List<IExtractor> CreateExtractors(
        string projectTypes, 
        string csProcessorOption, 
        string cppProcessorOption,
        string projectPath = null)
    {
        var extractors = new List<IExtractor>();
        var llmClient = GetLlmClient();

        // Parse project types into a set for easier checking
        var types = new HashSet<string>(
            (projectTypes ?? "cs").ToLowerInvariant().Split(',', ';', ' ').Select(t => t.Trim()),
            StringComparer.OrdinalIgnoreCase
        );

        // Add C# extractor if requested (including XAML for C# projects)
        if (types.Contains("cs") || types.Contains("all"))
        {
            // Add XAML extractor for C# projects
            extractors.Add(new XamlExtractor(projectPath, llmClient));

            // Add C# code extractor
            switch (csProcessorOption?.ToLowerInvariant())
            {
                case "roslyn":
                case "regex":
                    extractors.Add(new CsExtractor(csProcessorOption));
                    break;
                case "consolidated":
                    extractors.Add(new ConsolidatedCsProcessor(projectPath, llmClient));
                    break;
                default:
                    Console.WriteLine($"[WARNING] Unknown csProcessorOption '{csProcessorOption}', defaulting to consolidated...");
                    extractors.Add(new ConsolidatedCsProcessor(projectPath, llmClient));
                    break;
            }
        }

        // Add C++ extractor if requested
        if (types.Contains("cpp") || types.Contains("all"))
        {
            switch (cppProcessorOption?.ToLowerInvariant())
            {
                case "regex":
                    extractors.Add(new CppExtractor(projectPath, "regex"));
                    break;
                case "consolidated":
                    extractors.Add(new CppExtractor(projectPath, "consolidated", llmClient));
                    break;
                case "llm":
                    Console.WriteLine("[WARNING] LLM-only processor not implemented yet, defaulting to consolidated...");
                    extractors.Add(new CppExtractor(projectPath, "consolidated", llmClient));
                    break;
                default:
                    Console.WriteLine($"[WARNING] Unknown cppProcessorOption '{cppProcessorOption}', defaulting to consolidated...");
                    extractors.Add(new CppExtractor(projectPath, "consolidated", llmClient));
                    break;
            }
        }

        // If no valid project types specified, default to C#
        if (extractors.Count == 0)
        {
            Console.WriteLine($"[WARNING] No valid project types specified in '{projectTypes}', defaulting to C# only...");
            extractors.Add(new XamlExtractor(projectPath, llmClient)); // Add XAML extractor for C# default
            extractors.Add(new ConsolidatedCsProcessor(projectPath, llmClient));
        }

        return extractors;
    }

    private ILlmClient GetLlmClient()
    {
         // Try to create LLM client if config exists
        ILlmClient llmClient = null;
        try
        {
            string configPath = LLMConfigPathResolver.ResolveLLMConfigPath();
            if (configPath == null)
            {
                Console.WriteLine("Error: LLM config not found in any of the expected locations. Cannot run LLMCsProcessor.");
                return llmClient;
            }

            var config = LLMConfigLoader.LoadLlmConfig(configPath);
            if (config == null)
            {
                Console.WriteLine("Error: Failed to load LLM config. Cannot run LLMCsProcessor.");
                return llmClient;
            }

            // Use factory to create the appropriate LLM client based on configuration
            llmClient = LlmClientFactory.CreateLlmClient(config);

            Console.WriteLine($"Successfully initialized {config.llmProvider} LLM client.");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Warning: Could not initialize LLM client: {ex.Message}");
        }
        return llmClient;
    }

    private void ProcessStringInterpolations(string projectPath)
    {
        Console.WriteLine("[INFO] Processing string interpolations...");
        var processor = new StringInterpolationProcessor();
        int totalFiles = 0;
        int changedFiles = 0;
        int totalConversions = 0;

        // Get config manager to access skip patterns
        var config = ProjectTextExtractorConfigManager.GetConfigSafe(projectPath);
        var skipPatterns = config?.SkipPatterns ?? new List<string>();

        // Find all .cs files
        var csFiles = Directory.GetFiles(projectPath, "*.cs", SearchOption.AllDirectories)
            .Where(path => !PathUtils.IsInIgnoredDirectory(path) && 
                          !PathUtils.ShouldSkipPath(path, skipPatterns));

        foreach (var file in csFiles)
        {
            totalFiles++;
            try
            {
                var result = processor.ProcessFile(file);
                if (result.Changed)
                {
                    changedFiles++;
                    totalConversions += result.Conversions.Count;

                    // Log the changes
                    Console.WriteLine($"\nConverted {result.Conversions.Count} string interpolations in {file}:");
                    foreach (var (line, col, original, converted) in result.Conversions)
                    {
                        Console.WriteLine($"  Line {line}, Col {col}:");
                        Console.WriteLine($"    From: {original}");
                        Console.WriteLine($"    To:   {converted}");
                    }

                    // Save the changes
                    File.WriteAllText(file, result.UpdatedContent);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[ERROR] Failed to process {file}: {ex.Message}");
            }
        }

        Console.WriteLine($"\n[INFO] String interpolation conversion complete:");
        Console.WriteLine($"  Total files processed: {totalFiles}");
        Console.WriteLine($"  Files with changes: {changedFiles}");
        Console.WriteLine($"  Total conversions: {totalConversions}");
    }

    private void ProcessStringConcatenations(string projectPath)
    {
        Console.WriteLine("[INFO] Processing string concatenations...");
        var processor = new StringConcatenationProcessor();
        int totalFiles = 0;
        int changedFiles = 0;
        int totalConversions = 0;

        // Get config manager to access skip patterns
        var config = ProjectTextExtractorConfigManager.GetConfigSafe(projectPath);
        var skipPatterns = config?.SkipPatterns ?? new List<string>();

        // Find all .cs files
        var csFiles = Directory.GetFiles(projectPath, "*.cs", SearchOption.AllDirectories)
            .Where(path => !PathUtils.IsInIgnoredDirectory(path) &&
                          !PathUtils.ShouldSkipPath(path, skipPatterns));

        foreach (var file in csFiles)
        {
            totalFiles++;
            var result = processor.ProcessFile(file);
            if (result.Changed)
            {
                File.WriteAllText(file, result.UpdatedContent);
                changedFiles++;
                totalConversions += result.Conversions.Count;
                Console.WriteLine($"\nConverted {result.Conversions.Count} string concatenations in {file}:");
                foreach (var conv in result.Conversions)
                {
                    Console.WriteLine($@"  Line {conv.line}, Col {conv.col}: {conv.original} -> {conv.converted}");
                }
            }
        }
        Console.WriteLine($"\n[INFO] String concatenation conversion complete:");
        Console.WriteLine($"  Files processed: {totalFiles}");
        Console.WriteLine($"  Files with changes: {changedFiles}");
        Console.WriteLine($"  Total conversions: {totalConversions}");
    }

    private void ProcessXamlTextToCodeBehind(string projectPath)
    {
        Console.WriteLine("[INFO] Processing XAML text to code-behind...");
        var processor = new XamlTextToCodeBehindProcessor(projectPath);
        int totalFiles = 0;
        int changedFiles = 0;
        int totalMoves = 0;

        // Get config manager to access skip patterns
        var config = ProjectTextExtractorConfigManager.GetConfigSafe(projectPath);
        var skipPatterns = config?.SkipPatterns ?? new List<string>();

        // Find all .xaml files
        var xamlFiles = Directory.GetFiles(projectPath, "*.xaml", SearchOption.AllDirectories)
            .Where(path => !PathUtils.IsInIgnoredDirectory(path) &&
                          !PathUtils.ShouldSkipPath(path, skipPatterns));

        foreach (var file in xamlFiles)
        {
            totalFiles++;
            try
            {
                var result = processor.ProcessFile(file);
                if (result.Changed)
                {
                    changedFiles++;
                    totalMoves += result.Replacements.Count;

                    // Log the changes
                    Console.WriteLine($"\nMoved {result.Replacements.Count} text properties to code-behind in {file}:");
                    foreach (var replacement in result.Replacements)
                    {
                        Console.WriteLine($"  Line {replacement.LineNumber}, Property: {replacement.Property}");
                        Console.WriteLine($"    Value: {replacement.OriginalValue}");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[ERROR] Failed to process {file}: {ex.Message}");
            }
        }

        Console.WriteLine($"\n[INFO] XAML text to code-behind conversion complete:");
        Console.WriteLine($"  Total files processed: {totalFiles}");
        Console.WriteLine($"  Files with changes: {changedFiles}");
        Console.WriteLine($"  Total properties moved: {totalMoves}");
    }
}