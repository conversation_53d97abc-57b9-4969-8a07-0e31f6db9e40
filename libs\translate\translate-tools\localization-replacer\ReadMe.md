# Localization Replacer

## Overview
The **Localization Replacer** is a command-line tool that is replacing some specific strings with calls/bindings to the translation system. It can process **C#** and **XAML** files. The input for the tool is a json config file, that contains the extracted texts, with the source filePath, lineNumber, originalText and other attributes. [View the ProjectTextExtractorConfig file](../localization-lib/src/common/ProjectTextExtractorConfig.cs)

- **C#**: Adds to the strings the translation call `Translator.Instance.TranslateM` and inserts the `Using Alcon.Interop.Translate` once per file

- **XAML**: Adds the Bindings for the Translator `Text="{Binding Converter={StaticResource TranslateConverter}, ConverterParameter='id:-1741081814|en:SURGEON'}"`,the translate converter `xmlns:trConv=""clr-namespace:Alcon.Interop.Translate;assembly=translate_csharp`  and resource for it `<trConv:TranslateConverter x:Key="TranslateConverter" />`

## Features
- Automatic replacement for the strings with *Approved* status inside ProjectTextExtractorConfig.
- Updates config file by setting the updatedLineNumber and updatedColumn for each extractedText where replacement was done
- Change status to Resolved or Rejected

*Special cases:*
- XAML files:
    - For **Run Text** property adds Mode=OneWay inside the binding
        ```xaml
        <Run Text="{Binding Converter={StaticResource TranslateConverter}, ConverterParameter='id:-1741081814|en:SURGEON', Mode=OneWay}/>"
        ```

- C# files:
    - **Description** attribute inside enum is changed with LocalizedDescription

        ```csharp
        // From
        [Description("New Image Guided Measurement")] 
        Link

        // To 
        [LocalizedDescription("1291951203","New Image Guided Measurement")]
        Link
        ```

## Installation
- .NET 6.0+ installed on your system
- A project containing `.xaml` files and `.cs` files for your business logic
- CMake (if you’re building from source)

### Build (if applicable)
```bash
# Clone the repository (if needed)
git clone <repo-url>
cd localization-replacer

# Create a build directory
mkdir build && cd build

# Generate build files using CMake
cmake ..

# Build the project
cmake --build .
```
## Usage

### Command Syntax
```bash
localization_replacer.exe 
    -p <ProjectPath> 
    [-t <FileTypes>]
```

### Parameters

| Short | Long               | Required | Default         | Description                                                                                                                                  |
|-------|--------------------|----------|-----------------|----------------------------------------------------------------------------------------------------------------------------------------------|
| `-p`  | `--projectPath`    | **Yes**  | —               | Path to the folder containing files to scan (XAML, C#).                                                                                      |
| `-t`  | `--fileTypes`      | No       | `both`          | Which file types to process. `xaml`, `cs`, or `both`
