/*
 * Copyright 2021-present Alcon. All rights reserved.
 * Alcon proprietary/confidential.
 * Use is subject to license terms.
 */

using NUnit.Framework;
using System;
using LocalizationLib;

namespace LocalizationLib.Tests
{
    [TestFixture]
    public class LlmClientFactoryTests
    {
        [Test]
        public void CreateLlmClient_WithNullConfig_ThrowsArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => LlmClientFactory.CreateLlmClient(null));
        }

        [Test]
        public void CreateLlmClient_WithEmptyProvider_ThrowsInvalidOperationException()
        {
            // Arrange
            var config = new LLMConfig { llmProvider = "" };

            // Act & Assert
            var ex = Assert.Throws<InvalidOperationException>(() => LlmClientFactory.CreateLlmClient(config));
            Assert.That(ex.Message, Contains.Substring("llmProvider is not specified"));
        }

        [Test]
        public void CreateLlmClient_WithUnsupportedProvider_ThrowsInvalidOperationException()
        {
            // Arrange
            var config = new LLMConfig { llmProvider = "UnsupportedProvider" };

            // Act & Assert
            var ex = Assert.Throws<InvalidOperationException>(() => LlmClientFactory.CreateLlmClient(config));
            Assert.That(ex.Message, Contains.Substring("Unsupported LLM provider"));
        }

        [Test]
        public void CreateLlmClient_WithLocalProvider_ReturnsLocalLlmClient()
        {
            // Arrange
            var config = new LLMConfig
            {
                llmProvider = "LocalLlmProvider",
                localLlmProviderConfig = new LocalLlmProviderConfig
                {
                    serviceUrl = "http://localhost:1234",
                    modelId = "test-model",
                    temperature = 0.0,
                    maxRetries = 3,
                    timeoutSeconds = 30,
                    chunkSize = 300,
                    useChunking = false
                }
            };

            // Act
            var client = LlmClientFactory.CreateLlmClient(config);

            // Assert
            Assert.IsInstanceOf<LocalLlmClient>(client);
            Assert.AreEqual(config, client.GetConfig());
        }

        [Test]
        public void CreateLlmClient_WithLocalProviderCaseInsensitive_ReturnsLocalLlmClient()
        {
            // Arrange
            var config = new LLMConfig
            {
                llmProvider = "local",
                localLlmProviderConfig = new LocalLlmProviderConfig
                {
                    serviceUrl = "http://localhost:1234",
                    modelId = "test-model",
                    temperature = 0.0,
                    maxRetries = 3,
                    timeoutSeconds = 30,
                    chunkSize = 300,
                    useChunking = false
                }
            };

            // Act
            var client = LlmClientFactory.CreateLlmClient(config);

            // Assert
            Assert.IsInstanceOf<LocalLlmClient>(client);
        }

        [Test]
        public void CreateLlmClient_WithBedrockProvider_ReturnsBedrockLlmClient()
        {
            // Arrange
            var config = new LLMConfig
            {
                llmProvider = "BedrockProvider",
                bedrockProviderConfig = new BedrockProviderConfig
                {
                    region = "us-east-1",
                    modelId = "anthropic.claude-3-sonnet-20240229-v1:0",
                    temperature = 0.0,
                    maxRetries = 3,
                    timeoutSeconds = 120,
                    chunkSize = 3000,
                    maxTokens = 4096,
                    accessKey = "AKIAIOSFODNN7EXAMPLE",
                    secretKey = "wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY"
                }
            };

            // Act
            var client = LlmClientFactory.CreateLlmClient(config);

            // Assert
            Assert.IsInstanceOf<BedrockLlmClient>(client);
            Assert.AreEqual(config, client.GetConfig());
        }

        [Test]
        public void CreateLlmClient_WithBedrockProviderCaseInsensitive_ReturnsBedrockLlmClient()
        {
            // Arrange
            var config = new LLMConfig
            {
                llmProvider = "bedrock",
                bedrockProviderConfig = new BedrockProviderConfig
                {
                    region = "us-east-1",
                    modelId = "anthropic.claude-3-sonnet-20240229-v1:0",
                    temperature = 0.0,
                    maxRetries = 3,
                    timeoutSeconds = 120,
                    chunkSize = 3000,
                    maxTokens = 4096,
                    accessKey = "AKIAIOSFODNN7EXAMPLE",
                    secretKey = "wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY"
                }
            };

            // Act
            var client = LlmClientFactory.CreateLlmClient(config);

            // Assert
            Assert.IsInstanceOf<BedrockLlmClient>(client);
        }

        [Test]
        public void CreateLlmClient_WithLocalProviderMissingConfig_ThrowsInvalidOperationException()
        {
            // Arrange
            var config = new LLMConfig
            {
                llmProvider = "LocalLlmProvider",
                localLlmProviderConfig = null
            };

            // Act & Assert
            var ex = Assert.Throws<InvalidOperationException>(() => LlmClientFactory.CreateLlmClient(config));
            Assert.That(ex.Message, Contains.Substring("localLlmProviderConfig is required"));
        }

        [Test]
        public void CreateLlmClient_WithBedrockProviderMissingConfig_ThrowsInvalidOperationException()
        {
            // Arrange
            var config = new LLMConfig
            {
                llmProvider = "BedrockProvider",
                bedrockProviderConfig = null
            };

            // Act & Assert
            var ex = Assert.Throws<InvalidOperationException>(() => LlmClientFactory.CreateLlmClient(config));
            Assert.That(ex.Message, Contains.Substring("bedrockProviderConfig is required"));
        }

        [Test]
        public void CreateLlmClient_WithInvalidLocalConfig_ThrowsInvalidOperationException()
        {
            // Arrange
            var config = new LLMConfig
            {
                llmProvider = "LocalLlmProvider",
                localLlmProviderConfig = new LocalLlmProviderConfig
                {
                    serviceUrl = "", // Invalid
                    modelId = "test-model",
                    temperature = 0.0,
                    maxRetries = 3,
                    timeoutSeconds = 30,
                    chunkSize = 300
                }
            };

            // Act & Assert
            var ex = Assert.Throws<InvalidOperationException>(() => LlmClientFactory.CreateLlmClient(config));
            Assert.That(ex.Message, Contains.Substring("serviceUrl is required"));
        }

        [Test]
        public void CreateLlmClient_WithInvalidBedrockConfig_ThrowsInvalidOperationException()
        {
            // Arrange
            var config = new LLMConfig
            {
                llmProvider = "BedrockProvider",
                bedrockProviderConfig = new BedrockProviderConfig
                {
                    region = "", // Invalid
                    modelId = "anthropic.claude-3-sonnet-20240229-v1:0",
                    temperature = 0.0,
                    maxRetries = 3,
                    timeoutSeconds = 120,
                    chunkSize = 3000,
                    maxTokens = 4096
                }
            };

            // Act & Assert
            var ex = Assert.Throws<InvalidOperationException>(() => LlmClientFactory.CreateLlmClient(config));
            Assert.That(ex.Message, Contains.Substring("region is required"));
        }

        [Test]
        public void GetSupportedProviders_ReturnsExpectedProviders()
        {
            // Act
            var providers = LlmClientFactory.GetSupportedProviders();

            // Assert
            Assert.Contains("LocalLlmProvider", providers);
            Assert.Contains("BedrockProvider", providers);
            Assert.AreEqual(2, providers.Length);
        }

        [Test]
        public void IsProviderSupported_WithSupportedProviders_ReturnsTrue()
        {
            // Act & Assert
            Assert.IsTrue(LlmClientFactory.IsProviderSupported("LocalLlmProvider"));
            Assert.IsTrue(LlmClientFactory.IsProviderSupported("local"));
            Assert.IsTrue(LlmClientFactory.IsProviderSupported("BedrockProvider"));
            Assert.IsTrue(LlmClientFactory.IsProviderSupported("bedrock"));
        }

        [Test]
        public void IsProviderSupported_WithUnsupportedProvider_ReturnsFalse()
        {
            // Act & Assert
            Assert.IsFalse(LlmClientFactory.IsProviderSupported("UnsupportedProvider"));
            Assert.IsFalse(LlmClientFactory.IsProviderSupported(""));
            Assert.IsFalse(LlmClientFactory.IsProviderSupported(null));
        }
    }
}
