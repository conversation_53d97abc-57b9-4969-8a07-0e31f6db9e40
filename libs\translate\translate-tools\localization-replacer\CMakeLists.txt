cmake_minimum_required(VERSION 3.22.0)
include_guard(GLOBAL)
project(localization_replacer VERSION 1.0 LANGUAGES CSharp)

include("${CMAKE_CURRENT_SOURCE_DIR}/../../../../cmake/Globals.cmake")

setup_subdirectory("${CMAKE_CURRENT_SOURCE_DIR}/../localization-lib")

set(TARGET ${PROJECT_NAME})

file(GLOB_RECURSE CS_FILES CONFIGURE_DEPENDS "${CMAKE_CURRENT_SOURCE_DIR}/src/*.cs")

add_executable(${TARGET} ${CS_FILES})

set_target_properties(${TARGET} PROPERTIES
    DOTNET_SDK ${ROOT_DOTNET_SDK}
    DOTNET_TARGET_FRAMEWORK ${ROOT_DOTNET_TARGET_FRAMEWORK}  
    VS_PACKAGE_REFERENCES "${CommandLineParser};"
)

target_link_libraries(${TARGET} PRIVATE localizationlib)
