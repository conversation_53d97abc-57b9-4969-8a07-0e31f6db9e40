﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{4F041657-5A7B-30B7-8C02-4021DD130165}"
	ProjectSection(ProjectDependencies) = postProject
		{CA19AFAE-4324-3E83-A534-7D5D55019AC8} = {CA19AFAE-4324-3E83-A534-7D5D55019AC8}
		{259ABDC9-F845-31CD-9735-B69A45B30E0A} = {259ABDC9-F845-31CD-9735-B69A45B30E0A}
		{6EDDFEA2-94B0-37C8-B2B4-A6C2FA8142DD} = {6EDDFEA2-94B0-37C8-B2B4-A6C2FA8142DD}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ZERO_CHECK", "ZERO_CHECK.vcxproj", "{CA19AFAE-4324-3E83-A534-7D5D55019AC8}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "translate_csharp", "translate_csharp\translate_csharp.csproj", "{259ABDC9-F845-31CD-9735-B69A45B30E0A}"
	ProjectSection(ProjectDependencies) = postProject
		{CA19AFAE-4324-3E83-A534-7D5D55019AC8} = {CA19AFAE-4324-3E83-A534-7D5D55019AC8}
	EndProjectSection
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "translate_unit_tests", "translate_unit_tests.csproj", "{6EDDFEA2-94B0-37C8-B2B4-A6C2FA8142DD}"
	ProjectSection(ProjectDependencies) = postProject
		{CA19AFAE-4324-3E83-A534-7D5D55019AC8} = {CA19AFAE-4324-3E83-A534-7D5D55019AC8}
		{259ABDC9-F845-31CD-9735-B69A45B30E0A} = {259ABDC9-F845-31CD-9735-B69A45B30E0A}
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
		Release|x64 = Release|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{4F041657-5A7B-30B7-8C02-4021DD130165}.Debug|x64.ActiveCfg = Debug|x64
		{4F041657-5A7B-30B7-8C02-4021DD130165}.Release|x64.ActiveCfg = Release|x64
		{CA19AFAE-4324-3E83-A534-7D5D55019AC8}.Debug|x64.ActiveCfg = Debug|x64
		{CA19AFAE-4324-3E83-A534-7D5D55019AC8}.Debug|x64.Build.0 = Debug|x64
		{CA19AFAE-4324-3E83-A534-7D5D55019AC8}.Release|x64.ActiveCfg = Release|x64
		{CA19AFAE-4324-3E83-A534-7D5D55019AC8}.Release|x64.Build.0 = Release|x64
		{259ABDC9-F845-31CD-9735-B69A45B30E0A}.Debug|x64.ActiveCfg = Debug|Any CPU
		{259ABDC9-F845-31CD-9735-B69A45B30E0A}.Debug|x64.Build.0 = Debug|Any CPU
		{259ABDC9-F845-31CD-9735-B69A45B30E0A}.Release|x64.ActiveCfg = Release|Any CPU
		{259ABDC9-F845-31CD-9735-B69A45B30E0A}.Release|x64.Build.0 = Release|Any CPU
		{6EDDFEA2-94B0-37C8-B2B4-A6C2FA8142DD}.Debug|x64.ActiveCfg = Debug|Any CPU
		{6EDDFEA2-94B0-37C8-B2B4-A6C2FA8142DD}.Debug|x64.Build.0 = Debug|Any CPU
		{6EDDFEA2-94B0-37C8-B2B4-A6C2FA8142DD}.Release|x64.ActiveCfg = Release|Any CPU
		{6EDDFEA2-94B0-37C8-B2B4-A6C2FA8142DD}.Release|x64.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {B52C6B1F-7DE2-39D4-A571-A637A51B69DA}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal
