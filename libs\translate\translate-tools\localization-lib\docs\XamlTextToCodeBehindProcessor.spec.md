# XamlTextToCodeBehindProcessor Specification

## Purpose
The `XamlTextToCodeBehindProcessor` moves non-dependency property text values from XAML to code-behind, while preserving dependency properties in XAML. The primary goal is to identify and move only CLR properties that contain user-facing text, while ensuring that WPF's dependency property system remains intact.

## Core Principles
1. **All dependency properties must remain in XAML**
   - Dependency properties support WPF's property system (binding, animation, etc.)
   - Moving them to code-behind would break XAML functionality
   - They are designed to work with XAML's declarative nature

2. **Minimal File Changes**
   - Only modify lines that require changes
   - Preserve original file formatting exactly
   - No unnecessary whitespace or indentation changes
   - No reordering of existing attributes or elements
   - No reformatting of untouched code sections
   - Maintain original namespace declarations and prefixes
   - Preserve exact attribute spacing and line breaks

3. **Atomic Changes**
   - XAML and code-behind changes must succeed together or not at all
   - If any part of the process fails, both files must remain unmodified
   - Verify changes before committing to disk
   - Use temporary files for processing to avoid partial updates
   - Maintain file consistency between XAML and code-behind

## File Modification Rules

### XAML Changes
1. When removing attributes:
   - Remove only the specific attribute
   - Preserve spacing between remaining attributes
   - Don't reformat the element or its children
   - Don't add unnecessary line breaks
   - Maintain original namespace prefixes
   - Keep original attribute order

2. When adding x:Name:
   - Add as first attribute if no attributes exist
   - Insert before first attribute if attributes exist
   - Preserve original attribute spacing
   - Don't modify element indentation
   - Use existing namespace prefix for 'x'

Example of correct XAML modification:
```xaml
<!-- Original -->
<Button Content="Hello"    Width="100"    Height="30" />

<!-- Correct (only Content removed, spacing preserved) -->
<Button x:Name="autoGen_Button_123"    Width="100"    Height="30" />

<!-- Incorrect (unnecessary reformatting) -->
<Button 
    x:Name="autoGen_Button_123"
    Width="100"
    Height="30" />
```

### Code-behind Changes
1. When adding property assignments:
   - Insert at the end of InitializeComponent()
   - Match existing indentation exactly
   - Preserve existing line endings (CRLF/LF)
   - Don't modify existing code formatting

2. Code Insertion:
   - No blank lines between assignments
   - No extra newlines at end of method
   - Preserve original bracing style
   - Match existing string quote style

Example of correct code-behind modification:
```csharp
public void InitializeComponent()
{
    existingCode();
    moreExistingCode();
    autoGen_Button_123.Content = "Hello";  // New line matches indent
}  // No extra newlines added
```

## Dependency Property Detection
The processor provides an API to determine if a property is a dependency property:

```csharp
bool IsKnownDependencyProperty(string controlType, string propertyName)
```

This method checks:
1. Known WPF controls and their dependency properties
2. Common attached properties (Grid.Row, DockPanel.Dock, etc.)
3. Custom controls registered in the system

## Text Identification Rules
The processor uses the same text identification logic as `XamlProcessor`:

1. Only processes properties that match the pattern:
   ```regex
   (Content|Text|Header|ToolTip|Title)
   ```

2. Skips text that:
   - Is empty or whitespace
   - Starts with '{' (bindings)
   - Is a number or boolean value
   - **Is a dependency property for the control type**

## Processing Rules

1. Dependency Property Handling:
   - All dependency properties must remain in XAML
   - Common WPF dependency properties (Content, Text, etc.) stay in XAML
   - Attached properties (Grid.Row, etc.) stay in XAML
   - Only non-DP text properties move to code-behind

2. Resource Elements:
   - Skip all elements within Resource sections
   - Skip system types (Double, Int32, etc.)
   - Skip geometry and drawing resources

3. Geometry and Technical Elements:
   - Skip geometry elements (Path, Rectangle, etc.)
   - Skip technical attributes
   - Skip style and template elements

4. Property Processing:
   - Only move properties that match the text identification rules AND are not dependency properties
   - Add x:Name attribute only when moving a property
   - Preserve existing x:Name attributes
   - Preserve XAML formatting and structure

5. Code-behind Updates:
   - Add assignments in InitializeComponent()
   - Preserve existing code-behind formatting
   - Use proper string escaping for quotes

6. Atomic Processing:
   - Process XAML changes first in memory
   - Validate all changes before applying
   - Generate code-behind changes in memory
   - Only write to disk if both XAML and code-behind changes are valid
   - Rollback on any error
   - Use temporary files to ensure atomic writes

## Example

Input XAML:
```xaml
<StackPanel>
    <!-- Content is a DP for Button - stays in XAML -->
    <Button Content="Click me"    Margin="10,5"    Width="100" />
    
    <!-- CustomText is not a DP - moves to code-behind -->
    <local:CustomControl CustomText="Hello"    Height="25" />
    
    <!-- Text is a DP for TextBlock - stays in XAML -->
    <TextBlock Text="Some text"    FontSize="12" />
</StackPanel>
```

Output XAML:
```xaml
<StackPanel>
    <!-- Content is a DP for Button - stays in XAML -->
    <Button Content="Click me"    Margin="10,5"    Width="100" />
    
    <!-- CustomText is not a DP - moves to code-behind -->
    <local:CustomControl x:Name="autoGen_CustomControl_12345678"    Height="25" />
    
    <!-- Text is a DP for TextBlock - stays in XAML -->
    <TextBlock Text="Some text"    FontSize="12" />
</StackPanel>
```

Output Code-behind:
```csharp
public void InitializeComponent()
{
    InitializeComponent();
    autoGen_CustomControl_12345678.CustomText = "Hello";
}
```

## Dependencies
- Uses same text identification pattern as XamlProcessor.cs
- Requires code-behind file with InitializeComponent method
- Requires knowledge of WPF dependency property system 