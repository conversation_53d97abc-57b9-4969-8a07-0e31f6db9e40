{"Version": 1, "WorkspaceRootPath": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-replacer\\build\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{0E313E6D-A6C0-3AC5-A116-97BABF7FD877}|localization_replacer.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-replacer\\build\\localization_replacer.csproj||{04B8AB82-A572-4FEF-95CE-5222444B6B64}|", "RelativeMoniker": "D:0:0:{0E313E6D-A6C0-3AC5-A116-97BABF7FD877}|localization_replacer.csproj|solutionrelative:localization_replacer.csproj||{04B8AB82-A572-4FEF-95CE-5222444B6B64}|"}, {"AbsoluteMoniker": "D:0:0:{E9C03864-1F46-398B-B9C6-C4515F3BA72F}|trtralocali\\localizationlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\projectlocalizationreplacer.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{0E313E6D-A6C0-3AC5-A116-97BABF7FD877}|localization_replacer.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-replacer\\src\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E9C03864-1F46-398B-B9C6-C4515F3BA72F}|trtralocali\\localizationlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\replacer\\csreplacer.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{0E313E6D-A6C0-3AC5-A116-97BABF7FD877}|localization_replacer.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-replacer\\src\\tooloptions.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 4, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{e1b7d1f8-9b3c-49b1-8f4f-bfc63a88835d}"}, {"$type": "Document", "DocumentIndex": 1, "Title": "ProjectLocalizationReplacer.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\ProjectLocalizationReplacer.cs", "RelativeDocumentMoniker": "..\\..\\localization-lib\\src\\ProjectLocalizationReplacer.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\ProjectLocalizationReplacer.cs", "RelativeToolTip": "..\\..\\localization-lib\\src\\ProjectLocalizationReplacer.cs", "ViewState": "AgIAAA8AAAAAAAAAAAAmwBwAAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-14T07:50:18.73Z"}, {"$type": "Document", "DocumentIndex": 2, "Title": "Program.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-replacer\\src\\Program.cs", "RelativeDocumentMoniker": "..\\src\\Program.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-replacer\\src\\Program.cs", "RelativeToolTip": "..\\src\\Program.cs", "ViewState": "AgIAAAwAAAAAAAAAAAAAAB0AAAAaAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-14T02:58:31.718Z"}, {"$type": "Document", "DocumentIndex": 4, "Title": "ToolOptions.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-replacer\\src\\ToolOptions.cs", "RelativeDocumentMoniker": "..\\src\\ToolOptions.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-replacer\\src\\ToolOptions.cs", "RelativeToolTip": "..\\src\\ToolOptions.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-14T02:58:24.169Z"}, {"$type": "Document", "DocumentIndex": 0, "Title": "localization_replacer", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-replacer\\build\\localization_replacer.csproj", "RelativeDocumentMoniker": "localization_replacer.csproj", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-replacer\\build\\localization_replacer.csproj", "RelativeToolTip": "localization_replacer.csproj", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000758|", "WhenOpened": "2025-05-05T05:11:28.719Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "CsReplacer.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\replacer\\CsReplacer.cs", "RelativeDocumentMoniker": "..\\..\\localization-lib\\src\\replacer\\CsReplacer.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\replacer\\CsReplacer.cs", "RelativeToolTip": "..\\..\\localization-lib\\src\\replacer\\CsReplacer.cs", "ViewState": "AgIAAJkAAAAAAAAAAAAjwK0AAAAQAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-13T04:28:47.038Z"}]}]}]}