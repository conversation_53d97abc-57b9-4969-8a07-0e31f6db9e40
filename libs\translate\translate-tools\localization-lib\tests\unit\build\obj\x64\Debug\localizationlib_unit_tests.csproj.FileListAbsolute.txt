D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\testhost.exe
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\testhost.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\NUnit3.TestAdapter.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\NUnit3.TestAdapter.pdb
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\nunit.engine.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\nunit.engine.api.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\nunit.engine.core.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\testcentric.engine.metadata.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\localizationlib_unit_tests.deps.json
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\localizationlib_unit_tests.runtimeconfig.json
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\localizationlib_unit_tests.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\localizationlib_unit_tests.pdb
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\Castle.Core.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\CommunityToolkit.Mvvm.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\Microsoft.CodeAnalysis.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\Microsoft.CodeAnalysis.CSharp.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\Microsoft.CodeAnalysis.CSharp.Scripting.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\Microsoft.CodeAnalysis.Scripting.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\Microsoft.VisualStudio.CodeCoverage.Shim.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\Microsoft.TestPlatform.CoreUtilities.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\Microsoft.TestPlatform.PlatformAbstractions.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\Microsoft.VisualStudio.TestPlatform.ObjectModel.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\Microsoft.TestPlatform.CommunicationUtilities.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\Microsoft.TestPlatform.CrossPlatEngine.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\Microsoft.TestPlatform.Utilities.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\Microsoft.VisualStudio.TestPlatform.Common.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\Moq.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\Newtonsoft.Json.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\NuGet.Frameworks.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\nunit.framework.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\System.Diagnostics.EventLog.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\cs\Microsoft.CodeAnalysis.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\de\Microsoft.CodeAnalysis.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\es\Microsoft.CodeAnalysis.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\fr\Microsoft.CodeAnalysis.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\it\Microsoft.CodeAnalysis.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\ja\Microsoft.CodeAnalysis.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\ko\Microsoft.CodeAnalysis.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\pl\Microsoft.CodeAnalysis.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\pt-BR\Microsoft.CodeAnalysis.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\ru\Microsoft.CodeAnalysis.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\tr\Microsoft.CodeAnalysis.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\zh-Hans\Microsoft.CodeAnalysis.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\zh-Hant\Microsoft.CodeAnalysis.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\cs\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\de\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\es\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\fr\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\it\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\ja\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\ko\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\pl\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\pt-BR\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\ru\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\tr\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\zh-Hans\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\zh-Hant\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\cs\Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\de\Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\es\Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\fr\Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\it\Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\ja\Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\ko\Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\pl\Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\pt-BR\Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\ru\Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\tr\Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\zh-Hans\Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\zh-Hant\Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\cs\Microsoft.CodeAnalysis.Scripting.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\de\Microsoft.CodeAnalysis.Scripting.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\es\Microsoft.CodeAnalysis.Scripting.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\fr\Microsoft.CodeAnalysis.Scripting.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\it\Microsoft.CodeAnalysis.Scripting.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\ja\Microsoft.CodeAnalysis.Scripting.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\ko\Microsoft.CodeAnalysis.Scripting.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\pl\Microsoft.CodeAnalysis.Scripting.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\pt-BR\Microsoft.CodeAnalysis.Scripting.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\ru\Microsoft.CodeAnalysis.Scripting.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\tr\Microsoft.CodeAnalysis.Scripting.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\zh-Hans\Microsoft.CodeAnalysis.Scripting.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\zh-Hant\Microsoft.CodeAnalysis.Scripting.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\cs\Microsoft.TestPlatform.CoreUtilities.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\cs\Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\de\Microsoft.TestPlatform.CoreUtilities.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\de\Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\es\Microsoft.TestPlatform.CoreUtilities.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\es\Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\fr\Microsoft.TestPlatform.CoreUtilities.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\fr\Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\it\Microsoft.TestPlatform.CoreUtilities.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\it\Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\ja\Microsoft.TestPlatform.CoreUtilities.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\ja\Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\ko\Microsoft.TestPlatform.CoreUtilities.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\ko\Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\pl\Microsoft.TestPlatform.CoreUtilities.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\pl\Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\pt-BR\Microsoft.TestPlatform.CoreUtilities.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\pt-BR\Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\ru\Microsoft.TestPlatform.CoreUtilities.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\ru\Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\tr\Microsoft.TestPlatform.CoreUtilities.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\tr\Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\zh-Hans\Microsoft.TestPlatform.CoreUtilities.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\zh-Hans\Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\zh-Hant\Microsoft.TestPlatform.CoreUtilities.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\zh-Hant\Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\cs\Microsoft.TestPlatform.CommunicationUtilities.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\cs\Microsoft.TestPlatform.CrossPlatEngine.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\cs\Microsoft.VisualStudio.TestPlatform.Common.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\de\Microsoft.TestPlatform.CommunicationUtilities.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\de\Microsoft.TestPlatform.CrossPlatEngine.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\de\Microsoft.VisualStudio.TestPlatform.Common.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\es\Microsoft.TestPlatform.CommunicationUtilities.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\es\Microsoft.TestPlatform.CrossPlatEngine.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\es\Microsoft.VisualStudio.TestPlatform.Common.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\fr\Microsoft.TestPlatform.CommunicationUtilities.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\fr\Microsoft.TestPlatform.CrossPlatEngine.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\fr\Microsoft.VisualStudio.TestPlatform.Common.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\it\Microsoft.TestPlatform.CommunicationUtilities.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\it\Microsoft.TestPlatform.CrossPlatEngine.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\it\Microsoft.VisualStudio.TestPlatform.Common.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\ja\Microsoft.TestPlatform.CommunicationUtilities.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\ja\Microsoft.TestPlatform.CrossPlatEngine.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\ja\Microsoft.VisualStudio.TestPlatform.Common.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\ko\Microsoft.TestPlatform.CommunicationUtilities.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\ko\Microsoft.TestPlatform.CrossPlatEngine.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\ko\Microsoft.VisualStudio.TestPlatform.Common.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\pl\Microsoft.TestPlatform.CommunicationUtilities.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\pl\Microsoft.TestPlatform.CrossPlatEngine.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\pl\Microsoft.VisualStudio.TestPlatform.Common.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\pt-BR\Microsoft.TestPlatform.CommunicationUtilities.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\pt-BR\Microsoft.TestPlatform.CrossPlatEngine.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\pt-BR\Microsoft.VisualStudio.TestPlatform.Common.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\ru\Microsoft.TestPlatform.CommunicationUtilities.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\ru\Microsoft.TestPlatform.CrossPlatEngine.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\ru\Microsoft.VisualStudio.TestPlatform.Common.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\tr\Microsoft.TestPlatform.CommunicationUtilities.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\tr\Microsoft.TestPlatform.CrossPlatEngine.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\tr\Microsoft.VisualStudio.TestPlatform.Common.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\zh-Hans\Microsoft.TestPlatform.CommunicationUtilities.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\zh-Hans\Microsoft.TestPlatform.CrossPlatEngine.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\zh-Hans\Microsoft.VisualStudio.TestPlatform.Common.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\zh-Hant\Microsoft.TestPlatform.CommunicationUtilities.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\zh-Hant\Microsoft.TestPlatform.CrossPlatEngine.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\zh-Hant\Microsoft.VisualStudio.TestPlatform.Common.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\runtimes\win\lib\net6.0\System.Diagnostics.EventLog.Messages.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\runtimes\win\lib\net6.0\System.Diagnostics.EventLog.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\localizationlib.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\Debug\localizationlib.pdb
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\obj\x64\Debug\localizationlib_unit_tests.csproj.AssemblyReference.cache
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\obj\x64\Debug\localizationlib_unit_tests.GeneratedMSBuildEditorConfig.editorconfig
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\obj\x64\Debug\localizationlib_unit_tests.AssemblyInfoInputs.cache
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\obj\x64\Debug\localizationlib_unit_tests.AssemblyInfo.cs
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\obj\x64\Debug\localizationlib_unit_tests.csproj.CoreCompileInputs.cache
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\obj\x64\Debug\localizationlib_unit_tests.sourcelink.json
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\obj\x64\Debug\localiza.9319290F.Up2Date
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\obj\x64\Debug\localizationlib_unit_tests.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\obj\x64\Debug\refint\localizationlib_unit_tests.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\obj\x64\Debug\localizationlib_unit_tests.pdb
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\obj\x64\Debug\localizationlib_unit_tests.genruntimeconfig.cache
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\tests\unit\build\obj\x64\Debug\ref\localizationlib_unit_tests.dll
