# CMAKE generated file: DO NOT EDIT!
# Generated by CMake Version 4.0

# CS_FILES at D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-lib/CMakeLists.txt:9 (file)
file(GLOB_RECURSE NEW_GLOB LIST_DIRECTORIES false "D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-lib/src/*.cs")
set(OLD_GLOB
  "D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-lib/src/IExtractor.cs"
  "D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-lib/src/ProjectLocalizationExtractor.cs"
  "D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-lib/src/ProjectLocalizationReplacer.cs"
  "D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-lib/src/ReportGenerator.cs"
  "D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-lib/src/code-restructure/StringConcatenationProcessor.cs"
  "D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-lib/src/code-restructure/StringInterpolationProcessor.cs"
  "D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-lib/src/code-restructure/XamlTextToCodeBehindProcessor.cs"
  "D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-lib/src/common/ProjectTextExtractorConfig.cs"
  "D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-lib/src/common/ProjectTextExtractorConfigManager.cs"
  "D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-lib/src/merge/FileMerge.cs"
  "D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-lib/src/merge/XMLElementComparer.cs"
  "D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-lib/src/processer/cpp/ConsolidatedCppProcessor.cs"
  "D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-lib/src/processer/cpp/CppExtractor.cs"
  "D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-lib/src/processer/cpp/ICppProcessor.cs"
  "D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-lib/src/processer/cpp/RegexCppProcessor.cs"
  "D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-lib/src/processer/cs/ConsolidatedCsProcessor.cs"
  "D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-lib/src/processer/cs/CsExtractor.cs"
  "D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-lib/src/processer/cs/CsProcessorHelpers.cs"
  "D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-lib/src/processer/cs/ICsProcessor.cs"
  "D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-lib/src/processer/cs/llm/BaseLlmClient.cs"
  "D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-lib/src/processer/cs/llm/BedrockLlmClient.cs"
  "D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-lib/src/processer/cs/llm/ILlmClient.cs"
  "D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-lib/src/processer/cs/llm/LLMConfigLoader.cs"
  "D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-lib/src/processer/cs/llm/LLMCsProcessor.cs"
  "D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-lib/src/processer/cs/llm/LlmCacheManager.cs"
  "D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-lib/src/processer/cs/llm/LlmClientFactory.cs"
  "D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-lib/src/processer/cs/llm/LlmModels.cs"
  "D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-lib/src/processer/cs/llm/LlmPrompts.cs"
  "D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-lib/src/processer/cs/llm/LlmVerificationCache.cs"
  "D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-lib/src/processer/cs/llm/LocalLlmClient.cs"
  "D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-lib/src/processer/cs/regex/RegexCsProcessor.cs"
  "D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-lib/src/processer/cs/roslyn/ILiteralFilter.cs"
  "D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-lib/src/processer/cs/roslyn/RoslynCsProcessor.cs"
  "D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-lib/src/processer/cs/roslyn/RoslynStringLiteralFilter.cs"
  "D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-lib/src/processer/xaml/XamlExtractor.cs"
  "D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-lib/src/processer/xaml/XamlProcessor.cs"
  "D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-lib/src/replacer/CsReplacer.cs"
  "D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-lib/src/replacer/FileOffsetManager.cs"
  "D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-lib/src/replacer/IReplacer.cs"
  "D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-lib/src/replacer/XamlReplacer.cs"
  "D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-lib/src/utils/LLMConfigPathResolver.cs"
  "D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-lib/src/utils/LocalizationUtils.cs"
  "D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-lib/src/utils/PathUtils.cs"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-replacer/build/CMakeFiles/cmake.verify_globs")
endif()

# CS_FILES at CMakeLists.txt:11 (file)
file(GLOB_RECURSE NEW_GLOB LIST_DIRECTORIES false "D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-replacer/src/*.cs")
set(OLD_GLOB
  "D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-replacer/src/Program.cs"
  "D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-replacer/src/ToolOptions.cs"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-replacer/build/CMakeFiles/cmake.verify_globs")
endif()
