/*
 * Copyright 2021-present Alcon. All rights reserved.
 * Alcon proprietary/confidential.
 * Use is subject to license terms.
 */

using NUnit.Framework;
using System.IO;

namespace LocalizationLib.Tests;

[TestFixture]
public class RoslynCsProcessorTests
{
    private ICsProcessor _processor;

    [SetUp]
    public void Setup()
    {
        _processor = new RoslynCsProcessor();
    }

    [Test]
    public void ProcessCsFile_FindOnly_ReportsButDoesNotModifyFile()
    {
        // 1) Write a minimal .cs file
        string tempFile = Path.GetTempFileName() + ".cs";
        File.WriteAllText(tempFile, @"Console.WriteLine(""Hello"");");

        try
        {
            // 2) Process with findOnly == true
            var result = _processor.ProcessCsFile(tempFile, groupId: "TestGroup");

            // 3) Check the result
            Assert.AreEqual(1, result.Replacements.Count);
            Assert.AreEqual("Hello", result.Replacements[0].OriginalValue);

            // 4) The file on disk is unchanged
            string contentAfter = File.ReadAllText(tempFile);
            StringAssert.Contains("Console.WriteLine(\"Hello\");", contentAfter);

            // 5) The processor’s AllReplacementRecords has 1 record
            Assert.AreEqual(1, _processor.AllReplacementRecords.Count);
        }
        finally
        {
            if (File.Exists(tempFile))
            {
                File.Delete(tempFile);
            }
        }
    }
}
