﻿using System;
using System.Collections.Generic;
using System.Linq;

#if WINDOWS
using System.Windows;
#endif

namespace Alcon.Interop.Translate;

public class TranslateMetadata
{
    #region PUBLIC PROPERTIES

    public static string ValueSeparator => "|";
    public static string IdPrefix => "id:";
    public static string EnglishTextPrefix => "en:";
    public static string FontPrefix => "f:";
    public static string WidthPrefix => "w:";
    public static string HeightPrefix => "h:";
    public static string CanChangeFontPrefix => "cf:";
    public static string WrapPrefix => "wr:";
    public static string GroupPrefix => "gr:";

    public static string PlaceholderNumber => @"(?i)%\[number\]";
    public static string PlaceholderDate => @"(?i)%\[date\]";
    public static string PlaceholderString => @"(?i)%\[string\]";
    public static string Placeholders => $"{PlaceholderNumber}|{PlaceholderDate}|{PlaceholderString}";

    #endregion

    #region PUBLIC METHODS

    public static bool TryGetValue(string metadata, string prefix, out string value)
    {
        value = string.IsNullOrWhiteSpace(metadata) || string.IsNullOrWhiteSpace(prefix)
              ? null
              : metadata
                .Split(ValueSeparator.ToCharArray())
                .Select(v => v.Trim())
                .FirstOrDefault(v => v.StartsWith(prefix, StringComparison.OrdinalIgnoreCase) && v.Length > prefix.Length)?
                .Substring(prefix.Length);

        return !string.IsNullOrWhiteSpace(value);
    }

    public static bool TryGetValue(List<string> values, string prefix, out string value)
    {
        value = values == null || values.Count == 0 || string.IsNullOrWhiteSpace(prefix)
              ? null
              : values.FirstOrDefault(v => v.StartsWith(prefix, StringComparison.OrdinalIgnoreCase) && v.Length > prefix.Length)?
                .Substring(prefix.Length);

        return !string.IsNullOrWhiteSpace(value);
    }

    /// <summary>
    /// Used to define the translation metadata in code. It executes nothing, it is only used by the translation parser to gather translation data
    /// </summary>
    /// <param name="metadata"></param>
    public static string Data(string metadata)
    {
        //--- does nothing.
        return null;
    }

    public static string FromCapitalToWordSplit(string value)
    {
        if (string.IsNullOrWhiteSpace(value))
            return value;

        var ch = value.ToList();
        int n = ch.Count;
        for (int i = n - 1; i > 0; i--)
            if ((i > 0 && !char.IsUpper(ch[i - 1]) && char.IsUpper(ch[i]))
                ||
                (i > 0 && char.IsLetter(ch[i - 1]) && !char.IsLetter(ch[i]))
                ||
                (i > 0 && !char.IsLetter(ch[i - 1]) && char.IsLetter(ch[i]))
                )
                ch.Insert(i, ' ');
        return "en:" + new string(ch.ToArray());
    }

    #endregion

#if WINDOWS

    #region DEPENDENCY PROPERTIES

    #region DECLARATIONS

    public static readonly DependencyProperty DataProperty =
        DependencyProperty.RegisterAttached("Data", typeof(string), typeof(TranslateMetadata), new PropertyMetadata(null));

    #endregion

    #region GETTERS/SETTERS

    public static string GetData(DependencyObject obj)
    {
        return (string)obj.GetValue(DataProperty);
    }

    public static void SetData(DependencyObject obj, string value)
    {
        obj.SetValue(DataProperty, value);
    }

    #endregion

    #endregion

#endif
}
