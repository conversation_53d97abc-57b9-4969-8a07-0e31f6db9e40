/*
 * Copyright 2021-present Alcon. All rights reserved.
 * Alcon proprietary/confidential.
 * Use is subject to license terms.
 */

using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading.Tasks;

namespace LocalizationLib;

/// <summary>
/// Base class for LLM clients providing common functionality and patterns
/// </summary>
public abstract class BaseLlmClient : ILlmClient, IDisposable
{
    protected readonly LLMConfig _cfg;
    protected readonly HttpClient _httpClient;
    private bool _disposed = false;

    protected BaseLlmClient(LLMConfig cfg)
    {
        _cfg = cfg ?? throw new ArgumentNullException(nameof(cfg));
        _httpClient = CreateHttpClient();
    }

    #region Abstract Methods

    /// <summary>
    /// Creates the HTTP client with provider-specific configuration
    /// </summary>
    protected abstract HttpClient CreateHttpClient();

    /// <summary>
    /// Calls the LLM API for discovery operations
    /// </summary>
    protected abstract Task<string> CallLlmForDiscovery(string systemPrompt, string userPrompt);

    /// <summary>
    /// Calls the LLM API for verification operations
    /// </summary>
    protected abstract Task<string> CallLlmForVerification(string systemPrompt, string userPrompt);

    /// <summary>
    /// Parses the discovery response from the LLM
    /// </summary>
    protected abstract List<ReplacementRecord> ParseDiscoveryResponse(string rawResponse, string groupId, string filePath);

    /// <summary>
    /// Parses the verification response from the LLM
    /// </summary>
    protected abstract TranslationVerificationResponse ParseVerificationResponse(string rawResponse);

    #endregion

    #region ILlmClient Implementation

    public virtual List<ReplacementRecord> DiscoverAndProposeReplacements(
        string lineIndexedCode,
        string groupId,
        string fileName,
        string filePath)
    {
        try
        {
            // Build user message with template and code
            var userPrompt = BuildDiscoveryUserPrompt(lineIndexedCode, fileName);

            // Call LLM and parse response
            string rawResponse = CallLlmForDiscovery(GetDiscoverySystemPrompt(), userPrompt).Result;
            var partialRecords = ParseDiscoveryResponse(rawResponse, groupId, filePath);

            return partialRecords;
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"LLM discovery failed: {ex.Message}", ex);
        }
    }

    public virtual TranslationVerificationResult VerifyTranslationNeeded(
        string text,
        int lineNumber,
        string lineContent,
        string fileContent,
        string groupId,
        string filePath)
    {
        try
        {
            var systemPrompt = GetCsVerificationSystemPrompt();
            var userPrompt = LlmPrompts.CreateCsVerificationUserPrompt(text, lineNumber, lineContent, fileContent, filePath);

            string rawResponse = CallLlmForVerification(systemPrompt, userPrompt).Result;
            var response = ParseVerificationResponse(rawResponse);

            return new TranslationVerificationResult
            {
                NeedsTranslation = response?.needsTranslation ?? false,
                Reason = response?.reason ?? "Failed to parse response",
                Context = response?.context ?? "",
                LineNumber = lineNumber,
                LineContent = lineContent,
                FilePath = filePath,
                GroupId = groupId
            };
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"LLM C# verification failed: {ex.Message}", ex);
        }
    }

    public virtual TranslationVerificationResult VerifyXamlTranslationNeeded(
        string text,
        string propertyName,
        int lineNumber,
        string lineContent,
        string xamlContent,
        string filePath,
        string groupId)
    {
        try
        {
            var systemPrompt = GetXamlVerificationSystemPrompt();
            var userPrompt = LlmPrompts.CreateXamlVerificationUserPrompt(text, propertyName, lineNumber, lineContent, xamlContent, filePath);

            string rawResponse = CallLlmForVerification(systemPrompt, userPrompt).Result;
            var response = ParseVerificationResponse(rawResponse);

            return new TranslationVerificationResult
            {
                NeedsTranslation = response?.needsTranslation ?? false,
                Reason = response?.reason ?? "Failed to parse response",
                Context = response?.context ?? "",
                LineNumber = lineNumber,
                LineContent = lineContent,
                FilePath = filePath,
                GroupId = groupId
            };
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"LLM XAML verification failed: {ex.Message}", ex);
        }
    }

    public virtual TranslationVerificationResult VerifyCppTranslationNeeded(
        string text,
        string stringType,
        int lineNumber,
        string lineContent,
        string fileContent,
        string filePath,
        string groupId)
    {
        try
        {
            var systemPrompt = GetCppVerificationSystemPrompt();
            var userPrompt = LlmPrompts.CreateCppVerificationUserPrompt(text, stringType, lineNumber, lineContent, fileContent, filePath);

            string rawResponse = CallLlmForVerification(systemPrompt, userPrompt).Result;
            var response = ParseVerificationResponse(rawResponse);

            return new TranslationVerificationResult
            {
                NeedsTranslation = response?.needsTranslation ?? false,
                Reason = response?.reason ?? "Failed to parse response",
                Context = response?.context ?? "",
                LineNumber = lineNumber,
                LineContent = lineContent,
                FilePath = filePath,
                GroupId = groupId
            };
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"LLM C++ verification failed: {ex.Message}", ex);
        }
    }

    public LLMConfig GetConfig()
    {
        return _cfg;
    }

    #endregion

    #region Protected Helper Methods

    /// <summary>
    /// Gets the system prompt for discovery operations
    /// </summary>
    protected abstract string GetDiscoverySystemPrompt();

    /// <summary>
    /// Gets the system prompt for C# verification
    /// </summary>
    protected abstract string GetCsVerificationSystemPrompt();

    /// <summary>
    /// Gets the system prompt for XAML verification
    /// </summary>
    protected abstract string GetXamlVerificationSystemPrompt();

    /// <summary>
    /// Gets the system prompt for C++ verification
    /// </summary>
    protected abstract string GetCppVerificationSystemPrompt();

    /// <summary>
    /// Builds the user prompt for discovery operations
    /// </summary>
    protected virtual string BuildDiscoveryUserPrompt(string lineIndexedCode, string fileName)
    {
        string userPrompt = _cfg.promptTemplate;
        if (!string.IsNullOrEmpty(fileName))
        {
            userPrompt = userPrompt?.Replace("{{fileName}}", fileName) ?? 
                        "Analyze this code and identify user-facing strings that need translation:";
        }

        return userPrompt + $"\n\n```csharp\n{lineIndexedCode}\n```";
    }

    /// <summary>
    /// Determines if an exception is retryable
    /// </summary>
    protected virtual bool IsRetryableException(Exception ex)
    {
        return ex is TaskCanceledException ||  // Timeout
               ex is HttpRequestException ||   // Network issues
               ex is System.Net.Sockets.SocketException; // Connection issues
    }

    /// <summary>
    /// Calculates exponential backoff delay with a maximum cap
    /// </summary>
    protected virtual int CalculateExponentialBackoff(int attempt)
    {
        // Base delay starts at 1 second, doubles each attempt
        int baseDelay = (int)Math.Pow(2, attempt - 1) * 1000;

        // Cap at 30 seconds to avoid extremely long waits
        int maxDelay = 30000;

        return Math.Min(baseDelay, maxDelay);
    }

    /// <summary>
    /// Executes an operation with retry logic
    /// </summary>
    protected async Task<T> ExecuteWithRetry<T>(
        Func<Task<T>> operation,
        int maxRetries,
        Func<Exception, int, bool> shouldRetry = null,
        Func<int, int> calculateDelay = null)
    {
        int attempt = 0;
        while (true)
        {
            attempt++;
            try
            {
                return await operation();
            }
            catch (Exception ex) when (attempt < maxRetries)
            {
                bool retry = shouldRetry?.Invoke(ex, attempt) ?? IsRetryableException(ex);
                if (!retry)
                    throw;

                int delay = calculateDelay?.Invoke(attempt) ?? CalculateExponentialBackoff(attempt);
                await Task.Delay(delay);
            }
            catch
            {
                // If last attempt or non-retryable error, rethrow
                throw;
            }
        }
    }

    #endregion

    #region IDisposable Implementation

    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    protected virtual void Dispose(bool disposing)
    {
        if (!_disposed && disposing)
        {
            _httpClient?.Dispose();
            _disposed = true;
        }
    }

    #endregion
}
