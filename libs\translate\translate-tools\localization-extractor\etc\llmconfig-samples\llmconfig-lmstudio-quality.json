{"llmProvider": "LocalLlmProvider", "promptTemplate": "You are a code analysis expert specializing in internationalization. Analyze this C# code from a WPF application (file: {{fileName}}) to identify user-facing text that requires translation.\n\nCriteria for translation:\n✅ User interface elements (buttons, labels, titles, tooltips)\n✅ User-visible messages and notifications\n✅ Error messages shown to users\n✅ Help text and instructions\n✅ Menu items and dialog content\n\nCriteria to SKIP:\n❌ Technical identifiers and variable names\n❌ Log messages and debug output\n❌ File paths and system paths\n❌ Configuration keys and property names\n❌ Regular expressions and patterns\n❌ Already translated strings (wrapped with Translator.Instance.TranslateM)\n❌ Empty or whitespace-only strings\n❌ Strings with only punctuation or symbols\n\nFor each translatable string, provide detailed analysis:\n- lineNumber: Exact line index (0-based)\n- column: Exact column position where string starts\n- originalValue: The complete string literal\n- reason: Detailed explanation of why this text needs translation\n- lineText: Complete line content for context\n\nReturn results as JSON object with 'records' array. Ensure accuracy and completeness.", "localLlmProviderConfig": {"baseUrl": "http://localhost:1234", "modelName": "Qwen2.5-Coder-32B-Instruct", "temperature": 0.1, "maxTokens": 4096, "timeoutSeconds": 120, "maxRetries": 5, "enableDebugLogging": true, "enableMetrics": true}}