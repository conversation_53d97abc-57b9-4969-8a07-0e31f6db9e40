﻿
<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup Label="Globals">
    <ProjectGuid>{0E313E6D-A6C0-3AC5-A116-97BABF7FD877}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <Configurations>Debug;Release</Configurations>
    <EnableDefaultItems>false</EnableDefaultItems>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
    <ManagedAssembly>true</ManagedAssembly>
    <TargetFramework>net8.0</TargetFramework>
    <AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
    <OutputType>Exe</OutputType>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)' == 'Debug'">
    <OutputPath>D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-replacer\build\Debug\</OutputPath>
    <AssemblyName>localization_replacer</AssemblyName>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    <DebugType>full</DebugType>
    <DefineConstants>TRACE;DEBUG;_DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <LangVersion>latest</LangVersion>
    <Optimize>false</Optimize>
    <Platform>x64</Platform>
    <WarningLevel>3</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)' == 'Release'">
    <OutputPath>D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-replacer\build\Release\</OutputPath>
    <AssemblyName>localization_replacer</AssemblyName>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    <DebugType>none</DebugType>
    <DefineConstants>TRACE;_DISABLE_CONSTEXPR_MUTEX_CONSTRUCTOR</DefineConstants>
    <ErrorReport>queue</ErrorReport>
    <LangVersion>latest</LangVersion>
    <Optimize>true</Optimize>
    <Platform>x64</Platform>
    <WarningLevel>1</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <None Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-replacer\CMakeLists.txt">
      <Link>CMakeLists.txt</Link>
    </None>
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeCSharpInformation.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeGenericSystem.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeInitializeConfigs.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeSystemSpecificInformation.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeSystemSpecificInitialize.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\FindGit.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\FindPackageHandleStandardArgs.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\FindPackageMessage.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Windows-Initialize.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Windows.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\WindowsPaths.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="D:\kibisoft\workarea\git\interop\cmake\Globals.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="D:\kibisoft\workarea\git\interop\cmake\VsPackageReferences.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-replacer\build\CMakeFiles\4.0.0-rc4\CMakeCSharpCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-replacer\build\CMakeFiles\4.0.0-rc4\CMakeSystem.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-replacer\build\CMakeFiles\cmake.verify_globs" />
  </ItemGroup>
  <Target Condition="'$(Configuration)' == 'Debug'"
    Name="CustomCommand_Debug_a16e987d9f2ade71e68ac20bc95a30c3"
    Inputs="D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-replacer/CMakeLists.txt;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeCSharpInformation.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeGenericSystem.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\FindGit.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Windows-Initialize.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Windows.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\WindowsPaths.cmake;D:\kibisoft\workarea\git\interop\cmake\Globals.cmake;D:\kibisoft\workarea\git\interop\cmake\VsPackageReferences.cmake;D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-replacer\build\CMakeFiles\4.0.0-rc4\CMakeCSharpCompiler.cmake;D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-replacer\build\CMakeFiles\4.0.0-rc4\CMakeSystem.cmake;D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-replacer\build\CMakeFiles\cmake.verify_globs"
    Outputs="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-replacer\build\CMakeFiles\generate.stamp"
    BeforeTargets="CoreCompile"
    DependsOnTargets="PrepareForBuild">
    <Exec Command="echo Building Custom Rule D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-replacer/CMakeLists.txt" />
    <Exec Command="setlocal&#10;&quot;C:\Program Files\CMake\bin\cmake.exe&quot; -SD:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-replacer -BD:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-replacer/build --check-stamp-file D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-replacer/build/CMakeFiles/generate.stamp&#10;if %errorlevel% neq 0 goto :cmEnd&#10;:cmEnd&#10;endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone&#10;:cmErrorLevel&#10;exit /b %1&#10;:cmDone&#10;if %errorlevel% neq 0 goto :VCEnd&#10;:VCEnd" />
  </Target>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeCSharpInformation.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeGenericSystem.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeInitializeConfigs.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeSystemSpecificInformation.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeSystemSpecificInitialize.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\FindGit.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\FindPackageHandleStandardArgs.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\FindPackageMessage.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Windows-Initialize.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Windows.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\WindowsPaths.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="D:\kibisoft\workarea\git\interop\cmake\Globals.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="D:\kibisoft\workarea\git\interop\cmake\VsPackageReferences.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-replacer\build\CMakeFiles\4.0.0-rc4\CMakeCSharpCompiler.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-replacer\build\CMakeFiles\4.0.0-rc4\CMakeSystem.cmake" />
  </ItemGroup>
  <ItemGroup>
    <UpToDateCheckInput Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-replacer\build\CMakeFiles\cmake.verify_globs" />
  </ItemGroup>
  <Target Condition="'$(Configuration)' == 'Release'"
    Name="CustomCommand_Release_a16e987d9f2ade71e68ac20bc95a30c3"
    Inputs="D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-replacer/CMakeLists.txt;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeCSharpInformation.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeGenericSystem.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\FindGit.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Windows-Initialize.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Windows.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\WindowsPaths.cmake;D:\kibisoft\workarea\git\interop\cmake\Globals.cmake;D:\kibisoft\workarea\git\interop\cmake\VsPackageReferences.cmake;D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-replacer\build\CMakeFiles\4.0.0-rc4\CMakeCSharpCompiler.cmake;D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-replacer\build\CMakeFiles\4.0.0-rc4\CMakeSystem.cmake;D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-replacer\build\CMakeFiles\cmake.verify_globs"
    Outputs="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-replacer\build\CMakeFiles\generate.stamp"
    BeforeTargets="CoreCompile"
    DependsOnTargets="PrepareForBuild">
    <Exec Command="echo Building Custom Rule D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-replacer/CMakeLists.txt" />
    <Exec Command="setlocal&#10;&quot;C:\Program Files\CMake\bin\cmake.exe&quot; -SD:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-replacer -BD:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-replacer/build --check-stamp-file D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-replacer/build/CMakeFiles/generate.stamp&#10;if %errorlevel% neq 0 goto :cmEnd&#10;:cmEnd&#10;endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone&#10;:cmErrorLevel&#10;exit /b %1&#10;:cmDone&#10;if %errorlevel% neq 0 goto :VCEnd&#10;:VCEnd" />
  </Target>
  <ItemGroup>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-replacer\src\Program.cs">
      <Link>src\Program.cs</Link>
    </Compile>
    <Compile Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-replacer\src\ToolOptions.cs">
      <Link>src\ToolOptions.cs</Link>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="CommandLineParser" Version="2.9.1" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-replacer\build\ZERO_CHECK.vcxproj">
      <Project>{7DE76000-0EC1-3AA2-8056-9DA661FB28BC}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-replacer\build\trtralocali\localizationlib.csproj">
      <Project>{E9C03864-1F46-398B-B9C6-C4515F3BA72F}</Project>
      <Name>localizationlib</Name>
      <SkipGetTargetFrameworkProperties>true</SkipGetTargetFrameworkProperties>
    </ProjectReference>
  </ItemGroup>
</Project>