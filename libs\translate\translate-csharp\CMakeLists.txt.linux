include("${ROOT_DIR}/cmake/SetupCsproj.cmake")

is_installed("${PROJECT_NAME}.csproj" IS_INSTALLED)
if (${IS_INSTALLED}) 
    return() 
endif()  

assert_string_not_empty(${CMAKE_BUILD_TYPE})

if(NOT ${PROJECT_NAME}_OUTPUT_DIR)
    set(OUTPUT_DIR "${CMAKE_CURRENT_BINARY_DIR}/${CMAKE_BUILD_TYPE}")    
    set(${PROJECT_NAME}_OUTPUT_DIR "${OUTPUT_DIR}")
endif()

create_tagged_sources("${${PROJECT_NAME}_SOURCES}" TAGGED_SOURCES) 
    
create_and_build_csproj("${CMAKE_CURRENT_BINARY_DIR}/${PROJECT_NAME}.csproj"
    CONFIGURATION ${CMAKE_BUILD_TYPE}
    DOTNET_SDK "${ROOT_DOTNET_SDK}"
    TARGET_FRAMEWORK "${ROOT_DOTNET_TARGET_FRAMEWORK}"
    BUILD_ARCH "${ROOT_BUILD_ARCH}"
    OUTPUT_DIR "${${PROJECT_NAME}_OUTPUT_DIR}"
    OUTPUT_TYPE "Library"
    SOURCE_GROUPS "${TAGGED_SOURCES}"    
)