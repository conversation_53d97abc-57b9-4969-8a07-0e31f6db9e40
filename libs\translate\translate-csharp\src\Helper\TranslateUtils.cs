﻿namespace Alcon.Interop.Translate.Helper;

public class TranslateUtils
{
    #region PUBLIC METHODS

    public static string GetTextId(string metadata)
    {
        return string.IsNullOrEmpty(metadata)
            ? null
            : TranslateMetadata.TryGetValue(metadata, TranslateMetadata.IdPrefix, out string id) && !string.IsNullOrEmpty(id)
            ? id
            : $"{HashString(metadata)}";
    }

    #endregion

    #region PRIVATE METHODS

    private static int HashString(string text)
    {
        if (string.IsNullOrEmpty(text))
        {
            return 0;
        }

        unchecked
        {
            int hash = 23;
            foreach (char c in text)
            {
                hash = hash * 31 + c;
            }
            return hash;
        }
    }

    #endregion
}
