{"llmProvider": "BedrockProvider", "promptTemplate": "You are analyzing C# code from a WPF application (file: {{fileName}}). Your task is to identify user-facing text that needs translation.", "bedrockProviderConfig": {"region": "us-east-1", "modelId": "anthropic.claude-3-sonnet-20240229-v1:0", "temperature": 0.0, "maxRetries": 2, "timeoutSeconds": 60, "useChunking": true, "chunkSize": 2000, "maxTokens": 4096, "enableDebugLogging": true, "enableMetrics": true, "accessKey": "INVALID_KEY_TO_CAUSE_ERROR", "secretKey": "INVALID_SECRET_TO_CAUSE_ERROR"}}