{"format": 1, "restore": {"D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-replacer\\build\\localization_replacer.csproj": {}}, "projects": {"D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-replacer\\build\\localization_replacer.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-replacer\\build\\localization_replacer.csproj", "projectName": "localization_replacer", "projectPath": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-replacer\\build\\localization_replacer.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-replacer\\build\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "D:\\kibisoft\\workarea\\VisionPlanner\\NGSS_nugets": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-replacer\\build\\trtralocali\\localizationlib.csproj": {"projectPath": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-replacer\\build\\trtralocali\\localizationlib.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"CommandLineParser": {"target": "Package", "version": "[2.9.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.400/PortableRuntimeIdentifierGraph.json"}}}, "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-replacer\\build\\trtralocali\\localizationlib.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-replacer\\build\\trtralocali\\localizationlib.csproj", "projectName": "localizationlib", "projectPath": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-replacer\\build\\trtralocali\\localizationlib.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-replacer\\build\\trtralocali\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "D:\\kibisoft\\workarea\\VisionPlanner\\NGSS_nugets": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"CommunityToolkit.Mvvm": {"target": "Package", "version": "[8.2.2, )"}, "Microsoft.CodeAnalysis.CSharp.Scripting": {"target": "Package", "version": "[4.10.0, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.400/PortableRuntimeIdentifierGraph.json"}}}}}