/*
* Copyright 2021-present Alcon. All rights reserved.
* Alcon proprietary/confidential.
* Use is subject to license terms.
*/

using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Xml;
using System.Xml.Linq;
using System.Text.RegularExpressions;
using System.Net;
using LocalizationLib.Llm;

namespace LocalizationLib;

/// <summary>
/// Processes individual XAML files:
/// - Finds hard-coded text with TranslateConverter bindings.
/// - Ensures the TranslateConverter resource is present.
/// - Records per-file processing details including line number and column.
/// </summary>
public class XamlProcessor
{
    private readonly ILlmClient _llmClient;
    private readonly bool _useLlmVerification;
    private readonly string _projectPath;
    private readonly LLMConfig _llmConfig;
    private readonly LlmCacheManager _llmCacheManager;

    // Dictionary of known non-dependency properties
    private static readonly HashSet<string> NonDependencyProperties = new HashSet<string>
    {
        "Tag",           // Common non-dependency property
        "Name",          // Name property is not a dependency property
        "DataContext",   // DataContext is not a dependency property
        "ItemsSource",   // ItemsSource is not a dependency property
        "SelectedItem",  // SelectedItem is not a dependency property
        "SelectedValue", // SelectedValue is not a dependency property
        "SelectedIndex"  // SelectedIndex is not a dependency property
    };

    // Dictionary of properties that support binding and should be processed
    private static readonly HashSet<string> SupportedProperties = new HashSet<string>
    {
        "Content",  // Button, ContentControl
        "Text",     // TextBlock, TextBox
        "Header",   // GroupBox, TabItem
        "ToolTip",  // ToolTip
        "Title"     // Window, Page
    };

    // Dictionary of custom controls where certain properties are not dependency properties
    private static readonly Dictionary<string, HashSet<string>> HardcodedCustomControlNonDependencyProperties = new Dictionary<string, HashSet<string>>
    {
        { "RefractionControl", new HashSet<string> { "Title", "Header" } }
    };

    public XamlProcessor(string projectPath = null, ILlmClient llmClient = null)
    {
        _llmClient = llmClient;
        _useLlmVerification = llmClient != null;
        _projectPath = projectPath;
        _llmConfig = llmClient?.GetConfig();
        _llmCacheManager = new LlmCacheManager(projectPath ?? ".");
    }

    #region PUBLIC PROPERTIES
    // Public collection of all replacement records generated.
    public List<ReplacementRecord> AllReplacementRecords { get; } = new List<ReplacementRecord>();

    #endregion

    #region PUBLIC METHODS
    /// <summary>
    /// Processes a single XAML file.
    /// </summary>
    /// <param name="xamlPath">Full path of the XAML file to process.</param>
    /// <param name="groupId">The group id to assign to each ReplacementRecord generated from this file.</param>
    /// <returns>A FileProcessingResult containing replacement details.</returns>
    public FileProcessingResult ProcessXamlFile(string xamlPath, string groupId = "DefaultGroup")
    {
        string originalContent = File.ReadAllText(xamlPath);
        FileProcessingResult fileResult = new FileProcessingResult { FilePath = xamlPath };

        // Load the XAML content
        var doc = XDocument.Parse(originalContent);

        // Process all elements in the document
        ProcessElement(doc.Root, originalContent, xamlPath, groupId, fileResult);

        return fileResult;
    }

    /// <summary>
    /// Gets the merged dictionary of custom control non-dependency properties from both hardcoded values and configuration.
    /// This method is useful for debugging and testing purposes.
    /// 
    /// The custom control non-dependency properties can be configured in the ProjectTextExtractorConfig.json file
    /// under the "CustomControlNonDependencyProperties" section. Example:
    /// 
    /// {
    ///   "CustomControlNonDependencyProperties": {
    ///     "MyCustomControl": ["CustomProperty1", "CustomProperty2"],
    ///     "AnotherControl": ["Title", "Description"]
    ///   }
    /// }
    /// 
    /// These properties will be merged with the hardcoded properties defined in the code.
    /// </summary>
    /// <returns>Dictionary containing merged custom control non-dependency properties.</returns>
    public Dictionary<string, HashSet<string>> GetMergedCustomControlNonDependencyProperties()
    {
        return GetMergedCustomControlNonDependencyPropertiesInternal();
    }

    #endregion

    #region PRIVATE METHODS
    /// <summary>
    /// Gets the merged dictionary of custom control non-dependency properties from both hardcoded values and configuration.
    /// </summary>
    /// <returns>Dictionary containing merged custom control non-dependency properties.</returns>
    private Dictionary<string, HashSet<string>> GetMergedCustomControlNonDependencyPropertiesInternal()
    {
        var mergedProperties = new Dictionary<string, HashSet<string>>();

        // Start with hardcoded properties
        foreach (var kvp in HardcodedCustomControlNonDependencyProperties)
        {
            mergedProperties[kvp.Key] = new HashSet<string>(kvp.Value);
        }

        // Try to load from configuration if project path is available
        if (!string.IsNullOrEmpty(_projectPath))
        {
            try
            {
                var config = ProjectTextExtractorConfigManager.GetConfigSafe(_projectPath);
                if (config?.CustomControlNonDependencyProperties != null)
                {
                    foreach (var kvp in config.CustomControlNonDependencyProperties)
                    {
                        if (mergedProperties.ContainsKey(kvp.Key))
                        {
                            // Merge with existing hardcoded properties
                            foreach (var property in kvp.Value)
                            {
                                mergedProperties[kvp.Key].Add(property);
                            }
                        }
                        else
                        {
                            // Add new control type
                            mergedProperties[kvp.Key] = new HashSet<string>(kvp.Value);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Warning: Could not load custom control non-dependency properties from config: {ex.Message}");
            }
        }

        return mergedProperties;
    }

    private void ProcessElement(XElement element, string originalContent, string xamlPath, string groupId, FileProcessingResult fileResult)
    {
        string elementName = element.Name.LocalName;
        bool isRootElement = element == element.Document.Root;

        // Get the merged custom control non-dependency properties
        var customControlNonDependencyProperties = GetMergedCustomControlNonDependencyPropertiesInternal();

        // For custom controls, we need to check if it's in our CustomControlNonDependencyProperties
        bool isCustomControl = customControlNonDependencyProperties.ContainsKey(elementName);

        // Process attributes
        foreach (var attribute in element.Attributes())
        {
            string propertyName = attribute.Name.LocalName;
            string propertyValue = attribute.Value;

            // Skip if the property value is empty or contains only brackets
            if (string.IsNullOrWhiteSpace(propertyValue) || propertyValue == "[]")
                continue;

            // Skip if the property value is a binding expression
            if (propertyValue.StartsWith("{") && propertyValue.EndsWith("}"))
                continue;

            // Check if this is a custom control with non-dependency properties
            bool isNonDependencyProperty = isCustomControl &&
                customControlNonDependencyProperties[elementName].Contains(propertyName);

            // Skip if this is not a supported property
            if (!SupportedProperties.Contains(propertyName) && !isNonDependencyProperty)
            {
                continue;
            }

            // Skip if this is a known non-dependency property
            if (NonDependencyProperties.Contains(propertyName) && !isNonDependencyProperty)
            {
                continue;
            }

            // Calculate line and column numbers
            int lineNumber = 1;
            int columnNumber = 1;
            string lineContent = null;
            string rawOriginalValue = propertyValue;

            if (originalContent != null)
            {
                // Find the position of the actual text value in the raw file
                string attributePattern = $"{propertyName}=\"{propertyValue}\"";
                int attrIndex = originalContent.IndexOf(attributePattern);
                if (attrIndex >= 0)
                {
                    // Find the position of the actual text value (after the ="")
                    int textStartIndex = attrIndex + propertyName.Length + 2; // +2 for ="

                    // Calculate line number by counting newlines before the text
                    lineNumber = originalContent.Take(textStartIndex).Count(c => c == '\n') + 1;

                    // Find the start of the line containing the text
                    int lastNewline = originalContent.LastIndexOf('\n', textStartIndex);

                    // Calculate column number as position after last newline (or start of file)
                    // Add 1 for 1-based column indexing
                    columnNumber = lastNewline == -1 ? textStartIndex + 1 : textStartIndex - lastNewline;
                    lineContent = originalContent.Split('\n')[lineNumber - 1].TrimEnd('\r');
                    rawOriginalValue = propertyValue; // fallback: just use the decoded value
                }
                else
                {
                    FallbackFindLineColumn(originalContent, propertyName, propertyValue, out lineNumber, out columnNumber, out lineContent, out rawOriginalValue);
                }
            }

            // Create a record for verification
            var record = new ReplacementRecord
            {
                Property = propertyName,
                OriginalValue = rawOriginalValue,
                NewKey = $"en:{propertyValue}",
                FilePath = xamlPath,
                LineNumber = lineNumber,
                Column = columnNumber,
                LineContent = lineContent,
                GroupId = groupId,
                IsDependencyProperty = !isNonDependencyProperty,
                ControlType = elementName,
                IsRootElement = isRootElement
            };

            // Verify if the text needs translation
            if (!ShouldProcessText(propertyValue, propertyName, originalContent, xamlPath, groupId, record))
                continue;

            fileResult.Replacements.Add(record);
            AllReplacementRecords.Add(record);
        }

        // Process child elements recursively
        foreach (var childElement in element.Elements())
        {
            ProcessElement(childElement, originalContent, xamlPath, groupId, fileResult);
        }
    }

    private void FallbackFindLineColumn(string originalContent, string propertyName, string propertyValue, out int lineNumber, out int columnNumber, out string lineContent, out string rawOriginalValue)
    {
        lineNumber = 1;
        columnNumber = 1;
        lineContent = null;
        rawOriginalValue = propertyValue;

        // Regex to find all propertyName="...".
        var regex = new System.Text.RegularExpressions.Regex($@"{propertyName}\s*=\s*""([^""]*)""");
        var matches = regex.Matches(originalContent);

        foreach (System.Text.RegularExpressions.Match match in matches)
        {
            string rawValue = match.Groups[1].Value;
            // Decode the value for comparison
            string decoded = System.Net.WebUtility.HtmlDecode(rawValue);

            if (decoded == propertyValue)
            {
                // Find the index in the file
                int attrIndex = match.Index;
                int textStartIndex = attrIndex + propertyName.Length + 2; // +2 for ="
                lineNumber = originalContent.Take(textStartIndex).Count(c => c == '\n') + 1;
                int lastNewline = originalContent.LastIndexOf('\n', textStartIndex);
                columnNumber = lastNewline == -1 ? textStartIndex + 1 : textStartIndex - lastNewline;
                lineContent = originalContent.Split('\n')[lineNumber - 1].TrimEnd('\r');
                rawOriginalValue = rawValue;
                return;
            }
        }

        // Fallback to old logic if nothing found
        string[] lines = originalContent.Split('\n');
        string valueSnippet = propertyValue.Length > 10 ? propertyValue.Substring(0, 10) : propertyValue;
        for (int i = 0; i < lines.Length; i++)
        {
            string line = lines[i];
            if (line.Contains(propertyName) && line.Contains(valueSnippet))
            {
                lineNumber = i + 1;
                int attrStart = line.IndexOf(propertyName);
                columnNumber = attrStart >= 0 ? attrStart + 1 : 1;
                lineContent = line.TrimEnd('\r');
                rawOriginalValue = propertyValue;
                break;
            }
        }
    }

    private bool ShouldProcessText(string text, string propName, string xamlContent, string xamlPath, string groupId, ReplacementRecord record)
    {
        if (string.IsNullOrWhiteSpace(text) || text.StartsWith("{"))
        {
            Console.WriteLine($"\nSkipping text: \"{text}\"");
            Console.WriteLine($"Reason: {(string.IsNullOrWhiteSpace(text) ? "Empty text" : "Already using binding syntax")}");
            return false;
        }

        if (!_useLlmVerification)
        {
            Console.WriteLine($"\nLLM verification disabled, processing text: \"{text}\"");
            return true;
        }

        // Use the singleton instance
        var configManager = ProjectTextExtractorConfigManager.Instance;

        var uniqueKey = configManager.GenerateVerificationKey(record);
        Console.WriteLine("\n" + new string('=', 80));
        Console.WriteLine($"XAML Text Verification");
        Console.WriteLine(new string('=', 80));
        Console.WriteLine($"File: {xamlPath}");
        Console.WriteLine($"Line: {record.LineNumber}");
        Console.WriteLine($"Column: {record.Column}");
        Console.WriteLine($"Group ID: {groupId}");
        Console.WriteLine($"Control Type: {record.ControlType}");
        Console.WriteLine($"Property: {propName}");
        Console.WriteLine($"Text: \"{text}\"");
        Console.WriteLine($"Line Content: {record.LineContent ?? "<empty>"}");
        Console.WriteLine(new string('-', 80));

        // Check cached results from the LLM cache
        var cachedResult = _llmCacheManager.GetVerificationResult(
            record.OriginalValue,
            record.FilePath,
            record.LineNumber,
            record.Column,
            _llmClient,
            _llmConfig);

        if (cachedResult != null)
        {
            Console.WriteLine("Using cached verification result:");
            Console.WriteLine($"Decision: {(cachedResult.NeedsTranslation ? "Needs Translation" : "Skip")}");
            Console.WriteLine($"Reason: {cachedResult.Reason}");
            Console.WriteLine($"Context: {cachedResult.Context}");
            Console.WriteLine($"Timestamp: {cachedResult.Timestamp:yyyy-MM-dd HH:mm:ss}");

            record.Notes.Add($"LLM Verification (Cached): {(cachedResult.NeedsTranslation ? "Needs Translation" : "Skip")}");
            record.Notes.Add($"LLM Reason (Cached): {cachedResult.Reason}");
            record.Notes.Add($"LLM Context (Cached): {cachedResult.Context}");

            record.NeedsTranslation = cachedResult.NeedsTranslation;
            Console.WriteLine(new string('=', 80));
            return cachedResult.NeedsTranslation;
        }

        try
        {
            Console.WriteLine("Requesting LLM verification...");

            var verificationResult = _llmClient.VerifyXamlTranslationNeeded(
                text: text,
                propertyName: propName,
                lineNumber: record.LineNumber,
                lineContent: record.LineContent ?? "",
                xamlContent: xamlContent,
                filePath: xamlPath,
                groupId: groupId
            );

            Console.WriteLine("\nLLM Verification Result:");
            Console.WriteLine($"Decision: {(verificationResult.NeedsTranslation ? "Needs Translation" : "Skip")}");
            Console.WriteLine($"Reason: {verificationResult.Reason}");
            Console.WriteLine($"Context: {verificationResult.Context}");

            // Store the verification result in the LLM cache
            _llmCacheManager.AddVerificationResult(
                text,
                record.FilePath,
                record.LineNumber,
                record.Column,
                verificationResult.NeedsTranslation,
                verificationResult.Reason,
                verificationResult.Context,
                ExtractionSource.Xaml,
                _llmClient,
                _llmConfig,
                record.LineContent
            );

            // Add verification details to notes
            record.Notes.Add($"LLM Verification: {(verificationResult.NeedsTranslation ? "Needs Translation" : "Skip")}");
            record.Notes.Add($"LLM Reason: {verificationResult.Reason}");
            record.Notes.Add($"LLM Context: {verificationResult.Context}");
            record.NeedsTranslation = verificationResult.NeedsTranslation;

            return verificationResult.NeedsTranslation;
        }
        catch (Exception ex)
        {
            Console.WriteLine("\nError during LLM verification:");
            Console.WriteLine($"Exception: {ex.GetType().Name}");
            Console.WriteLine($"Message: {ex.Message}");
            Console.WriteLine($"Stack Trace: {ex.StackTrace}");

            record.Notes.Add($"LLM Verification Error: {ex.Message}");
            Console.WriteLine("\nDefaulting to process text due to error.");
            Console.WriteLine(new string('=', 80));

            return true; // On error, process the text to be safe
        }
    }

    #endregion
}