/*
 * Copyright 2021-present Alcon. All rights reserved.
 * Alcon proprietary/confidential.
 * Use is subject to license terms.
 */

using System.Collections.Generic;

namespace LocalizationLib;

/// <summary>
/// Represents a processor that scans .cs files for user-facing text
/// and optionally replaces them for localization.
/// </summary>
public interface ICsProcessor
{
    /// <summary>
    /// A global list of all replacement records generated by this processor.
    /// </summary>
    List<ReplacementRecord> AllReplacementRecords { get; }

    /// <summary>
    /// Processes a single .cs file, returning a FileProcessingResult with found items.
    /// </summary>
    /// <param name="csPath">Path to the .cs file.</param>
    /// <param name="groupId">Logical grouping ID for the discovered translations.</param>
    /// <returns>A FileProcessingResult summarizing findings.</returns>
    FileProcessingResult ProcessCsFile(string csPath, string groupId);
}
