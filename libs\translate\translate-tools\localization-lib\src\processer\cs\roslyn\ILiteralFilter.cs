/*
 * Copyright 2021-present Alcon. All rights reserved.
 * Alcon proprietary/confidential.
 * Use is subject to license terms.
 */

using Microsoft.CodeAnalysis.CSharp.Syntax;

namespace LocalizationLib;

/// <summary>
/// Decides whether a particular string literal should be skipped
/// based on usage context (e.g., if it's used in FindResource, e.PropertyName, etc.).
/// </summary>
public interface ILiteralFilter
{
    bool ShouldSkip(LiteralExpressionSyntax node, string literalValue);
}