/*
 * Copyright 2021-present Alcon. All rights reserved.
 * Alcon proprietary/confidential.
 * Use is subject to license terms.
 */

namespace LocalizationLib;

/// <summary>
/// Shared prompt templates for LLM clients to ensure consistency
/// </summary>
public static class LlmPrompts
{
    #region Discovery Prompts

    /// <summary>
    /// System prompt for C# code analysis and localization discovery
    /// </summary>
    public const string CsDiscoverySystemPrompt = @"
You are an AI that scans line-indexed C# code from a WPF application. 
Your task is to extract only user-facing text (the text that has meaning for end users and should be translated). 
Do not include:
- Strings that are only punctuation or symbols (with no translatable meaning).
- Strings already wrapped with Translator.Instance.TranslateM(...).

For each user-facing string you find, include it in the records array with:
- lineNumber: the line index from the snippet
- column: the exact column index in that line where the string literal starts
- originalValue: the user-facing string literal discovered
- reason: a brief explanation of why it is user-facing text
- lineText: the entire line from the snippet containing that string

Important:
- If multiple unwrapped translatable strings occur on the same line, include each occurrence in the records array.
- Return only the JSON object with the records array as the final output (no extra text).";

    /// <summary>
    /// System prompt for Bedrock C# code analysis (simplified version)
    /// </summary>
    public const string BedrockCsDiscoverySystemPrompt = @"
You are an AI that scans line-indexed C# code from a WPF application. 
Your task is to identify user-facing text that needs translation.

Return a JSON object with a 'records' array containing objects with these fields:
- lineNumber: The line index in the file
- column: The exact column where the string starts  
- originalValue: The string literal to be translated
- reason: A brief explanation of why it's user-facing text
- lineText: The complete line containing the string

If no translatable text is found, return {""records"": []}.";

    #endregion

    #region Verification Prompts

    /// <summary>
    /// System prompt for C# translation verification
    /// </summary>
    public const string CsVerificationSystemPrompt = @"
You are an AI that analyzes C# code to determine if a specific text needs translation.
Your task is to verify if the given text is user-facing and should be wrapped with Translator.Instance.TranslateM().

Consider these factors:
1. Is the text meaningful to end users?
2. Is it already wrapped with Translator.Instance.TranslateM()?
3. Is it just punctuation or symbols?
4. Is it part of a technical identifier or internal string?
5. Is it in a context where translation would make sense?

IMPORTANT: DO NOT translate these types of strings:
- Switch case values (case ""SomeValue"": ...) - these are internal keys/identifiers
- Enum string representations or mappings
- Configuration keys or setting names
- API endpoint paths or parameter names
- Database field names or SQL identifiers
- File paths, URLs, or system paths
- Technical constants or internal identifiers
- Log levels, error codes, or status codes
- Property names in serialization/deserialization
- Method names, class names, or namespace references
- Regular expressions or format strings
- Internal state machine values

ONLY translate strings that are:
- UI labels, buttons, or menu text shown to users
- Error messages displayed to end users
- Tooltips, help text, or user instructions
- Dialog titles or notification messages
- User-facing status messages or confirmations

Provide a detailed analysis with your reasoning.";

    /// <summary>
    /// System prompt for XAML translation verification
    /// </summary>
    public const string XamlVerificationSystemPrompt = @"
You are an AI that analyzes XAML UI text to determine if it needs translation.
Your task is to verify if the given text in a specific XAML property needs to be wrapped with TranslateConverter.

Consider these factors:
1. Is the text meaningful to end users (labels, buttons, headers, tooltips)?
2. Is it already using a binding or TranslateConverter?
3. Is it just punctuation, symbols, or numbers?
4. Is it a technical identifier or internal string?
5. Does the property type (Content, Text, Header, ToolTip, Title) typically contain user-facing text?
6. Does the surrounding XAML context suggest this is user-facing content?

Provide a detailed analysis with your reasoning.";

    /// <summary>
    /// System prompt for C++ translation verification
    /// </summary>
    public const string CppVerificationSystemPrompt = @"
You are an AI that analyzes C++ code to determine if a specific text needs translation.
Your task is to verify if the given text might be user-facing and should be considered for localization.

IMPORTANT GUIDELINES:
1. When in doubt, mark the text as needing translation. It's better to have users review a few extra strings than to miss important ones.
2. Consider these contexts where text often needs translation:
   - Server responses and error messages (e.g., ""Proto deserialization error"", ""Connection timeout"")
   - API response messages
   - Log messages that might be shown to users
   - Status messages and notifications
   - Configuration or setting descriptions
   - Any text that might be displayed to end users

3. Only skip translation if you are CERTAIN the text:
   - Is purely technical/internal (e.g., variable names, function names)
   - Is a debug-only message (e.g., ""DEBUG: Memory allocation failed"")
   - Is a log message that is not user-facing and for developers only
   - Is a code comment
   - Is a format string with no translatable content (e.g., ""%d:%02d:%02d"")
   - Is a technical identifier or key
   - Is a text that do not have any meaning for end users and not make sense to translate
   - Is a preprocessor macro name
   - Is a logging level (e.g., ""INFO"", ""ERROR"", ""DEBUG"")

4. Consider the string type and context:
   - char*/std::string: Often used for user messages
   - wchar_t*/std::wstring: Typically used for UI or internationalized text
   - Raw string literals: May contain formatted messages
   - String literals in error handling: Often need translation
   - String literals in response generation: Often need translation

5. Special cases that usually need translation:
   - Error messages returned to clients
   - Status messages in server responses
   - Configuration error messages
   - User-facing notifications
   - API response messages
   - Any text that might be displayed in a UI or client application

Provide a detailed analysis with your reasoning, but remember: when uncertain, recommend translation to allow for user review.";

    #endregion

    #region Bedrock Verification Prompts

    /// <summary>
    /// Simplified system prompt for Bedrock C# verification
    /// </summary>
    public const string BedrockCsVerificationSystemPrompt = @"
You are an AI that analyzes C# code to determine if a specific text needs translation.
Your task is to verify if the given text is user-facing and should be wrapped with Translator.Instance.TranslateM().

IMPORTANT: DO NOT translate these types of strings:
- Switch case values (case ""SomeValue"": ...) - these are internal keys/identifiers
- Enum string representations or mappings
- Configuration keys or setting names
- API endpoint paths or parameter names
- Database field names or SQL identifiers
- File paths, URLs, or system paths
- Technical constants or internal identifiers
- Log levels, error codes, or status codes
- Property names in serialization/deserialization
- Method names, class names, or namespace references
- Regular expressions or format strings
- Internal state machine values

ONLY translate strings that are:
- UI labels, buttons, or menu text shown to users
- Error messages displayed to end users
- Tooltips, help text, or user instructions
- Dialog titles or notification messages
- User-facing status messages or confirmations

Return a JSON object with these fields:
- needsTranslation: boolean indicating if translation is needed
- reason: detailed explanation of why the text needs or doesn't need translation
- context: additional context about where and how the text is used";

    /// <summary>
    /// Simplified system prompt for Bedrock XAML verification
    /// </summary>
    public const string BedrockXamlVerificationSystemPrompt = @"
You are an AI that analyzes XAML code to determine if a specific text needs translation.
Focus on user-facing content in WPF applications.

Return a JSON object with these fields:
- needsTranslation: boolean indicating if translation is needed
- reason: detailed explanation of why the text needs or doesn't need translation
- context: additional context about where and how the text is used";

    /// <summary>
    /// Simplified system prompt for Bedrock C++ verification
    /// </summary>
    public const string BedrockCppVerificationSystemPrompt = @"
You are an AI that analyzes C++ code to determine if a specific text needs translation.
Focus on user-facing content in C++ applications.

Return a JSON object with these fields:
- needsTranslation: boolean indicating if translation is needed
- reason: detailed explanation of why the text needs or doesn't need translation
- context: additional context about where and how the text is used";

    #endregion

    #region User Prompt Templates

    /// <summary>
    /// Creates a user prompt for C# verification
    /// </summary>
    public static string CreateCsVerificationUserPrompt(string text, int lineNumber, string lineContent, string fileContent, string filePath)
    {
        // Analyze the context to provide hints about the string usage
        string contextHint = "";
        if (lineContent.Contains("case ") && lineContent.Contains(":"))
        {
            contextHint = "\n\nCONTEXT HINT: This string appears to be in a switch case statement. Switch case values are typically internal keys/identifiers and should NOT be translated.";
        }
        else if (lineContent.Contains("enum") || lineContent.Contains("Enum."))
        {
            contextHint = "\n\nCONTEXT HINT: This string appears to be related to enum handling. Enum string representations are typically internal identifiers and should NOT be translated.";
        }
        else if (lineContent.Contains("config") || lineContent.Contains("Config") || lineContent.Contains("setting") || lineContent.Contains("Setting"))
        {
            contextHint = "\n\nCONTEXT HINT: This string appears to be related to configuration. Configuration keys are typically internal identifiers and should NOT be translated.";
        }
        else if (lineContent.Contains("Log") || lineContent.Contains("Debug") || lineContent.Contains("Trace"))
        {
            contextHint = "\n\nCONTEXT HINT: This string appears to be in a logging context. Log messages for debugging are typically internal and should NOT be translated unless they are user-facing error messages.";
        }

        return $@"Please analyze this text from a C# file:

Text to analyze: ""{text}""
Line number: {lineNumber}
Line content: {lineContent}

File context:
```csharp
{fileContent}
```{contextHint}

Determine if this text needs translation and explain why.";
    }

    /// <summary>
    /// Creates a user prompt for XAML verification
    /// </summary>
    public static string CreateXamlVerificationUserPrompt(string text, string propertyName, int lineNumber, string lineContent, string xamlContent, string filePath)
    {
        return $@"Please analyze this XAML text:

Text to analyze: ""{text}""
Property: {propertyName}
Line number: {lineNumber}
Line content: {lineContent}

XAML context:
```xaml
{xamlContent}
```

Determine if this text needs translation and explain why.";
    }

    /// <summary>
    /// Creates a user prompt for C++ verification
    /// </summary>
    public static string CreateCppVerificationUserPrompt(string text, string stringType, int lineNumber, string lineContent, string fileContent, string filePath)
    {
        return $@"Please analyze this text from a C++ file:

Text to analyze: ""{text}""
String type: {stringType}
Line number: {lineNumber}
Line content: {lineContent}

File context:
```cpp
{fileContent}
```

Determine if this text needs translation and explain why.";
    }

    /// <summary>
    /// Creates a user prompt for Bedrock C# verification (simplified)
    /// </summary>
    public static string CreateBedrockCsVerificationUserPrompt(string text, int lineNumber, string lineContent, string fileContent, string filePath)
    {
        // Analyze the context to provide hints about the string usage
        string contextHint = "";
        if (lineContent.Contains("case ") && lineContent.Contains(":"))
        {
            contextHint = "\n\nCONTEXT HINT: This string appears to be in a switch case statement. Switch case values are typically internal keys/identifiers and should NOT be translated.";
        }
        else if (lineContent.Contains("enum") || lineContent.Contains("Enum."))
        {
            contextHint = "\n\nCONTEXT HINT: This string appears to be related to enum handling. Enum string representations are typically internal identifiers and should NOT be translated.";
        }
        else if (lineContent.Contains("config") || lineContent.Contains("Config") || lineContent.Contains("setting") || lineContent.Contains("Setting"))
        {
            contextHint = "\n\nCONTEXT HINT: This string appears to be related to configuration. Configuration keys are typically internal identifiers and should NOT be translated.";
        }
        else if (lineContent.Contains("Log") || lineContent.Contains("Debug") || lineContent.Contains("Trace"))
        {
            contextHint = "\n\nCONTEXT HINT: This string appears to be in a logging context. Log messages for debugging are typically internal and should NOT be translated unless they are user-facing error messages.";
        }

        return $@"
Text to analyze: ""{text}""
Line number: {lineNumber}
Line content: {lineContent}
File path: {filePath}

Context (surrounding code):
{fileContent}{contextHint}

Should this text be translated?";
    }

    /// <summary>
    /// Creates a user prompt for Bedrock XAML verification (simplified)
    /// </summary>
    public static string CreateBedrockXamlVerificationUserPrompt(string text, string propertyName, int lineNumber, string lineContent, string xamlContent, string filePath)
    {
        return $@"
Text to analyze: ""{text}""
Property name: {propertyName}
Line number: {lineNumber}
Line content: {lineContent}
File path: {filePath}

Context (surrounding XAML):
{xamlContent}

Should this XAML text be translated?";
    }

    /// <summary>
    /// Creates a user prompt for Bedrock C++ verification (simplified)
    /// </summary>
    public static string CreateBedrockCppVerificationUserPrompt(string text, string stringType, int lineNumber, string lineContent, string fileContent, string filePath)
    {
        return $@"
Text to analyze: ""{text}""
String type: {stringType}
Line number: {lineNumber}
Line content: {lineContent}
File path: {filePath}

Context (surrounding C++ code):
{fileContent}

Should this C++ text be translated?";
    }

    #endregion
}
