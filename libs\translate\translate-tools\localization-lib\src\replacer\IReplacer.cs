/*
 * Copyright 2021-present Alcon. All rights reserved.
 * Alcon proprietary/confidential.
 * Use is subject to license terms.
 */

namespace LocalizationLib;

public interface IReplacer
{
    string SupportedFileTypes { get; }

    public ReplaceStatus Replace(string filePath, ExtractedTextInfo text);
}

public enum ReplaceStatus
{
    Replaced,       // the replace was done
    NotReplaced,    // the replace couldn't be done
    AlreadyReplaced // the replace was already there
}