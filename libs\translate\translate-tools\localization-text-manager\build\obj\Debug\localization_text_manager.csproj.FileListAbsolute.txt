D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-text-manager\build\Debug\localization_text_manager.exe
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-text-manager\build\Debug\localization_text_manager.deps.json
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-text-manager\build\Debug\localization_text_manager.runtimeconfig.json
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-text-manager\build\Debug\localization_text_manager.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-text-manager\build\Debug\localization_text_manager.pdb
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-text-manager\build\Debug\CommunityToolkit.Mvvm.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-text-manager\build\Debug\Newtonsoft.Json.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-text-manager\build\Debug\translate_csharp.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-text-manager\build\Debug\ui_extractor_tool.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-text-manager\build\Debug\translate_csharp.pdb
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-text-manager\build\Debug\ui_extractor_tool.pdb
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-text-manager\build\obj\Debug\localization_text_manager.csproj.AssemblyReference.cache
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-text-manager\build\obj\Debug\App.g.cs
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-text-manager\build\obj\Debug\localization_text_manager_MarkupCompile.cache
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-text-manager\build\obj\Debug\localization_text_manager.GeneratedMSBuildEditorConfig.editorconfig
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-text-manager\build\obj\Debug\localization_text_manager.AssemblyInfoInputs.cache
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-text-manager\build\obj\Debug\localization_text_manager.AssemblyInfo.cs
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-text-manager\build\obj\Debug\localization_text_manager.csproj.CoreCompileInputs.cache
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-text-manager\build\obj\Debug\localization_text_manager.sourcelink.json
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-text-manager\build\obj\Debug\localiza.014F5F9B.Up2Date
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-text-manager\build\obj\Debug\localization_text_manager.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-text-manager\build\obj\Debug\refint\localization_text_manager.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-text-manager\build\obj\Debug\localization_text_manager.pdb
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-text-manager\build\obj\Debug\localization_text_manager.genruntimeconfig.cache
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-text-manager\build\obj\Debug\ref\localization_text_manager.dll
