﻿using Alcon.Interop.Translate.Models;

namespace Alcon.Interop.Translate.Helper;

public static class LanguageHelper
{
    public static LanguageModel CreateLanguageModel(string languageName)
    {
        return languageName switch
        {
            "English" => new LanguageModel { Name = languageName, IsoCode = "EN", TranslatedName = "English" },
            "Portuguese" => new LanguageModel { Name = languageName, IsoCode = "PT", TranslatedName = "Português" },
            "Spanish" => new LanguageModel { Name = languageName, IsoCode = "ES", TranslatedName = "Español" },
            "Chinesse" => new LanguageModel { Name = languageName, IsoCode = "ZH", TranslatedName = "中华人民共和国" },
            "Japanesse" => new LanguageModel { Name = languageName, IsoCode = "JA", TranslatedName = "日本" },
            "Korean" => new LanguageModel { Name = languageName, IsoCode = "RN", TranslatedName = "한국어" },
            "German" => new LanguageModel { Name = languageName, IsoCode = "DE", TranslatedName = "Deutsch" },
            "Byelorussian" => new LanguageModel { Name = languageName, IsoCode = "BE", TranslatedName = "Русский" },
            "Dutch" => new LanguageModel { Name = languageName, IsoCode = "NL", TranslatedName = "Dutch" },
            "Czech" => new LanguageModel { Name = languageName, IsoCode = "CS", TranslatedName = "Czech" },
            "Danish" => new LanguageModel { Name = languageName, IsoCode = "DA", TranslatedName = "Dansk" },
            "Finnish" => new LanguageModel { Name = languageName, IsoCode = "DA", TranslatedName = "Suomeksi" },
            "Francais" => new LanguageModel { Name = languageName, IsoCode = "FR", TranslatedName = "Français" },
            "Hungarian" => new LanguageModel { Name = languageName, IsoCode = "HU", TranslatedName = "Magyar" },
            "Italian" => new LanguageModel { Name = languageName, IsoCode = "IT", TranslatedName = "Italiano" },
            "Kazakh" => new LanguageModel { Name = languageName, IsoCode = "KK", TranslatedName = "Русский" },
            "Norwegian" => new LanguageModel { Name = languageName, IsoCode = "NO", TranslatedName = "Norsk" },
            "Polish" => new LanguageModel { Name = languageName, IsoCode = "NO", TranslatedName = "Polski" },
            "Romanian" => new LanguageModel { Name = languageName, IsoCode = "RO", TranslatedName = "Română" },
            "Russian" => new LanguageModel { Name = languageName, IsoCode = "RU", TranslatedName = "Русский" },
            "Slovak" => new LanguageModel { Name = languageName, IsoCode = "RU", TranslatedName = "Slovenský" },
            "Swedish" => new LanguageModel { Name = languageName, IsoCode = "SV", TranslatedName = "Svensk" },
            "Turkish" => new LanguageModel { Name = languageName, IsoCode = "TR", TranslatedName = "Türkçe" },
            "Ukrainian" => new LanguageModel { Name = languageName, IsoCode = "UA", TranslatedName = "Українська" },
            _ => null,
        };
    }
}
