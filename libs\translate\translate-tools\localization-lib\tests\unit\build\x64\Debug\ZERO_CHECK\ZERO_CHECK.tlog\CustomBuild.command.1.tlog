^D:\KIBISOFT\WORKAREA\GIT\INTEROP\LIBS\TRANSLATE\TRANSLATE-TOOLS\LOCALIZATION-LIB\TESTS\UNIT\BUILD\CMAKEFILES\A1F12246291DCFB468CAC735AA77EEE1\GENERATE.STAMP.RULE
setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SD:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-lib/tests/unit -BD:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-lib/tests/unit/build --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file D:/kibisoft/workarea/git/interop/libs/translate/translate-tools/localization-lib/tests/unit/build/localizationlib_unit_tests.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
