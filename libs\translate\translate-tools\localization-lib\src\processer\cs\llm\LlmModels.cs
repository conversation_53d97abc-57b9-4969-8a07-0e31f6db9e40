/*
 * Copyright 2021-present Alcon. All rights reserved.
 * Alcon proprietary/confidential.
 * Use is subject to license terms.
 */

using System.Collections.Generic;

namespace LocalizationLib;

#region Shared Response Models

/// <summary>
/// Result of translation verification analysis
/// </summary>
public class TranslationVerificationResult
{
    public bool NeedsTranslation { get; set; }
    public string Reason { get; set; }
    public string Context { get; set; }
    public int LineNumber { get; set; }
    public string LineContent { get; set; }
    public string FilePath { get; set; }
    public string GroupId { get; set; }
}

/// <summary>
/// Response from LLM for localization discovery
/// </summary>
public class LocalizationResponse
{
    public List<LocalizationRecord> records { get; set; }
}

/// <summary>
/// Individual localization record from LLM
/// </summary>
public class LocalizationRecord
{
    public int lineNumber { get; set; }
    public int column { get; set; }
    public string originalValue { get; set; }
    public string reason { get; set; }
    public string lineText { get; set; }
}

/// <summary>
/// Response from LLM for translation verification
/// </summary>
public class TranslationVerificationResponse
{
    public bool needsTranslation { get; set; }
    public string reason { get; set; }
    public string context { get; set; }
}

#endregion

#region Local LLM Models (OpenAI-compatible)

/// <summary>
/// Chat completion request for OpenAI-compatible APIs
/// </summary>
public class ChatCompletionRequest
{
    public string model { get; set; }
    public List<ChatMessage> messages { get; set; }
    public double temperature { get; set; }
    public int max_tokens { get; set; }
    public bool stream { get; set; }
    public ResponseFormat response_format { get; set; }
}

/// <summary>
/// Chat message for OpenAI-compatible APIs
/// </summary>
public class ChatMessage
{
    public string role { get; set; }
    public string content { get; set; }
}

/// <summary>
/// Chat completion response from OpenAI-compatible APIs
/// </summary>
public class ChatCompletionResponse
{
    public string id { get; set; }
    public string @object { get; set; }
    public int created { get; set; }
    public string model { get; set; }
    public List<ChatChoice> choices { get; set; }
}

/// <summary>
/// Choice in chat completion response
/// </summary>
public class ChatChoice
{
    public int index { get; set; }
    public string finish_reason { get; set; }
    public ChatMessage message { get; set; }
}

#endregion

#region JSON Schema Models (for Local LLM)

/// <summary>
/// Response format specification for structured output
/// </summary>
public class ResponseFormat
{
    public string type { get; set; } = "json_schema";
    public JsonSchemaWrapper json_schema { get; set; }
}

/// <summary>
/// JSON schema wrapper for structured output
/// </summary>
public class JsonSchemaWrapper
{
    public string name { get; set; } = "localization_records";
    public string strict { get; set; } = "true";
    public JsonSchema schema { get; set; }
}

/// <summary>
/// JSON schema definition
/// </summary>
public class JsonSchema
{
    public string type { get; set; } = "object";
    public object properties { get; set; }
    public string[] required { get; set; }
}

/// <summary>
/// Schema properties for localization records
/// </summary>
public class LocalizationSchemaProperties
{
    public JsonSchemaArray records { get; set; }
}

/// <summary>
/// Schema properties for translation verification
/// </summary>
public class TranslationVerificationSchemaProperties
{
    public JsonSchemaProperty needsTranslation { get; set; }
    public JsonSchemaProperty reason { get; set; }
    public JsonSchemaProperty context { get; set; }
}

/// <summary>
/// JSON schema array definition
/// </summary>
public class JsonSchemaArray
{
    public string type { get; set; } = "array";
    public JsonSchemaItems items { get; set; }
}

/// <summary>
/// JSON schema items definition
/// </summary>
public class JsonSchemaItems
{
    public string type { get; set; } = "object";
    public LocalizationRecordSchemaProperties properties { get; set; }
    public string[] required { get; set; }
}

/// <summary>
/// Schema properties for localization record items
/// </summary>
public class LocalizationRecordSchemaProperties
{
    public JsonSchemaProperty lineNumber { get; set; }
    public JsonSchemaProperty column { get; set; }
    public JsonSchemaProperty originalValue { get; set; }
    public JsonSchemaProperty reason { get; set; }
    public JsonSchemaProperty lineText { get; set; }
}

/// <summary>
/// JSON schema property definition
/// </summary>
public class JsonSchemaProperty
{
    public string type { get; set; }
    public string description { get; set; }
}

#endregion

#region Bedrock Models

/// <summary>
/// Response from Bedrock for localization discovery
/// </summary>
public class BedrockDiscoveryResponse
{
    public BedrockDiscoveryRecord[] records { get; set; }
}

/// <summary>
/// Individual discovery record from Bedrock
/// </summary>
public class BedrockDiscoveryRecord
{
    public int lineNumber { get; set; }
    public int column { get; set; }
    public string originalValue { get; set; }
    public string reason { get; set; }
    public string lineText { get; set; }
}

/// <summary>
/// Response from Bedrock for translation verification
/// </summary>
public class BedrockVerificationResponse
{
    public bool needsTranslation { get; set; }
    public string reason { get; set; }
    public string context { get; set; }
}

/// <summary>
/// Response models for different Bedrock models
/// </summary>
public class ClaudeResponse
{
    public ClaudeContent[] content { get; set; }
}

/// <summary>
/// Content from Claude response
/// </summary>
public class ClaudeContent
{
    public string text { get; set; }
}

/// <summary>
/// Response from Amazon Titan
/// </summary>
public class TitanResponse
{
    public TitanResult[] results { get; set; }
}

/// <summary>
/// Result from Titan response
/// </summary>
public class TitanResult
{
    public string outputText { get; set; }
}

/// <summary>
/// Response from Llama models
/// </summary>
public class LlamaResponse
{
    public string generation { get; set; }
}

/// <summary>
/// Tool use response models for structured JSON output
/// </summary>
public class ClaudeToolResponse
{
    public ClaudeToolContent[] content { get; set; }
}

/// <summary>
/// Content from Claude tool use response
/// </summary>
public class ClaudeToolContent
{
    public string type { get; set; }  // "text" or "tool_use"
    public string text { get; set; }  // For text content
    public string id { get; set; }    // For tool use
    public string name { get; set; }  // For tool use
    public object input { get; set; } // For tool use - the structured JSON data
}

#endregion
