{"Version": 1, "WorkspaceRootPath": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-text-manager\\build\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{E947F683-9112-3425-B4CE-89F60C32F003}|ui_extractor_tool\\ui_extractor_tool.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-uitext-extractor\\src\\extractor\\xmltextexporter.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{5EE3347A-9326-37E9-ADBC-A22721CACB82}|translate_csharp\\translate_csharp.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-csharp\\src\\translator.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E947F683-9112-3425-B4CE-89F60C32F003}|ui_extractor_tool\\ui_extractor_tool.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-uitext-extractor\\src\\viewmodels\\statusviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E947F683-9112-3425-B4CE-89F60C32F003}|ui_extractor_tool\\ui_extractor_tool.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-uitext-extractor\\src\\views\\textgrid.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{E947F683-9112-3425-B4CE-89F60C32F003}|ui_extractor_tool\\ui_extractor_tool.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-uitext-extractor\\src\\utils\\excelexporter.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E947F683-9112-3425-B4CE-89F60C32F003}|ui_extractor_tool\\ui_extractor_tool.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-uitext-extractor\\src\\utils\\textinfoextensions.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E947F683-9112-3425-B4CE-89F60C32F003}|ui_extractor_tool\\ui_extractor_tool.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-uitext-extractor\\src\\views\\bulkactions.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{E947F683-9112-3425-B4CE-89F60C32F003}|ui_extractor_tool\\ui_extractor_tool.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-uitext-extractor\\src\\classes\\uiextractedtextinfo.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E947F683-9112-3425-B4CE-89F60C32F003}|ui_extractor_tool\\ui_extractor_tool.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-uitext-extractor\\src\\viewmodels\\uiextractorviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E947F683-9112-3425-B4CE-89F60C32F003}|ui_extractor_tool\\ui_extractor_tool.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-uitext-extractor\\src\\extractor\\wpfuitextextractor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E947F683-9112-3425-B4CE-89F60C32F003}|ui_extractor_tool\\ui_extractor_tool.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-uitext-extractor\\src\\viewmodels\\filterviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 1, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{e1b7d1f8-9b3c-49b1-8f4f-bfc63a88835d}"}, {"$type": "Document", "DocumentIndex": 0, "Title": "XmlTextExporter.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-uitext-extractor\\src\\Extractor\\XmlTextExporter.cs", "RelativeDocumentMoniker": "..\\..\\localization-uitext-extractor\\src\\Extractor\\XmlTextExporter.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-uitext-extractor\\src\\Extractor\\XmlTextExporter.cs", "RelativeToolTip": "..\\..\\localization-uitext-extractor\\src\\Extractor\\XmlTextExporter.cs", "ViewState": "AgIAADgAAAAAAAAAAADwv0UAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T06:39:14.735Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "Translator.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-csharp\\src\\Translator.cs", "RelativeDocumentMoniker": "..\\..\\..\\translate-csharp\\src\\Translator.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-csharp\\src\\Translator.cs", "RelativeToolTip": "..\\..\\..\\translate-csharp\\src\\Translator.cs", "ViewState": "AgIAAH4AAAAAAAAAAAAAwJkAAABmAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T06:33:04.764Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "StatusViewModel.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-uitext-extractor\\src\\ViewModels\\StatusViewModel.cs", "RelativeDocumentMoniker": "..\\..\\localization-uitext-extractor\\src\\ViewModels\\StatusViewModel.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-uitext-extractor\\src\\ViewModels\\StatusViewModel.cs", "RelativeToolTip": "..\\..\\localization-uitext-extractor\\src\\ViewModels\\StatusViewModel.cs", "ViewState": "AgIAABoAAAAAAAAAAAAQwJMCAAAsAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-27T11:37:34.181Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "UIExtractedTextInfo.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-uitext-extractor\\src\\Classes\\UIExtractedTextInfo.cs", "RelativeDocumentMoniker": "..\\..\\localization-uitext-extractor\\src\\Classes\\UIExtractedTextInfo.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-uitext-extractor\\src\\Classes\\UIExtractedTextInfo.cs", "RelativeToolTip": "..\\..\\localization-uitext-extractor\\src\\Classes\\UIExtractedTextInfo.cs", "ViewState": "AgIAAK4AAAAAAAAAAAAEwLsAAAAUAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-28T03:11:20.391Z"}, {"$type": "Document", "DocumentIndex": 4, "Title": "ExcelExporter.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-uitext-extractor\\src\\Utils\\ExcelExporter.cs", "RelativeDocumentMoniker": "..\\..\\localization-uitext-extractor\\src\\Utils\\ExcelExporter.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-uitext-extractor\\src\\Utils\\ExcelExporter.cs", "RelativeToolTip": "..\\..\\localization-uitext-extractor\\src\\Utils\\ExcelExporter.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAMAAAASAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-28T04:55:17.27Z"}, {"$type": "Document", "DocumentIndex": 5, "Title": "TextInfoExtensions.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-uitext-extractor\\src\\Utils\\TextInfoExtensions.cs", "RelativeDocumentMoniker": "..\\..\\localization-uitext-extractor\\src\\Utils\\TextInfoExtensions.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-uitext-extractor\\src\\Utils\\TextInfoExtensions.cs", "RelativeToolTip": "..\\..\\localization-uitext-extractor\\src\\Utils\\TextInfoExtensions.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-28T04:57:16.714Z"}, {"$type": "Document", "DocumentIndex": 6, "Title": "BulkActions.xaml", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-uitext-extractor\\src\\Views\\BulkActions.xaml", "RelativeDocumentMoniker": "..\\..\\localization-uitext-extractor\\src\\Views\\BulkActions.xaml", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-uitext-extractor\\src\\Views\\BulkActions.xaml", "RelativeToolTip": "..\\..\\localization-uitext-extractor\\src\\Views\\BulkActions.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-05-28T03:04:15.934Z"}, {"$type": "Document", "DocumentIndex": 3, "Title": "TextGrid.xaml", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-uitext-extractor\\src\\Views\\TextGrid.xaml", "RelativeDocumentMoniker": "..\\..\\localization-uitext-extractor\\src\\Views\\TextGrid.xaml", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-uitext-extractor\\src\\Views\\TextGrid.xaml", "RelativeToolTip": "..\\..\\localization-uitext-extractor\\src\\Views\\TextGrid.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-05-27T11:36:44.973Z"}, {"$type": "Document", "DocumentIndex": 9, "Title": "WpfUITextExtractor.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-uitext-extractor\\src\\Extractor\\WpfUITextExtractor.cs", "RelativeDocumentMoniker": "..\\..\\localization-uitext-extractor\\src\\Extractor\\WpfUITextExtractor.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-uitext-extractor\\src\\Extractor\\WpfUITextExtractor.cs", "RelativeToolTip": "..\\..\\localization-uitext-extractor\\src\\Extractor\\WpfUITextExtractor.cs", "ViewState": "AgIAAPMAAAAAAAAAAAAuwAEBAAAQAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-28T03:12:36.204Z"}, {"$type": "Document", "DocumentIndex": 8, "Title": "UIExtractorViewModel.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-uitext-extractor\\src\\ViewModels\\UIExtractorViewModel.cs", "RelativeDocumentMoniker": "..\\..\\localization-uitext-extractor\\src\\ViewModels\\UIExtractorViewModel.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-uitext-extractor\\src\\ViewModels\\UIExtractorViewModel.cs", "RelativeToolTip": "..\\..\\localization-uitext-extractor\\src\\ViewModels\\UIExtractorViewModel.cs", "ViewState": "AgIAAHYAAAAAAAAAAAAmwJQAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-27T12:10:16.706Z"}, {"$type": "Document", "DocumentIndex": 10, "Title": "FilterViewModel.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-uitext-extractor\\src\\ViewModels\\FilterViewModel.cs", "RelativeDocumentMoniker": "..\\..\\localization-uitext-extractor\\src\\ViewModels\\FilterViewModel.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-uitext-extractor\\src\\ViewModels\\FilterViewModel.cs", "RelativeToolTip": "..\\..\\localization-uitext-extractor\\src\\ViewModels\\FilterViewModel.cs", "ViewState": "AgIAAK8AAAAAAAAAAAAcwMEAAAAUAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-27T11:36:25.189Z"}]}]}]}