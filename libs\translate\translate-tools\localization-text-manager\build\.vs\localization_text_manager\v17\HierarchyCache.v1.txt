﻿++Solution 'localization_text_manager' ‎ (5 of 5 projects)
i:{00000000-0000-0000-0000-000000000000}:localization_text_manager.sln
++ALL_BUILD
i:{00000000-0000-0000-0000-000000000000}:ALL_BUILD
++References
i:{0687d9ca-0398-3513-9268-7d1051306bfe}:{07553F5A-B84C-4654-877A-6D2AD2282E8D}
i:{973f07e1-9ca2-3229-98f3-3f350db36a82}:{07553F5A-B84C-4654-877A-6D2AD2282E8D}
++External Dependencies
i:{0687d9ca-0398-3513-9268-7d1051306bfe}:{5D83A384-FDA3-476F-841E-2B60A6988FC6}
i:{973f07e1-9ca2-3229-98f3-3f350db36a82}:{5D83A384-FDA3-476F-841E-2B60A6988FC6}
++CMakeLists.txt
i:{0687d9ca-0398-3513-9268-7d1051306bfe}:D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-text-manager\CMakeLists.txt
i:{e947f683-9112-3425-b4ce-89f60c32f003}:d:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-uitext-extractor\cmakelists.txt
i:{8702da1b-6bf0-3ae3-af59-95da8c38b0ff}:d:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-text-manager\cmakelists.txt
i:{5ee3347a-9326-37e9-adbc-a22721cacb82}:d:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\cmakelists.txt
++localization_text_manager
e:{0687d9ca-0398-3513-9268-7d1051306bfe}:{8702DA1B-6BF0-3AE3-AF59-95DA8C38B0FF}
i:{00000000-0000-0000-0000-000000000000}:localization_text_manager
++translate_csharp
e:{0687d9ca-0398-3513-9268-7d1051306bfe}:{5EE3347A-9326-37E9-ADBC-A22721CACB82}
i:{8702da1b-6bf0-3ae3-af59-95da8c38b0ff}:>877
i:{00000000-0000-0000-0000-000000000000}:translate_csharp
++ui_extractor_tool
e:{0687d9ca-0398-3513-9268-7d1051306bfe}:{E947F683-9112-3425-B4CE-89F60C32F003}
i:{00000000-0000-0000-0000-000000000000}:ui_extractor_tool
i:{8702da1b-6bf0-3ae3-af59-95da8c38b0ff}:>881
++ZERO_CHECK
e:{0687d9ca-0398-3513-9268-7d1051306bfe}:{973F07E1-9CA2-3229-98F3-3F350DB36A82}
i:{00000000-0000-0000-0000-000000000000}:ZERO_CHECK
i:{e947f683-9112-3425-b4ce-89f60c32f003}:>876
i:{8702da1b-6bf0-3ae3-af59-95da8c38b0ff}:>879
i:{5ee3347a-9326-37e9-adbc-a22721cacb82}:>861
++CMake Rules
i:{973f07e1-9ca2-3229-98f3-3f350db36a82}:CMake Rules
++generate.stamp.rule
i:{973f07e1-9ca2-3229-98f3-3f350db36a82}:D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-text-manager\build\CMakeFiles\9d7ae70f1b0822e0659f38f6fe3001ea\generate.stamp.rule
++Dependencies
i:{e947f683-9112-3425-b4ce-89f60c32f003}:>858
i:{8702da1b-6bf0-3ae3-af59-95da8c38b0ff}:>859
i:{5ee3347a-9326-37e9-adbc-a22721cacb82}:>857
++src
i:{e947f683-9112-3425-b4ce-89f60c32f003}:d:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-text-manager\build\ui_extractor_tool\src\
i:{8702da1b-6bf0-3ae3-af59-95da8c38b0ff}:d:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-text-manager\build\src\
i:{5ee3347a-9326-37e9-adbc-a22721cacb82}:d:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-text-manager\build\translate_csharp\src\
++Classes
i:{e947f683-9112-3425-b4ce-89f60c32f003}:d:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-text-manager\build\ui_extractor_tool\src\classes\
++Extractor
i:{e947f683-9112-3425-b4ce-89f60c32f003}:d:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-text-manager\build\ui_extractor_tool\src\extractor\
++IUITextExtractor.cs
i:{e947f683-9112-3425-b4ce-89f60c32f003}:d:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-uitext-extractor\src\extractor\iuitextextractor.cs
++TextManager.cs
i:{e947f683-9112-3425-b4ce-89f60c32f003}:d:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-uitext-extractor\src\extractor\textmanager.cs
++UIExtractor.cs
i:{e947f683-9112-3425-b4ce-89f60c32f003}:d:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-uitext-extractor\src\extractor\uiextractor.cs
++UIExtractorWindow.xaml
i:{e947f683-9112-3425-b4ce-89f60c32f003}:d:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-uitext-extractor\src\extractor\uiextractorwindow.xaml
++UIExtractorWindow.xaml.cs
i:{e947f683-9112-3425-b4ce-89f60c32f003}:d:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-uitext-extractor\src\extractor\uiextractorwindow.xaml.cs
++WpfUITextExtractor.cs
i:{e947f683-9112-3425-b4ce-89f60c32f003}:d:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-uitext-extractor\src\extractor\wpfuitextextractor.cs
++XmlTextExporter.cs
i:{e947f683-9112-3425-b4ce-89f60c32f003}:d:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-uitext-extractor\src\extractor\xmltextexporter.cs
++SourceMapping
i:{e947f683-9112-3425-b4ce-89f60c32f003}:d:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-text-manager\build\ui_extractor_tool\src\sourcemapping\
++Style
i:{e947f683-9112-3425-b4ce-89f60c32f003}:d:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-text-manager\build\ui_extractor_tool\src\style\
++Utils
i:{e947f683-9112-3425-b4ce-89f60c32f003}:d:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-text-manager\build\ui_extractor_tool\src\utils\
++ViewModels
i:{e947f683-9112-3425-b4ce-89f60c32f003}:d:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-text-manager\build\ui_extractor_tool\src\viewmodels\
++BaseViewModel.cs
i:{e947f683-9112-3425-b4ce-89f60c32f003}:d:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-uitext-extractor\src\viewmodels\baseviewmodel.cs
++FileOperationsViewModel.cs
i:{e947f683-9112-3425-b4ce-89f60c32f003}:d:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-uitext-extractor\src\viewmodels\fileoperationsviewmodel.cs
++FilterViewModel.cs
i:{e947f683-9112-3425-b4ce-89f60c32f003}:d:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-uitext-extractor\src\viewmodels\filterviewmodel.cs
++ProgressViewModel.cs
i:{e947f683-9112-3425-b4ce-89f60c32f003}:d:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-uitext-extractor\src\viewmodels\progressviewmodel.cs
++SourceMappingWindowViewModel.cs
i:{e947f683-9112-3425-b4ce-89f60c32f003}:d:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-uitext-extractor\src\viewmodels\sourcemappingwindowviewmodel.cs
++StatusViewModel.cs
i:{e947f683-9112-3425-b4ce-89f60c32f003}:d:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-uitext-extractor\src\viewmodels\statusviewmodel.cs
++UIExtractorViewModel.cs
i:{e947f683-9112-3425-b4ce-89f60c32f003}:d:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-uitext-extractor\src\viewmodels\uiextractorviewmodel.cs
++UITextInfoWindowViewModel.cs
i:{e947f683-9112-3425-b4ce-89f60c32f003}:d:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-uitext-extractor\src\viewmodels\uitextinfowindowviewmodel.cs
++Views
i:{e947f683-9112-3425-b4ce-89f60c32f003}:d:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-text-manager\build\ui_extractor_tool\src\views\
++BulkActions.xaml
i:{e947f683-9112-3425-b4ce-89f60c32f003}:d:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-uitext-extractor\src\views\bulkactions.xaml
++FileSelectionPanel.xaml
i:{e947f683-9112-3425-b4ce-89f60c32f003}:d:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-uitext-extractor\src\views\fileselectionpanel.xaml
++FilterPanel.xaml
i:{e947f683-9112-3425-b4ce-89f60c32f003}:d:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-uitext-extractor\src\views\filterpanel.xaml
++SourceMappingWindow.xaml
i:{e947f683-9112-3425-b4ce-89f60c32f003}:d:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-uitext-extractor\src\views\sourcemappingwindow.xaml
++TextGrid.xaml
i:{e947f683-9112-3425-b4ce-89f60c32f003}:d:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-uitext-extractor\src\views\textgrid.xaml
++UITextInfoWindow.xaml
i:{e947f683-9112-3425-b4ce-89f60c32f003}:d:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-uitext-extractor\src\views\uitextinfowindow.xaml
++LLMConfigPathResolver.cs
i:{e947f683-9112-3425-b4ce-89f60c32f003}:d:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\src\utils\llmconfigpathresolver.cs
++LocalizationUtils.cs
i:{e947f683-9112-3425-b4ce-89f60c32f003}:d:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\src\utils\localizationutils.cs
++PathUtils.cs
i:{e947f683-9112-3425-b4ce-89f60c32f003}:d:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\src\utils\pathutils.cs
++ProjectTextExtractorConfig.cs
i:{e947f683-9112-3425-b4ce-89f60c32f003}:d:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\src\common\projecttextextractorconfig.cs
++ProjectTextExtractorConfigManager.cs
i:{e947f683-9112-3425-b4ce-89f60c32f003}:d:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-lib\src\common\projecttextextractorconfigmanager.cs
++Analyzers
i:{e947f683-9112-3425-b4ce-89f60c32f003}:>878
i:{8702da1b-6bf0-3ae3-af59-95da8c38b0ff}:>882
i:{5ee3347a-9326-37e9-adbc-a22721cacb82}:>862
++Assemblies
i:{e947f683-9112-3425-b4ce-89f60c32f003}:>891
++Packages
i:{e947f683-9112-3425-b4ce-89f60c32f003}:>885
i:{8702da1b-6bf0-3ae3-af59-95da8c38b0ff}:>912
++Projects
i:{e947f683-9112-3425-b4ce-89f60c32f003}:>874
i:{8702da1b-6bf0-3ae3-af59-95da8c38b0ff}:>875
i:{5ee3347a-9326-37e9-adbc-a22721cacb82}:>860
++SourceInfo.cs
i:{e947f683-9112-3425-b4ce-89f60c32f003}:d:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-uitext-extractor\src\classes\sourceinfo.cs
++UIExtractedTextInfo.cs
i:{e947f683-9112-3425-b4ce-89f60c32f003}:d:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-uitext-extractor\src\classes\uiextractedtextinfo.cs
++IMatchingStrategy.cs
i:{e947f683-9112-3425-b4ce-89f60c32f003}:d:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-uitext-extractor\src\sourcemapping\imatchingstrategy.cs
++MatchingStrategies.cs
i:{e947f683-9112-3425-b4ce-89f60c32f003}:d:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-uitext-extractor\src\sourcemapping\matchingstrategies.cs
++UIElementSourceMapper.cs
i:{e947f683-9112-3425-b4ce-89f60c32f003}:d:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-uitext-extractor\src\sourcemapping\uielementsourcemapper.cs
++UIExtractorStyles.xaml
i:{e947f683-9112-3425-b4ce-89f60c32f003}:d:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-uitext-extractor\src\style\uiextractorstyles.xaml
++TextInfoExtensions.cs
i:{e947f683-9112-3425-b4ce-89f60c32f003}:d:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-uitext-extractor\src\utils\textinfoextensions.cs
++TextMatchingUtils.cs
i:{e947f683-9112-3425-b4ce-89f60c32f003}:d:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-uitext-extractor\src\utils\textmatchingutils.cs
++UIExtractorConfig.cs
i:{e947f683-9112-3425-b4ce-89f60c32f003}:d:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-uitext-extractor\src\utils\uiextractorconfig.cs
++UIExtractorConverters.cs
i:{e947f683-9112-3425-b4ce-89f60c32f003}:d:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-uitext-extractor\src\utils\uiextractorconverters.cs
++UiUtils.cs
i:{e947f683-9112-3425-b4ce-89f60c32f003}:d:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-uitext-extractor\src\utils\uiutils.cs
++BulkActions.xaml.cs
i:{e947f683-9112-3425-b4ce-89f60c32f003}:d:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-uitext-extractor\src\views\bulkactions.xaml.cs
++FileSelectionPanel.xaml.cs
i:{e947f683-9112-3425-b4ce-89f60c32f003}:d:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-uitext-extractor\src\views\fileselectionpanel.xaml.cs
++FilterPanel.xaml.cs
i:{e947f683-9112-3425-b4ce-89f60c32f003}:d:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-uitext-extractor\src\views\filterpanel.xaml.cs
++SourceMappingWindow.xaml.cs
i:{e947f683-9112-3425-b4ce-89f60c32f003}:d:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-uitext-extractor\src\views\sourcemappingwindow.xaml.cs
++TextGrid.xaml.cs
i:{e947f683-9112-3425-b4ce-89f60c32f003}:d:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-uitext-extractor\src\views\textgrid.xaml.cs
++UITextInfoWindow.xaml.cs
i:{e947f683-9112-3425-b4ce-89f60c32f003}:d:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-uitext-extractor\src\views\uitextinfowindow.xaml.cs
++CommunityToolkit.Mvvm.CodeFixers
i:{e947f683-9112-3425-b4ce-89f60c32f003}:c:\users\<USER>\.nuget\packages\communitytoolkit.mvvm\8.2.2\analyzers\dotnet\roslyn4.3\cs\communitytoolkit.mvvm.codefixers.dll
i:{8702da1b-6bf0-3ae3-af59-95da8c38b0ff}:c:\users\<USER>\.nuget\packages\communitytoolkit.mvvm\8.2.2\analyzers\dotnet\roslyn4.3\cs\communitytoolkit.mvvm.codefixers.dll
++CommunityToolkit.Mvvm.SourceGenerators
i:{e947f683-9112-3425-b4ce-89f60c32f003}:c:\users\<USER>\.nuget\packages\communitytoolkit.mvvm\8.2.2\analyzers\dotnet\roslyn4.3\cs\communitytoolkit.mvvm.sourcegenerators.dll
i:{8702da1b-6bf0-3ae3-af59-95da8c38b0ff}:c:\users\<USER>\.nuget\packages\communitytoolkit.mvvm\8.2.2\analyzers\dotnet\roslyn4.3\cs\communitytoolkit.mvvm.sourcegenerators.dll
++PresentationCore
i:{e947f683-9112-3425-b4ce-89f60c32f003}:>919
++PresentationFramework
i:{e947f683-9112-3425-b4ce-89f60c32f003}:>921
++System
i:{e947f683-9112-3425-b4ce-89f60c32f003}:>916
++System.Core
i:{e947f683-9112-3425-b4ce-89f60c32f003}:>915
++System.Data
i:{e947f683-9112-3425-b4ce-89f60c32f003}:>904
++System.Drawing
i:{e947f683-9112-3425-b4ce-89f60c32f003}:>892
++System.IO.Compression.FileSystem
i:{e947f683-9112-3425-b4ce-89f60c32f003}:>918
++System.Numerics
i:{e947f683-9112-3425-b4ce-89f60c32f003}:>897
++System.Runtime.Serialization
i:{e947f683-9112-3425-b4ce-89f60c32f003}:>908
++System.Windows.Controls.Ribbon
i:{e947f683-9112-3425-b4ce-89f60c32f003}:>920
++System.Windows.Forms
i:{e947f683-9112-3425-b4ce-89f60c32f003}:>895
++System.Xaml
i:{e947f683-9112-3425-b4ce-89f60c32f003}:>900
++System.Xml
i:{e947f683-9112-3425-b4ce-89f60c32f003}:>914
++System.Xml.Linq
i:{e947f683-9112-3425-b4ce-89f60c32f003}:>894
++UIAutomationClient
i:{e947f683-9112-3425-b4ce-89f60c32f003}:>902
++UIAutomationClientsideProviders
i:{e947f683-9112-3425-b4ce-89f60c32f003}:>911
++UIAutomationProvider
i:{e947f683-9112-3425-b4ce-89f60c32f003}:>898
++UIAutomationTypes
i:{e947f683-9112-3425-b4ce-89f60c32f003}:>906
++WindowsBase
i:{e947f683-9112-3425-b4ce-89f60c32f003}:>917
++CommunityToolkit.Mvvm (8.2.2)
i:{e947f683-9112-3425-b4ce-89f60c32f003}:>887
i:{8702da1b-6bf0-3ae3-af59-95da8c38b0ff}:>913
++Newtonsoft.Json (13.0.3)
i:{e947f683-9112-3425-b4ce-89f60c32f003}:>890
++Frameworks
i:{8702da1b-6bf0-3ae3-af59-95da8c38b0ff}:>907
i:{5ee3347a-9326-37e9-adbc-a22721cacb82}:>871
++App.xaml
i:{8702da1b-6bf0-3ae3-af59-95da8c38b0ff}:d:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-text-manager\src\app.xaml
++Microsoft.CodeAnalysis.CSharp.NetAnalyzers
i:{8702da1b-6bf0-3ae3-af59-95da8c38b0ff}:c:\program files\dotnet\sdk\8.0.400\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.csharp.netanalyzers.dll
i:{5ee3347a-9326-37e9-adbc-a22721cacb82}:c:\program files\dotnet\sdk\8.0.400\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.csharp.netanalyzers.dll
++Microsoft.CodeAnalysis.NetAnalyzers
i:{8702da1b-6bf0-3ae3-af59-95da8c38b0ff}:c:\program files\dotnet\sdk\8.0.400\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.netanalyzers.dll
i:{5ee3347a-9326-37e9-adbc-a22721cacb82}:c:\program files\dotnet\sdk\8.0.400\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.netanalyzers.dll
++Microsoft.Interop.ComInterfaceGenerator
i:{8702da1b-6bf0-3ae3-af59-95da8c38b0ff}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.8\analyzers\dotnet\cs\microsoft.interop.cominterfacegenerator.dll
i:{5ee3347a-9326-37e9-adbc-a22721cacb82}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.8\analyzers\dotnet\cs\microsoft.interop.cominterfacegenerator.dll
++Microsoft.Interop.JavaScript.JSImportGenerator
i:{8702da1b-6bf0-3ae3-af59-95da8c38b0ff}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.8\analyzers\dotnet\cs\microsoft.interop.javascript.jsimportgenerator.dll
i:{5ee3347a-9326-37e9-adbc-a22721cacb82}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.8\analyzers\dotnet\cs\microsoft.interop.javascript.jsimportgenerator.dll
++Microsoft.Interop.LibraryImportGenerator
i:{8702da1b-6bf0-3ae3-af59-95da8c38b0ff}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.8\analyzers\dotnet\cs\microsoft.interop.libraryimportgenerator.dll
i:{5ee3347a-9326-37e9-adbc-a22721cacb82}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.8\analyzers\dotnet\cs\microsoft.interop.libraryimportgenerator.dll
++Microsoft.Interop.SourceGeneration
i:{8702da1b-6bf0-3ae3-af59-95da8c38b0ff}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.8\analyzers\dotnet\cs\microsoft.interop.sourcegeneration.dll
i:{5ee3347a-9326-37e9-adbc-a22721cacb82}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.8\analyzers\dotnet\cs\microsoft.interop.sourcegeneration.dll
++System.Text.Json.SourceGeneration
i:{8702da1b-6bf0-3ae3-af59-95da8c38b0ff}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.8\analyzers\dotnet\cs\system.text.json.sourcegeneration.dll
i:{5ee3347a-9326-37e9-adbc-a22721cacb82}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.8\analyzers\dotnet\cs\system.text.json.sourcegeneration.dll
++System.Text.RegularExpressions.Generator
i:{8702da1b-6bf0-3ae3-af59-95da8c38b0ff}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.8\analyzers\dotnet\cs\system.text.regularexpressions.generator.dll
i:{5ee3347a-9326-37e9-adbc-a22721cacb82}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.8\analyzers\dotnet\cs\system.text.regularexpressions.generator.dll
++Microsoft.NETCore.App
i:{8702da1b-6bf0-3ae3-af59-95da8c38b0ff}:>910
i:{5ee3347a-9326-37e9-adbc-a22721cacb82}:>873
++Microsoft.WindowsDesktop.App.WPF
i:{8702da1b-6bf0-3ae3-af59-95da8c38b0ff}:>909
i:{5ee3347a-9326-37e9-adbc-a22721cacb82}:>872
++App.xaml.cs
i:{8702da1b-6bf0-3ae3-af59-95da8c38b0ff}:d:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-text-manager\src\app.xaml.cs
++Attributes
i:{5ee3347a-9326-37e9-adbc-a22721cacb82}:d:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-text-manager\build\translate_csharp\src\attributes\
++Converters
i:{5ee3347a-9326-37e9-adbc-a22721cacb82}:d:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-text-manager\build\translate_csharp\src\converters\
++Helper
i:{5ee3347a-9326-37e9-adbc-a22721cacb82}:d:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-text-manager\build\translate_csharp\src\helper\
++Models
i:{5ee3347a-9326-37e9-adbc-a22721cacb82}:d:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-text-manager\build\translate_csharp\src\models\
++Translator.cs
i:{5ee3347a-9326-37e9-adbc-a22721cacb82}:d:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\src\translator.cs
++LocalizedDescriptionAttribute.cs
i:{5ee3347a-9326-37e9-adbc-a22721cacb82}:d:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\src\attributes\localizeddescriptionattribute.cs
++TranslateConverter.cs
i:{5ee3347a-9326-37e9-adbc-a22721cacb82}:d:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\src\converters\translateconverter.cs
++LanguageHelper.cs
i:{5ee3347a-9326-37e9-adbc-a22721cacb82}:d:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\src\helper\languagehelper.cs
++Serializer.cs
i:{5ee3347a-9326-37e9-adbc-a22721cacb82}:d:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\src\helper\serializer.cs
++TranslateMetadata.cs
i:{5ee3347a-9326-37e9-adbc-a22721cacb82}:d:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\src\helper\translatemetadata.cs
++TranslateUtils.cs
i:{5ee3347a-9326-37e9-adbc-a22721cacb82}:d:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\src\helper\translateutils.cs
++LanguageModel.cs
i:{5ee3347a-9326-37e9-adbc-a22721cacb82}:d:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\src\models\languagemodel.cs
++LanguageTranslation.cs
i:{5ee3347a-9326-37e9-adbc-a22721cacb82}:d:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\src\models\languagetranslation.cs
++LocalizedString.cs
i:{5ee3347a-9326-37e9-adbc-a22721cacb82}:d:\kibisoft\workarea\git\interop\libs\translate\translate-csharp\src\models\localizedstring.cs
++ExcelExporter.cs
i:{e947f683-9112-3425-b4ce-89f60c32f003}:d:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-uitext-extractor\src\utils\excelexporter.cs
++System.Web
i:{e947f683-9112-3425-b4ce-89f60c32f003}:>1373
