# Known Issues and Solutions

This document describes special cases where the standard translation approach might not work and provides solutions for each case.

## 1. Enum Description Attributes

### Issue
Description attributes in enums cannot be directly translated using standard translation methods.

```csharp
public enum Measure
{
    [Description("Calibrate")]
    Calibrate,
    [Description("New Image Guided Measurement")]
    Link
}
```

### Solution
Replace `Description` attributes with `LocalizedDescription` attributes that support translation:

```csharp
public enum Measure
{
    [LocalizedDescription("56523235", "Calibrate")]
    Calibrate,
    [LocalizedDescription("3434343434", "New Image Guided Measurement")]
    Link
}
```

## 2. Run.Text Property in XAML

### Issue
The `Run.Text` property in XAML doesn't support two-way bindings by default.

```xaml
<TextBlock>        
    <Run Text="Standby in">
</TextBlock>
```

### Solution
Use Mode=OneWay

```xaml
<TextBlock>
    <Run Text="{Binding Converter={StaticResource TranslateConverter}, ConverterParameter='id:-1983694322|en:Standby in', Mode=OneWay}/>
</TextBlock>
```

## 3. Non-Dependency Properties

### Issue
Binding can only be set on a DependencyProperty of a DependencyObject. Some properties in XAML are not dependency properties and cannot be directly bound.

### Solution
For non-dependency properties, we need to move the text to code-behind. There are two main cases:

#### A. Root Element Properties (Window, UserControl)
```xaml
<!-- Before -->
<Window Title="Main Window">
    <Button Content="Click Me" />
</Window>

<!-- After -->
<Window>
    <Button Content="Click Me" />
</Window>
```

```csharp
// Code-behind
public MainWindow()
{
    InitializeComponent();
    Title = "Main Window";
}
```

#### B. Custom Control Properties
```xaml
<!-- Before -->
<CustomControl Title="Custom Title" Header="Custom Header">
    <Button Content="Click Me" />
</CustomControl>

<!-- After -->
<CustomControl x:Name="myCustomControl">
    <Button Content="Click Me" />
</CustomControl>
```

```csharp
// Code-behind
public MainWindow()
{
    InitializeComponent();
    myCustomControl.Title = "Custom Title";
    myCustomControl.Header = "Custom Header";
}
```

### Automated Solution
The extractor tool automatically handles these cases when `--convertXamlTextToCodeBehind` is enabled. It will:
- Identify non-dependency properties
- Move them to code-behind
- For root elements, add assignments directly in the constructor
- For other elements, add assignments after InitializeComponent()
