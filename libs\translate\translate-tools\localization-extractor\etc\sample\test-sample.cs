using System;
using System.Windows;

namespace TestApp
{
    public class MainWindow : Window
    {
        public MainWindow()
        {
            // User-facing strings that should be translated
            this.Title = "My Application";
            
            var welcomeMessage = "Welcome to our application!";
            var errorMessage = "An error occurred while processing your request.";
            
            // Technical strings that should NOT be translated
            var logMessage = "DEBUG: Application started";
            var configKey = "app.settings.theme";
            
            Console.WriteLine("Hello World!");
            MessageBox.Show("Please enter your name");
        }
        
        private void ShowError()
        {
            MessageBox.Show("File not found", "Error", MessageBoxButton.OK);
        }
    }
}
