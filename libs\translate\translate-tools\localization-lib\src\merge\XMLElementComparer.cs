/*
 * Copyright 2021-present Alcon. All rights reserved.
 * Alcon proprietary/confidential.
 * Use is subject to license terms.
 */

using System.Collections.Generic;
using System.Linq;
using System.Xml.Linq;

namespace LocalizationLib;
class XElementComparer : IEqualityComparer<XElement>
{
    /// <summary>
    /// Two elements are equal if their attributes are equal
    /// </summary>
    /// <param name="x"></param>
    /// <param name="y"></param>
    /// <returns></returns>
    public bool Equals(XElement x, XElement y)
    {
        if (x == null || y == null) return false;

        if (x.Name != y.Name) return false;

        var xAttrs = new HashSet<string>(x.Attributes().Select(a => $"{a.Name}={a.Value}"));
        var yAttrs = new HashSet<string>(y.Attributes().Select(a => $"{a.Name}={a.Value}"));

        return xAttrs.SetEquals(yAttrs);
    }

    public int GetHashCode(XElement obj)
    {
        if (obj == null) return 0;

        int hash = obj.Name.GetHashCode();
        foreach (var attr in obj.Attributes())
        {
            hash ^= attr.Name.GetHashCode() ^ attr.Value.GetHashCode();
        }
        hash ^= obj.Value.GetHashCode();

        return hash;
    }
}