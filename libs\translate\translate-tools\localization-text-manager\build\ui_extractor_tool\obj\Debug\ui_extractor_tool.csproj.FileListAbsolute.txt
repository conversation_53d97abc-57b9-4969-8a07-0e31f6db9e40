D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-text-manager\build\ui_extractor_tool\Debug\ui_extractor_tool.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-text-manager\build\ui_extractor_tool\Debug\ui_extractor_tool.pdb
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-text-manager\build\ui_extractor_tool\Debug\CommunityToolkit.Mvvm.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-text-manager\build\ui_extractor_tool\Debug\Microsoft.Bcl.AsyncInterfaces.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-text-manager\build\ui_extractor_tool\Debug\Newtonsoft.Json.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-text-manager\build\ui_extractor_tool\Debug\System.Buffers.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-text-manager\build\ui_extractor_tool\Debug\System.ComponentModel.Annotations.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-text-manager\build\ui_extractor_tool\Debug\System.Memory.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-text-manager\build\ui_extractor_tool\Debug\System.Numerics.Vectors.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-text-manager\build\ui_extractor_tool\Debug\System.Runtime.CompilerServices.Unsafe.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-text-manager\build\ui_extractor_tool\Debug\System.Threading.Tasks.Extensions.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-text-manager\build\ui_extractor_tool\obj\Debug\ui_extractor_tool.csproj.AssemblyReference.cache
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-text-manager\build\ui_extractor_tool\obj\Debug\UIExtractorWindow.g.cs
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-text-manager\build\ui_extractor_tool\obj\Debug\BulkActions.g.cs
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-text-manager\build\ui_extractor_tool\obj\Debug\FileSelectionPanel.g.cs
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-text-manager\build\ui_extractor_tool\obj\Debug\FilterPanel.g.cs
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-text-manager\build\ui_extractor_tool\obj\Debug\SourceMappingWindow.g.cs
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-text-manager\build\ui_extractor_tool\obj\Debug\TextGrid.g.cs
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-text-manager\build\ui_extractor_tool\obj\Debug\UITextInfoWindow.g.cs
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-text-manager\build\ui_extractor_tool\obj\Debug\GeneratedInternalTypeHelper.g.cs
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-text-manager\build\ui_extractor_tool\obj\Debug\ui_extractor_tool_MarkupCompile.cache
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-text-manager\build\ui_extractor_tool\obj\Debug\ui_extractor_tool_MarkupCompile.lref
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-text-manager\build\ui_extractor_tool\obj\Debug\UIExtractorWindow.baml
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-text-manager\build\ui_extractor_tool\obj\Debug\UIExtractorStyles.baml
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-text-manager\build\ui_extractor_tool\obj\Debug\BulkActions.baml
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-text-manager\build\ui_extractor_tool\obj\Debug\FileSelectionPanel.baml
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-text-manager\build\ui_extractor_tool\obj\Debug\FilterPanel.baml
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-text-manager\build\ui_extractor_tool\obj\Debug\SourceMappingWindow.baml
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-text-manager\build\ui_extractor_tool\obj\Debug\TextGrid.baml
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-text-manager\build\ui_extractor_tool\obj\Debug\UITextInfoWindow.baml
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-text-manager\build\ui_extractor_tool\obj\Debug\ui_extractor_tool.g.resources
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-text-manager\build\ui_extractor_tool\obj\Debug\ui_extractor_tool.GeneratedMSBuildEditorConfig.editorconfig
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-text-manager\build\ui_extractor_tool\obj\Debug\ui_extractor_tool.AssemblyInfoInputs.cache
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-text-manager\build\ui_extractor_tool\obj\Debug\ui_extractor_tool.AssemblyInfo.cs
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-text-manager\build\ui_extractor_tool\obj\Debug\ui_extractor_tool.csproj.CoreCompileInputs.cache
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-text-manager\build\ui_extractor_tool\obj\Debug\ui_extractor_tool.sourcelink.json
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-text-manager\build\ui_extractor_tool\obj\Debug\ui_extra.429F059A.Up2Date
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-text-manager\build\ui_extractor_tool\obj\Debug\ui_extractor_tool.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-text-manager\build\ui_extractor_tool\obj\Debug\ui_extractor_tool.pdb
