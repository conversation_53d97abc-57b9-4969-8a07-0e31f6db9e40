/*
 * Copyright 2021-present Alcon. All rights reserved.
 * Alcon proprietary/confidential.
 * Use is subject to license terms.
 */

using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;

namespace LocalizationLib;

/// <summary>
/// Local LLM client for OpenAI-compatible APIs (LMStudio, Ollama, etc.)
///
/// This implementation provides integration with local LLM services using OpenAI-compatible REST APIs
/// with JSON schema enforcement for structured output and proper error handling.
///
/// Features:
/// - OpenAI-compatible API support (LMStudio, Ollama, etc.)
/// - JSON schema enforcement for structured responses
/// - Proper error handling with exception throwing
/// - Retry logic with exponential backoff
/// - Resource management with IDisposable pattern
///
/// Usage:
/// Configure via LLMConfig with localLlmProviderConfig section containing serviceUrl, modelId,
/// and other parameters. The client will enforce JSON schemas for consistent output.
/// </summary>
public class LocalLlmClient : BaseLlmClient
{
    private readonly bool _stream;

    public LocalLlmClient(LLMConfig cfg) : base(cfg)
    {
        if (_cfg.localLlmProviderConfig == null)
        {
            throw new InvalidOperationException("No localLlmProviderConfig found in LLMConfig.");
        }

        if (string.IsNullOrEmpty(_cfg.localLlmProviderConfig.serviceUrl) ||
            string.IsNullOrEmpty(_cfg.localLlmProviderConfig.modelId))
        {
            throw new InvalidOperationException("serviceUrl or modelId is missing in localLlmProviderConfig.");
        }

        // Decide if streaming is needed. For now, let's say false
        _stream = false;
    }

    #region BaseLlmClient Implementation

    protected override HttpClient CreateHttpClient()
    {
        return new HttpClient
        {
            Timeout = TimeSpan.FromSeconds(_cfg.localLlmProviderConfig.timeoutSeconds)
        };
    }

    protected override async Task<string> CallLlmForDiscovery(string systemPrompt, string userPrompt)
    {
        var request = CreateDiscoveryRequest(systemPrompt, userPrompt);
        return await CallLocalLlm(request);
    }

    protected override async Task<string> CallLlmForVerification(string systemPrompt, string userPrompt)
    {
        var request = CreateVerificationRequest(systemPrompt, userPrompt);
        return await CallLocalLlm(request);
    }

    protected override List<ReplacementRecord> ParseDiscoveryResponse(string rawResponse, string groupId, string filePath)
    {
        return ExtractRecordsFromResponse(rawResponse, groupId, filePath);
    }

    protected override TranslationVerificationResponse ParseVerificationResponse(string rawResponse)
    {
        var completion = JsonSerializer.Deserialize<ChatCompletionResponse>(rawResponse);
        if (completion?.choices == null || completion.choices.Count == 0)
            throw new InvalidOperationException("Failed to get response from LLM");

        string content = completion.choices[0].message?.content;
        if (string.IsNullOrWhiteSpace(content))
            throw new InvalidOperationException("Empty response from LLM");

        var verificationResponse = JsonSerializer.Deserialize<TranslationVerificationResponse>(content);
        if (verificationResponse == null)
            throw new InvalidOperationException("Failed to parse LLM response");

        return verificationResponse;
    }

    protected override string GetDiscoverySystemPrompt()
    {
        return LlmPrompts.CsDiscoverySystemPrompt;
    }

    protected override string GetCsVerificationSystemPrompt()
    {
        return LlmPrompts.CsVerificationSystemPrompt;
    }

    protected override string GetXamlVerificationSystemPrompt()
    {
        return LlmPrompts.XamlVerificationSystemPrompt;
    }

    protected override string GetCppVerificationSystemPrompt()
    {
        return LlmPrompts.CppVerificationSystemPrompt;
    }

    #endregion

    #region Private Helper Methods

    private ChatCompletionRequest CreateDiscoveryRequest(string systemPrompt, string userPrompt)
    {
        var systemMessage = new ChatMessage
        {
            role = "system",
            content = systemPrompt
        };

        var userMessage = new ChatMessage
        {
            role = "user",
            content = userPrompt
        };

        return new ChatCompletionRequest
        {
            model = _cfg.localLlmProviderConfig.modelId,
            messages = new List<ChatMessage> { systemMessage, userMessage },
            temperature = _cfg.localLlmProviderConfig.temperature,
            max_tokens = -1,
            stream = _stream,
            response_format = CreateDiscoveryResponseFormat()
        };
    }

    private ChatCompletionRequest CreateVerificationRequest(string systemPrompt, string userPrompt)
    {
        var systemMessage = new ChatMessage
        {
            role = "system",
            content = systemPrompt
        };

        var userMessage = new ChatMessage
        {
            role = "user",
            content = userPrompt
        };

        return new ChatCompletionRequest
        {
            model = _cfg.localLlmProviderConfig.modelId,
            messages = new List<ChatMessage> { systemMessage, userMessage },
            temperature = _cfg.localLlmProviderConfig.temperature,
            max_tokens = -1,
            stream = _stream,
            response_format = CreateVerificationResponseFormat()
        };
    }

    private ResponseFormat CreateDiscoveryResponseFormat()
    {
        return new ResponseFormat
        {
            type = "json_schema",
            json_schema = new JsonSchemaWrapper
            {
                name = "localization_records",
                strict = "true",
                schema = new JsonSchema
                {
                    type = "object",
                    properties = new LocalizationSchemaProperties
                    {
                        records = new JsonSchemaArray
                        {
                            type = "array",
                            items = new JsonSchemaItems
                            {
                                type = "object",
                                properties = new LocalizationRecordSchemaProperties
                                {
                                    lineNumber = new JsonSchemaProperty { type = "integer", description = "The line index from the snippet" },
                                    column = new JsonSchemaProperty { type = "integer", description = "The exact column index in that line where the string literal starts" },
                                    originalValue = new JsonSchemaProperty { type = "string", description = "The user-facing string literal discovered" },
                                    reason = new JsonSchemaProperty { type = "string", description = "A brief explanation of why it is user-facing text" },
                                    lineText = new JsonSchemaProperty { type = "string", description = "The entire line from the snippet containing that string" }
                                },
                                required = new[] { "lineNumber", "column", "originalValue", "reason", "lineText" }
                            }
                        }
                    },
                    required = new[] { "records" }
                }
            }
        };
    }

    private ResponseFormat CreateVerificationResponseFormat()
    {
        return new ResponseFormat
        {
            type = "json_schema",
            json_schema = new JsonSchemaWrapper
            {
                name = "translation_verification",
                strict = "true",
                schema = new JsonSchema
                {
                    type = "object",
                    properties = new TranslationVerificationSchemaProperties
                    {
                        needsTranslation = new JsonSchemaProperty
                        {
                            type = "boolean",
                            description = "Whether the text needs to be translated"
                        },
                        reason = new JsonSchemaProperty
                        {
                            type = "string",
                            description = "Detailed explanation of why the text needs or doesn't need translation"
                        },
                        context = new JsonSchemaProperty
                        {
                            type = "string",
                            description = "Additional context about where and how the text is used"
                        }
                    },
                    required = new[] { "needsTranslation", "reason", "context" }
                }
            }
        };
    }



    private async Task<string> CallLocalLlm(ChatCompletionRequest requestBody)
    {
        string endpoint = $"{_cfg.localLlmProviderConfig.serviceUrl}/v1/chat/completions";
        string json = JsonSerializer.Serialize(requestBody);
        var content = new StringContent(json, Encoding.UTF8, "application/json");

        int attempt = 0;
        while (true)
        {
            attempt++;
            try
            {
                var response = await _httpClient.PostAsync(endpoint, content);

                if (!response.IsSuccessStatusCode)
                {
                    string errorBody = await response.Content.ReadAsStringAsync();
                    throw new HttpRequestException($"Local LLM API error: {response.StatusCode} - {errorBody}");
                }

                return await response.Content.ReadAsStringAsync();
            }
            catch (HttpRequestException) when (attempt < _cfg.localLlmProviderConfig.maxRetries)
            {
                int delay = CalculateExponentialBackoff(attempt);
                await Task.Delay(delay);
            }
            catch (Exception ex) when (attempt < _cfg.localLlmProviderConfig.maxRetries && IsRetryableException(ex))
            {
                int delay = CalculateExponentialBackoff(attempt);
                await Task.Delay(delay);
            }
            catch
            {
                // If last attempt or non-retryable error, rethrow
                throw;
            }
        }
    }

    private List<ReplacementRecord> ExtractRecordsFromResponse(string rawResponse, string groupId, string filePath)
    {
        try
        {
            // 1) parse top-level ChatCompletionResponse
            var completion = JsonSerializer.Deserialize<ChatCompletionResponse>(rawResponse);
            if (completion?.choices == null || completion.choices.Count == 0)
                throw new InvalidOperationException("Failed to get response from LLM");

            string content = completion.choices[0].message?.content;
            if (string.IsNullOrWhiteSpace(content))
                throw new InvalidOperationException("Empty response from LLM");

            // 2) Parse the JSON response with records array
            var response = JsonSerializer.Deserialize<LocalizationResponse>(content);
            if (response?.records == null)
                throw new InvalidOperationException("Failed to parse LLM response");

            // 3) Build final ReplacementRecord
            var results = new List<ReplacementRecord>();
            foreach (var item in response.records)
            {
                var rec = new ReplacementRecord
                {
                    LineNumber = item.lineNumber,
                    FilePath = filePath,
                    Column = item.column,
                    OriginalValue = item.originalValue,
                    NewKey = $"en:{item.originalValue.Trim() ?? ""}",
                    GroupId = groupId,
                    Property = "(string literal)",
                    LineContent = item.lineText,
                    Source = ExtractionSource.CsLlm
                };

                // Store reason in Notes for debugging
                Console.WriteLine($"Line: {item.lineText}");
                Console.WriteLine($"Reason: {item.reason}");
                rec.Notes.Add($"Reason: {item.reason}");

                results.Add(rec);
            }
            return results;
        }
        catch (JsonException ex)
        {
            throw new InvalidOperationException($"Failed to parse LLM response: {ex.Message}", ex);
        }
    }

    #endregion
}