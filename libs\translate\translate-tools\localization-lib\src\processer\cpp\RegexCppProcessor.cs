/*
 * Copyright 2021-present Alcon. All rights reserved.
 * Alcon proprietary/confidential.
 * Use is subject to license terms.
 */

using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;

namespace LocalizationLib;

/// <summary>
/// A regex-based processor that extracts ALL string literals from C++ files.
/// The processor focuses on accurate string detection and location tracking.
/// Translation need validation is handled by the LLM layer.
/// </summary>
public sealed class RegexCppProcessor : ICppProcessor
{
    // Match ALL C++ string literal types, including escaped quotes
    private static readonly Regex _cppStringLiteralRegex = new(
        @"(?<prefix>L|u8|u|U)?""(?<content>(?:[^""\\]|\\.)*)""",
        RegexOptions.Compiled | RegexOptions.ExplicitCapture);

    // Match ALL C++ raw string literals (R"(...)", R"delim(...)delim")
    // Using balanced parentheses matching to handle nested parentheses
    private static readonly Regex _cppRawStringLiteralRegex = new(
        @"\bR""([^""()\s]{0,16})\((?<content>(?:[^()]|\((?<nested>)|\)(?<-nested>))*(?(nested)(?!)))\)\1""",
        RegexOptions.Compiled | RegexOptions.Singleline);

    // Skip patterns for C++ specific contexts
    private static readonly Regex _cppSkipPatternRegex = new(
        @"(?:
            # Skip preprocessor directives (including multi-line)
            ^\s*#(?:define|ifdef|ifndef|endif|pragma|include).*$|
            # Skip single-line comments
            //[^\n]*$|
            # Skip multi-line comments (non-greedy)
            /\*[^*]*\*+(?:[^/*][^*]*\*+)*/|
            # Skip logging macros (with optional whitespace and parameters)
            \b(?:LOG|TRACE|DEBUG|INFO|WARN|ERROR|FATAL)\s*\([^)]*\)|
            # Skip string literals in certain contexts (with optional whitespace)
            \b(?:const|static|constexpr)\s+(?:char|wchar_t|char8_t|char16_t|char32_t)\s*\*\s*\w+\s*=\s*""[^""]*""|
            # Skip string literals in array initializers
            \{\s*""[^""]*""\s*(?:,\s*""[^""]*""\s*)*\}|
            # Skip string literals in enum definitions
            \benum\s+\w+\s*\{[^}]*\}|
            # Skip string literals in struct/class definitions
            \b(?:struct|class)\s+\w+\s*\{[^}]*\}
        )",
        RegexOptions.Compiled | RegexOptions.IgnorePatternWhitespace | RegexOptions.Multiline);

    private readonly List<ReplacementRecord> _allReplacementRecords = new();

    /// <inheritdoc/>
    public List<ReplacementRecord> AllReplacementRecords => _allReplacementRecords;

    /// <inheritdoc/>
    public FileProcessingResult ProcessCppFile(string cppPath, string groupId)
    {
        if (!File.Exists(cppPath))
        {
            throw new FileNotFoundException($"C++ file not found: {cppPath}");
        }

        var fileContent = File.ReadAllText(cppPath);
        var result = new FileProcessingResult { FilePath = cppPath };

        // Track processed positions to avoid duplicates
        var processedPositions = new HashSet<int>();

        // Process all string literals in a single pass
        ProcessStringLiterals(fileContent, cppPath, groupId, result, processedPositions);

        return result;
    }

    /// <inheritdoc/>
    public string GetStringType(ReplacementRecord record)
    {
        // Extract the string type from the original text
        var match = _cppStringLiteralRegex.Match(record.OriginalValue);
        if (match.Success)
        {
            var prefix = match.Groups["prefix"].Value;
            return prefix switch
            {
                "L" => "wchar_t*",
                "u8" => "char8_t*",
                "u" => "char16_t*",
                "U" => "char32_t*",
                _ => "char*"
            };
        }

        // For raw string literals, default to char*
        if (_cppRawStringLiteralRegex.IsMatch(record.OriginalValue))
        {
            return "char*";
        }

        return "char*"; // Default fallback
    }

    private void ProcessStringLiterals(string fileContent, string filePath, string groupId, FileProcessingResult result, HashSet<int> processedPositions)
    {
        // First process raw string literals
        var rawMatches = _cppRawStringLiteralRegex.Matches(fileContent);
        foreach (Match match in rawMatches)
        {
            if (processedPositions.Contains(match.Index))
            {
                continue;
            }

            var originalText = match.Value;
            var delimiter = match.Groups[1].Value;
            var content = match.Groups["content"].Value;

            // Skip if the string is in a context we want to ignore
            if (ShouldSkipString(fileContent, match.Index, originalText))
            {
                continue;
            }

            AddReplacementRecord(fileContent, match.Index, originalText, content, filePath, groupId, result, processedPositions);

            // Mark the entire raw string range as processed to prevent regular string regex from matching it
            for (int i = match.Index; i < match.Index + match.Length; i++)
            {
                processedPositions.Add(i);
            }
        }

        // Then process regular string literals, but only in unprocessed positions
        var matches = _cppStringLiteralRegex.Matches(fileContent);
        foreach (Match match in matches)
        {
            if (processedPositions.Contains(match.Index))
            {
                continue;
            }

            var originalText = match.Value;
            var content = match.Groups["content"].Value;

            // Skip if the string is in a context we want to ignore
            if (ShouldSkipString(fileContent, match.Index, originalText))
            {
                continue;
            }

            AddReplacementRecord(fileContent, match.Index, originalText, content, filePath, groupId, result, processedPositions);
        }
    }

    private void AddReplacementRecord(string fileContent, int position, string originalText, string content, 
        string filePath, string groupId, FileProcessingResult result, HashSet<int> processedPositions)
    {
        var lineNumber = GetLineNumber(fileContent, position);
        var lineContent = GetLineContent(fileContent, position);
        var column = GetColumnPosition(fileContent, position, lineContent);
        var context = GetStringContext(fileContent, position, originalText);

        var record = new ReplacementRecord
        {
            Property = "(string literal)",
            OriginalValue = originalText,
            NewKey = $"en:{content}",
            FilePath = filePath,
            LineNumber = lineNumber,
            Column = column,
            GroupId = groupId,
            Source = ExtractionSource.CppRegex,
            LineContent = lineContent,
            Notes = new List<string> { context }
        };

        _allReplacementRecords.Add(record);
        result.Replacements.Add(record);
        processedPositions.Add(position);
    }

    private bool ShouldSkipString(string fileContent, int matchIndex, string originalText)
    {
        // Get the line containing the match
        var lineContent = GetLineContent(fileContent, matchIndex);
        var stringStart = lineContent.IndexOf(originalText);

        // Only skip strings in #include and #pragma directives
        if (lineContent.TrimStart().StartsWith("#include") || 
            lineContent.TrimStart().StartsWith("#pragma"))
        {
            return true;
        }

        // For #define, only skip if it's a technical constant
        if (lineContent.TrimStart().StartsWith("#define"))
        {
            var defineContent = lineContent.Substring(lineContent.IndexOf("#define") + 7).Trim();
            if (defineContent.StartsWith("_") || defineContent.ToUpper() == defineContent)
            {
                return true;
            }
        }

        // All other strings should be kept for LLM validation
        return false;
    }

    private string GetStringContext(string fileContent, int matchIndex, string originalText)
    {
        var context = new StringBuilder();
        var lineContent = GetLineContent(fileContent, matchIndex);
        var stringStart = lineContent.IndexOf(originalText);

        // Add preprocessor context if available
        if (lineContent.TrimStart().StartsWith("#"))
        {
            context.AppendLine("Context: Preprocessor directive");
        }

        // Add function/method context if available
        var functionContext = GetFunctionContext(fileContent, matchIndex);
        if (!string.IsNullOrEmpty(functionContext))
        {
            context.AppendLine($"Function: {functionContext}");
        }

        // Add class/namespace context if available
        var classContext = GetClassContext(fileContent, matchIndex);
        if (!string.IsNullOrEmpty(classContext))
        {
            // Only add class context if we're actually inside the class definition
            var classRegex = new Regex($@"\bclass\s+{Regex.Escape(classContext)}\s*\{{");
            var classMatch = classRegex.Match(fileContent.Substring(0, matchIndex));
            if (classMatch.Success)
            {
                var classStart = classMatch.Index;
                var classEnd = FindMatchingBrace(fileContent, classStart + classMatch.Length - 1);
                if (classEnd > matchIndex)
                {
                    context.AppendLine($"Class: {classContext}");
                }
            }
        }

        // Add comment context if available
        var commentContext = GetCommentContext(fileContent, matchIndex, stringStart);
        if (!string.IsNullOrEmpty(commentContext))
        {
            context.AppendLine($"Comment: {commentContext}");
        }

        // Add surrounding code context (reduced window size for better precision)
        var surroundingContext = GetSurroundingContext(fileContent, matchIndex);
        if (!string.IsNullOrEmpty(surroundingContext))
        {
            context.AppendLine($"Surrounding code: {surroundingContext}");
        }

        return context.ToString();
    }

    private string GetFunctionContext(string fileContent, int position)
    {
        // Look for the nearest function definition before the position
        var regex = new Regex(@"\b(?:void|int|bool|string|char\*|wchar_t\*|std::\w+)\s+(\w+)\s*\([^)]*\)\s*(?:const)?\s*\{");
        var matches = regex.Matches(fileContent.Substring(0, position));
        
        // Only return the innermost function that contains the position
        for (int i = matches.Count - 1; i >= 0; i--)
        {
            var match = matches[i];
            var funcStart = match.Index;
            var funcEnd = FindMatchingBrace(fileContent, funcStart + match.Length - 1);
            if (funcEnd > position)
            {
                // Check if we're in a nested function
                var nestedFunc = FindNestedFunction(fileContent, funcStart, position);
                return nestedFunc ?? match.Groups[1].Value;
            }
        }
        return null;
    }

    private string FindNestedFunction(string fileContent, int outerFuncStart, int position)
    {
        var regex = new Regex(@"\b(?:void|int|bool|string|char\*|wchar_t\*|std::\w+)\s+(\w+)\s*\([^)]*\)\s*(?:const)?\s*\{");
        var matches = regex.Matches(fileContent.Substring(outerFuncStart, position - outerFuncStart));
        
        for (int i = matches.Count - 1; i >= 0; i--)
        {
            var match = matches[i];
            var funcStart = outerFuncStart + match.Index;
            var funcEnd = FindMatchingBrace(fileContent, funcStart + match.Length - 1);
            if (funcEnd > position)
            {
                return match.Groups[1].Value;
            }
        }
        return null;
    }

    private string GetClassContext(string fileContent, int position)
    {
        // Look for the nearest class/struct definition before the position
        var regex = new Regex(@"\b(?:class|struct)\s+(\w+)\s*\{");
        var matches = regex.Matches(fileContent.Substring(0, position));
        
        // Only return the innermost class that contains the position
        for (int i = matches.Count - 1; i >= 0; i--)
        {
            var match = matches[i];
            var classStart = match.Index;
            var classEnd = FindMatchingBrace(fileContent, classStart + match.Length - 1);
            
            // Only include the class if the position is inside its braces
            if (classEnd > position)
            {
                // Check if we're in a nested class
                var nestedClass = FindNestedClass(fileContent, classStart, position);
                return nestedClass ?? match.Groups[1].Value;
            }
        }
        return null;
    }

    private string FindNestedClass(string fileContent, int outerClassStart, int position)
    {
        var regex = new Regex(@"\b(?:class|struct)\s+(\w+)\s*\{");
        var matches = regex.Matches(fileContent.Substring(outerClassStart, position - outerClassStart));
        
        for (int i = matches.Count - 1; i >= 0; i--)
        {
            var match = matches[i];
            var classStart = outerClassStart + match.Index;
            var classEnd = FindMatchingBrace(fileContent, classStart + match.Length - 1);
            if (classEnd > position)
            {
                return match.Groups[1].Value;
            }
        }
        return null;
    }

    private string GetCommentContext(string fileContent, int position, int stringStart)
    {
        var lineContent = GetLineContent(fileContent, position);
        
        // Check for single-line comment
        var commentStart = lineContent.IndexOf("//");
        if (commentStart >= 0 && commentStart < stringStart)
        {
            return lineContent.Substring(commentStart).Trim();
        }

        // Check for block comment
        var blockCommentStart = lineContent.IndexOf("/*");
        if (blockCommentStart >= 0 && blockCommentStart < stringStart)
        {
            var blockCommentEnd = lineContent.IndexOf("*/", blockCommentStart);
            if (blockCommentEnd > stringStart)
            {
                return lineContent.Substring(blockCommentStart, blockCommentEnd - blockCommentStart + 2).Trim();
            }
        }

        return null;
    }

    private string GetSurroundingContext(string fileContent, int position)
    {
        // Use a smaller window for more precise context
        var start = Math.Max(0, position - 30);
        var end = Math.Min(fileContent.Length, position + 30);
        var context = fileContent.Substring(start, end - start).Trim();
        
        // Clean up the context to make it more readable
        context = Regex.Replace(context, @"\s+", " ");
        context = Regex.Replace(context, @"[{};]", " ");
        return context.Trim();
    }

    private int FindMatchingBrace(string text, int openBracePos)
    {
        int depth = 1;
        for (int i = openBracePos + 1; i < text.Length; i++)
        {
            if (text[i] == '{') depth++;
            else if (text[i] == '}') depth--;
            if (depth == 0) return i;
        }
        return -1;
    }

    private static int GetLineNumber(string fileContent, int position)
    {
        return fileContent.Take(position).Count(c => c == '\n') + 1;
    }

    private static string GetLineContent(string fileContent, int position)
    {
        var startOfLine = position;
        while (startOfLine > 0 && fileContent[startOfLine - 1] != '\n')
        {
            startOfLine--;
        }

        var endOfLine = position;
        while (endOfLine < fileContent.Length && fileContent[endOfLine] != '\n')
        {
            endOfLine++;
        }

        return fileContent.Substring(startOfLine, endOfLine - startOfLine).TrimEnd('\r');
    }

    private static int GetColumnPosition(string fileContent, int position, string lineContent)
    {
        var lineStart = position;
        while (lineStart > 0 && fileContent[lineStart - 1] != '\n')
        {
            lineStart--;
        }
        return position - lineStart + 1;
    }

    private bool IsValidRawString(string originalText, string delimiter, string content)
    {
        // Basic validation
        if (string.IsNullOrEmpty(originalText) || !originalText.StartsWith("R\"") || !originalText.EndsWith("\""))
        {
            return false;
        }

        // Check if the delimiter is valid
        if (delimiter.Length > 16)
        {
            return false;
        }

        // Check if the content is properly enclosed
        var expectedEnd = $"){delimiter}\"";
        if (!originalText.EndsWith(expectedEnd))
        {
            return false;
        }

        return true;
    }
} 