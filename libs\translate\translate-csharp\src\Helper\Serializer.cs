﻿using System;
using System.Globalization;
using System.IO;
using System.Threading;
using System.Xml;
using System.Xml.Serialization;

namespace Alcon.Interop.Translate.Helper;

public class Serializer
{
    #region PUBLIC METHODS

    public static (bool result, string errorMessage) SerializeToXmlFile<T>(T obj, string filePath) where T : class
    {
        bool result = true;
        string errorMessage = null;

        try
        {
            XmlSerializer xs = new(typeof(T));

            XmlWriterSettings settings = new()
            {
                Indent = true
            };

            XmlSerializerNamespaces ns = new();
            ns.Add("", "");

            using XmlWriter writer = XmlWriter.Create(filePath, settings);

            xs.Serialize(writer, obj, ns);
        }
        catch (Exception e)
        {
            result = false;
            errorMessage = e.Message;
        }

        return (result, errorMessage);
    }

    public static (T, string errorMessage) DeserializeXml<T>(string xml) where T : class
    {
        CultureInfo currentCulture = SetCultureToUs();

        try
        {
            XmlSerializer xmlSerializer = new(typeof(T));
            StringReader textReader = new(xml);
            return ((T)xmlSerializer.Deserialize(textReader), null);
        }
        catch (Exception e)
        {
            return (default(T), e.ToString());
        }
        finally
        {
            // Restore the saved culture
            Thread.CurrentThread.CurrentCulture = currentCulture;
        }
    }

    public static (T, string errorMessage) DeserializeFromXmlFile<T>(string fileName) where T : class
    {
        try
        {
            string xml = File.ReadAllText(fileName);
            return DeserializeXml<T>(xml);
        }
        catch (Exception e)
        {
            return (default(T), e.ToString());
        }
    }

    #endregion

    #region PRIVATE METHODS

    private static CultureInfo SetCultureToUs()
    {
        CultureInfo oldCulture = Thread.CurrentThread.CurrentCulture;
        Thread.CurrentThread.CurrentCulture = new CultureInfo("en-US");
        return oldCulture;
    }

    #endregion
}
