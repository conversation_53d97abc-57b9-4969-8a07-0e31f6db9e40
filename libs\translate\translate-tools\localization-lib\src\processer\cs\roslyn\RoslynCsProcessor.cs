/*
 * Copyright 2021-present Alcon. All rights reserved.
 * Alcon proprietary/confidential.
 * Use is subject to license terms.
 */

using Microsoft.CodeAnalysis;
using Microsoft.CodeAnalysis.CSharp;
using Microsoft.CodeAnalysis.CSharp.Syntax;
using System.Collections.Generic;
using System.IO;

namespace LocalizationLib;

public class RoslynCsProcessor : ICsProcessor
{
    public List<ReplacementRecord> AllReplacementRecords { get; } = new List<ReplacementRecord>();

    private readonly ILiteralFilter _filter;

    public RoslynCsProcessor()
    {
        // We accept a filter in the constructor, so user can pass
        // e.g. new StringLiteralFilter() or a custom filter.
        _filter = new RoslynStringLiteralFilter();
    }

    public FileProcessingResult ProcessCsFile(string csPath, string groupId)
    {
        string originalContent = File.ReadAllText(csPath);
        var tree = CSharpSyntaxTree.ParseText(originalContent, path:csPath);
        var root = tree.GetRoot();

        var collector = new StringLiteralCollector(tree, groupId, _filter);
        collector.Visit(root);

        var fileResult = new FileProcessingResult
        {
            FilePath = csPath
        };
        fileResult.Replacements.AddRange(collector.ReplacementCandidates);
        AllReplacementRecords.AddRange(collector.ReplacementCandidates);

        return fileResult;
    }

    /// <summary>
    /// The SyntaxWalker that identifies which strings to replace.
    /// Defers skip logic to the ILiteralFilter.
    /// </summary>
    private class StringLiteralCollector : CSharpSyntaxWalker
    {
        private readonly SyntaxTree _tree;
        private readonly string _groupId;
        private readonly ILiteralFilter _filter;

        public List<ReplacementRecord> ReplacementCandidates { get; } = new List<ReplacementRecord>();
        public Dictionary<SyntaxNode, string> ReplacementMap { get; } = new Dictionary<SyntaxNode, string>();

        public StringLiteralCollector(SyntaxTree tree, string groupId, ILiteralFilter filter)
        {
            _tree = tree;
            _groupId = groupId;
            _filter = filter;
        }

        public override void VisitLiteralExpression(LiteralExpressionSyntax node)
        {
            // Only process string literal expressions
            if (!node.IsKind(SyntaxKind.StringLiteralExpression))
            {
                base.VisitLiteralExpression(node);
                return;
            }

            // The unescaped text
            string literalValue = node.Token.ValueText;

            // Let our filter decide if we skip it
            if (_filter.ShouldSkip(node, literalValue))
            {
                base.VisitLiteralExpression(node);
                return;
            }

            // Otherwise, record it
            var linePos = _tree.GetLineSpan(node.Span).StartLinePosition;
            int line = linePos.Line + 1;
            int col = linePos.Character + 1;

            var newKey = $"en:{literalValue}";
            var record = new ReplacementRecord
            {
                Property = "(string literal)",
                OriginalValue = literalValue,
                NewKey = newKey,
                FilePath = _tree.FilePath,
                LineNumber = line,
                Column = col,
                GroupId = _groupId,
                Source = ExtractionSource.CsRoslyn,
            };
            ReplacementCandidates.Add(record);

            ReplacementMap[node] = newKey;

            base.VisitLiteralExpression(node);
        }
    }

    /// <summary>
    /// Rewrites string literals to Translator.Instance.TranslateM("en:Original").
    /// </summary>
    private class TranslateRewriter : CSharpSyntaxRewriter
    {
        private readonly Dictionary<SyntaxNode, string> _map;

        public TranslateRewriter(Dictionary<SyntaxNode, string> replacementMap)
        {
            _map = replacementMap;
        }

        public override SyntaxNode VisitLiteralExpression(LiteralExpressionSyntax node)
        {
            if (_map.TryGetValue(node, out var paramValue))
            {
                // Build the new code
                string newCode = $"Translator.Instance.TranslateM(\"{Escape(paramValue)}\")";
                var newExpr = SyntaxFactory.ParseExpression(newCode);
                return newExpr;
            }

            return base.VisitLiteralExpression(node);
        }

        private string Escape(string s)
        {
            return s.Replace("\\", "\\\\").Replace("\"", "\\\"");
        }
    }
}
