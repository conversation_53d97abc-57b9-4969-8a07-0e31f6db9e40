﻿using System.Collections.Generic;
using System.Xml.Serialization;

namespace Alcon.Interop.Translate.Models;

[XmlType(TypeName = "language")]
public class LanguageTranslation
{
    #region PUBLIC PROPERTIES

    [XmlAttribute(AttributeName = "version")]
    public string Version { get; set; }

    [XmlAttribute(AttributeName = "name")]
    public string Language { get; set; }

    [XmlArray(ElementName = "fontclasses")]
    [XmlArrayItem(ElementName = "fontclass")]
    public List<FontClass> FontClasses { get; set; } = new();

    [XmlArray(ElementName = "strings")]
    [XmlArrayItem(ElementName = "group")]
    public List<LocalizedStringGroup> Groups { get; } = new();

    #endregion
}

public class FontClass
{
    #region PUBLIC PROPERTIES

    [XmlAttribute(AttributeName = "name")]
    public string Name { get; set; }

    [XmlAttribute(AttributeName = "face")]
    public string Face { get; set; }

    [XmlAttribute(AttributeName = "weight")]
    public string Weight { get; set; }

    [XmlAttribute(AttributeName = "size")]
    public uint Size { get; set; }

    #endregion
}
