{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {"localizationlib/1.0.0": {"dependencies": {"CommunityToolkit.Mvvm": "8.2.2", "Microsoft.CodeAnalysis.CSharp.Scripting": "4.10.0", "Newtonsoft.Json": "13.0.3"}, "runtime": {"localizationlib.dll": {}}}, "CommunityToolkit.Mvvm/8.2.2": {"runtime": {"lib/net6.0/CommunityToolkit.Mvvm.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.CodeAnalysis.Analyzers/3.3.4": {}, "Microsoft.CodeAnalysis.Common/4.10.0": {"dependencies": {"Microsoft.CodeAnalysis.Analyzers": "3.3.4", "System.Collections.Immutable": "8.0.0", "System.Reflection.Metadata": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.CodeAnalysis.dll": {"assemblyVersion": "********", "fileVersion": "4.1000.24.27302"}}, "resources": {"lib/net8.0/cs/Microsoft.CodeAnalysis.resources.dll": {"locale": "cs"}, "lib/net8.0/de/Microsoft.CodeAnalysis.resources.dll": {"locale": "de"}, "lib/net8.0/es/Microsoft.CodeAnalysis.resources.dll": {"locale": "es"}, "lib/net8.0/fr/Microsoft.CodeAnalysis.resources.dll": {"locale": "fr"}, "lib/net8.0/it/Microsoft.CodeAnalysis.resources.dll": {"locale": "it"}, "lib/net8.0/ja/Microsoft.CodeAnalysis.resources.dll": {"locale": "ja"}, "lib/net8.0/ko/Microsoft.CodeAnalysis.resources.dll": {"locale": "ko"}, "lib/net8.0/pl/Microsoft.CodeAnalysis.resources.dll": {"locale": "pl"}, "lib/net8.0/pt-BR/Microsoft.CodeAnalysis.resources.dll": {"locale": "pt-BR"}, "lib/net8.0/ru/Microsoft.CodeAnalysis.resources.dll": {"locale": "ru"}, "lib/net8.0/tr/Microsoft.CodeAnalysis.resources.dll": {"locale": "tr"}, "lib/net8.0/zh-Hans/Microsoft.CodeAnalysis.resources.dll": {"locale": "zh-Hans"}, "lib/net8.0/zh-Hant/Microsoft.CodeAnalysis.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.CSharp/4.10.0": {"dependencies": {"Microsoft.CodeAnalysis.Analyzers": "3.3.4", "Microsoft.CodeAnalysis.Common": "4.10.0", "System.Collections.Immutable": "8.0.0", "System.Reflection.Metadata": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.CodeAnalysis.CSharp.dll": {"assemblyVersion": "********", "fileVersion": "4.1000.24.27302"}}, "resources": {"lib/net8.0/cs/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "cs"}, "lib/net8.0/de/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "de"}, "lib/net8.0/es/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "es"}, "lib/net8.0/fr/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "fr"}, "lib/net8.0/it/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "it"}, "lib/net8.0/ja/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ja"}, "lib/net8.0/ko/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ko"}, "lib/net8.0/pl/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "pl"}, "lib/net8.0/pt-BR/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "pt-BR"}, "lib/net8.0/ru/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ru"}, "lib/net8.0/tr/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "tr"}, "lib/net8.0/zh-Hans/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "zh-Hans"}, "lib/net8.0/zh-Hant/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.CSharp.Scripting/4.10.0": {"dependencies": {"Microsoft.CSharp": "4.7.0", "Microsoft.CodeAnalysis.Analyzers": "3.3.4", "Microsoft.CodeAnalysis.CSharp": "4.10.0", "Microsoft.CodeAnalysis.Common": "4.10.0", "Microsoft.CodeAnalysis.Scripting.Common": "4.10.0", "System.Collections.Immutable": "8.0.0", "System.Reflection.Metadata": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.CodeAnalysis.CSharp.Scripting.dll": {"assemblyVersion": "********", "fileVersion": "4.1000.24.27302"}}, "resources": {"lib/net8.0/cs/Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll": {"locale": "cs"}, "lib/net8.0/de/Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll": {"locale": "de"}, "lib/net8.0/es/Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll": {"locale": "es"}, "lib/net8.0/fr/Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll": {"locale": "fr"}, "lib/net8.0/it/Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll": {"locale": "it"}, "lib/net8.0/ja/Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll": {"locale": "ja"}, "lib/net8.0/ko/Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll": {"locale": "ko"}, "lib/net8.0/pl/Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll": {"locale": "pl"}, "lib/net8.0/pt-BR/Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll": {"locale": "pt-BR"}, "lib/net8.0/ru/Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll": {"locale": "ru"}, "lib/net8.0/tr/Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll": {"locale": "tr"}, "lib/net8.0/zh-Hans/Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll": {"locale": "zh-Hans"}, "lib/net8.0/zh-Hant/Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.Scripting.Common/4.10.0": {"dependencies": {"Microsoft.CodeAnalysis.Analyzers": "3.3.4", "Microsoft.CodeAnalysis.Common": "4.10.0", "System.Collections.Immutable": "8.0.0", "System.Reflection.Metadata": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.CodeAnalysis.Scripting.dll": {"assemblyVersion": "********", "fileVersion": "4.1000.24.27302"}}, "resources": {"lib/net8.0/cs/Microsoft.CodeAnalysis.Scripting.resources.dll": {"locale": "cs"}, "lib/net8.0/de/Microsoft.CodeAnalysis.Scripting.resources.dll": {"locale": "de"}, "lib/net8.0/es/Microsoft.CodeAnalysis.Scripting.resources.dll": {"locale": "es"}, "lib/net8.0/fr/Microsoft.CodeAnalysis.Scripting.resources.dll": {"locale": "fr"}, "lib/net8.0/it/Microsoft.CodeAnalysis.Scripting.resources.dll": {"locale": "it"}, "lib/net8.0/ja/Microsoft.CodeAnalysis.Scripting.resources.dll": {"locale": "ja"}, "lib/net8.0/ko/Microsoft.CodeAnalysis.Scripting.resources.dll": {"locale": "ko"}, "lib/net8.0/pl/Microsoft.CodeAnalysis.Scripting.resources.dll": {"locale": "pl"}, "lib/net8.0/pt-BR/Microsoft.CodeAnalysis.Scripting.resources.dll": {"locale": "pt-BR"}, "lib/net8.0/ru/Microsoft.CodeAnalysis.Scripting.resources.dll": {"locale": "ru"}, "lib/net8.0/tr/Microsoft.CodeAnalysis.Scripting.resources.dll": {"locale": "tr"}, "lib/net8.0/zh-Hans/Microsoft.CodeAnalysis.Scripting.resources.dll": {"locale": "zh-Hans"}, "lib/net8.0/zh-Hant/Microsoft.CodeAnalysis.Scripting.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CSharp/4.7.0": {}, "Newtonsoft.Json/13.0.3": {"runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"assemblyVersion": "********", "fileVersion": "13.0.3.27908"}}}, "System.Collections.Immutable/8.0.0": {}, "System.Reflection.Metadata/8.0.0": {"dependencies": {"System.Collections.Immutable": "8.0.0"}}}}, "libraries": {"localizationlib/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "CommunityToolkit.Mvvm/8.2.2": {"type": "package", "serviceable": true, "sha512": "sha512-r0g0k9tGYdrnz8R7T3x5UiokDffeevzK/2P/9SBL6fqLgN8B157MIi/bVUWI1KAz6ZorZrK9AdABCWUeXZZsvA==", "path": "communitytoolkit.mvvm/8.2.2", "hashPath": "communitytoolkit.mvvm.8.2.2.nupkg.sha512"}, "Microsoft.CodeAnalysis.Analyzers/3.3.4": {"type": "package", "serviceable": true, "sha512": "sha512-AxkxcPR+rheX0SmvpLVIGLhOUXAKG56a64kV9VQZ4y9gR9ZmPXnqZvHJnmwLSwzrEP6junUF11vuc+aqo5r68g==", "path": "microsoft.codeanalysis.analyzers/3.3.4", "hashPath": "microsoft.codeanalysis.analyzers.3.3.4.nupkg.sha512"}, "Microsoft.CodeAnalysis.Common/4.10.0": {"type": "package", "serviceable": true, "sha512": "sha512-7O4+dn0fNKykPpEB1i8/5EKzwD3fuu/shdbbnnsBmdiHMaBz6telOubDFwPwLQQ/PvOAWTFIWWTyAOmWvXRD2g==", "path": "microsoft.codeanalysis.common/4.10.0", "hashPath": "microsoft.codeanalysis.common.4.10.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.CSharp/4.10.0": {"type": "package", "serviceable": true, "sha512": "sha512-iifqKy3KvCgPABHFbFlSxjEoE+OItZGuZ191NM/TWV750m1jMypr7BtrP65ET+OK2KNVupO8S8xCtxbNqw056A==", "path": "microsoft.codeanalysis.csharp/4.10.0", "hashPath": "microsoft.codeanalysis.csharp.4.10.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.CSharp.Scripting/4.10.0": {"type": "package", "serviceable": true, "sha512": "sha512-B8QflImoUzHSAoSF3bkvX7HmVs1H3KWg5joP3aNl+MNByKhqZsOi1//YUt6ALl9HvO8byB6Z0OC2yaTUpfD0tw==", "path": "microsoft.codeanalysis.csharp.scripting/4.10.0", "hashPath": "microsoft.codeanalysis.csharp.scripting.4.10.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.Scripting.Common/4.10.0": {"type": "package", "serviceable": true, "sha512": "sha512-L+IbItJyxtLcbSxRXvdJRCGMW9IgHXZvNl5VcuFMcb49nFnhqrm79y50GOukSyBccF0WlnalbjRK/Beb2aKJbw==", "path": "microsoft.codeanalysis.scripting.common/4.10.0", "hashPath": "microsoft.codeanalysis.scripting.common.4.10.0.nupkg.sha512"}, "Microsoft.CSharp/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-pTj+D3uJWyN3My70i2Hqo+OXixq3Os2D1nJ2x92FFo6sk8fYS1m1WLNTs0Dc1uPaViH0YvEEwvzddQ7y4rhXmA==", "path": "microsoft.csharp/4.7.0", "hashPath": "microsoft.csharp.4.7.0.nupkg.sha512"}, "Newtonsoft.Json/13.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "path": "newtonsoft.json/13.0.3", "hashPath": "newtonsoft.json.13.0.3.nupkg.sha512"}, "System.Collections.Immutable/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-AurL6Y5BA1WotzlEvVaIDpqzpIPvYnnldxru8oXJU2yFxFUy3+pNXjXd1ymO+RA0rq0+590Q8gaz2l3Sr7fmqg==", "path": "system.collections.immutable/8.0.0", "hashPath": "system.collections.immutable.8.0.0.nupkg.sha512"}, "System.Reflection.Metadata/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ptvgrFh7PvWI8bcVqG5rsA/weWM09EnthFHR5SCnS6IN+P4mj6rE1lBDC4U8HL9/57htKAqy4KQ3bBj84cfYyQ==", "path": "system.reflection.metadata/8.0.0", "hashPath": "system.reflection.metadata.8.0.0.nupkg.sha512"}}}