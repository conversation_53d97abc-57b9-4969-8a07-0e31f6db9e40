{"Version": 1, "WorkspaceRootPath": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-extractor\\build\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{7010611E-5491-3CA3-BC86-93F184DB937B}|localization_extractor.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-extractor\\build\\localization_extractor.csproj||{04B8AB82-A572-4FEF-95CE-5222444B6B64}|", "RelativeMoniker": "D:0:0:{7010611E-5491-3CA3-BC86-93F184DB937B}|localization_extractor.csproj|solutionrelative:localization_extractor.csproj||{04B8AB82-A572-4FEF-95CE-5222444B6B64}|"}, {"AbsoluteMoniker": "D:0:0:{45258CA8-B215-3C81-B480-6857ECC13998}|trtralocali\\localizationlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\projectlocalizationextractor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{7010611E-5491-3CA3-BC86-93F184DB937B}|localization_extractor.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-extractor\\src\\tooloptions.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{45258CA8-B215-3C81-B480-6857ECC13998}|trtralocali\\localizationlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\processer\\xaml\\xamlprocessor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{45258CA8-B215-3C81-B480-6857ECC13998}|trtralocali\\localizationlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\processer\\cs\\llm\\llmverificationcache.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{45258CA8-B215-3C81-B480-6857ECC13998}|trtralocali\\localizationlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\processer\\cs\\llm\\llmcachemanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{7010611E-5491-3CA3-BC86-93F184DB937B}|localization_extractor.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-extractor\\src\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{45258CA8-B215-3C81-B480-6857ECC13998}|trtralocali\\localizationlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\processer\\cs\\consolidatedcsprocessor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{45258CA8-B215-3C81-B480-6857ECC13998}|trtralocali\\localizationlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\processer\\cs\\llm\\illmclient.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{45258CA8-B215-3C81-B480-6857ECC13998}|trtralocali\\localizationlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\processer\\cpp\\consolidatedcppprocessor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{45258CA8-B215-3C81-B480-6857ECC13998}|trtralocali\\localizationlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\processer\\cs\\llm\\localllmclient.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{45258CA8-B215-3C81-B480-6857ECC13998}|trtralocali\\localizationlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\processer\\cs\\llm\\llmprompts.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{45258CA8-B215-3C81-B480-6857ECC13998}|trtralocali\\localizationlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\common\\projecttextextractorconfig.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{45258CA8-B215-3C81-B480-6857ECC13998}|trtralocali\\localizationlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\processer\\xaml\\xamlextractor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{45258CA8-B215-3C81-B480-6857ECC13998}|trtralocali\\localizationlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\processer\\cs\\llm\\llmclientfactory.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{45258CA8-B215-3C81-B480-6857ECC13998}|trtralocali\\localizationlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\processer\\cs\\llm\\llmcsprocessor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{45258CA8-B215-3C81-B480-6857ECC13998}|trtralocali\\localizationlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\common\\projecttextextractorconfigmanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{45258CA8-B215-3C81-B480-6857ECC13998}|trtralocali\\localizationlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\code-restructure\\xamltexttocodebehindprocessor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{45258CA8-B215-3C81-B480-6857ECC13998}|trtralocali\\localizationlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\processer\\cpp\\cppextractor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{45258CA8-B215-3C81-B480-6857ECC13998}|trtralocali\\localizationlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\utils\\llmconfigpathresolver.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{45258CA8-B215-3C81-B480-6857ECC13998}|trtralocali\\localizationlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\processer\\cs\\csextractor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{7010611E-5491-3CA3-BC86-93F184DB937B}|localization_extractor.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-extractor\\cmakelists.txt||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{45258CA8-B215-3C81-B480-6857ECC13998}|trtralocali\\localizationlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\projectlocalizationreplacer.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{45258CA8-B215-3C81-B480-6857ECC13998}|trtralocali\\localizationlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\code-restructure\\stringinterpolationprocessor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{45258CA8-B215-3C81-B480-6857ECC13998}|trtralocali\\localizationlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\replacer\\xamlreplacer.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{45258CA8-B215-3C81-B480-6857ECC13998}|trtralocali\\localizationlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\utils\\pathutils.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{45258CA8-B215-3C81-B480-6857ECC13998}|trtralocali\\localizationlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\processer\\cs\\roslyn\\roslyncsprocessor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{45258CA8-B215-3C81-B480-6857ECC13998}|trtralocali\\localizationlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\utils\\localizationutils.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{45258CA8-B215-3C81-B480-6857ECC13998}|trtralocali\\localizationlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\merge\\filemerge.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{45258CA8-B215-3C81-B480-6857ECC13998}|trtralocali\\localizationlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\processer\\cs\\regex\\regexcsprocessor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{45258CA8-B215-3C81-B480-6857ECC13998}|trtralocali\\localizationlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\processer\\cs\\roslyn\\roslynstringliteralfilter.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{45258CA8-B215-3C81-B480-6857ECC13998}|trtralocali\\localizationlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\processer\\cs\\csprocessorhelpers.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{45258CA8-B215-3C81-B480-6857ECC13998}|trtralocali\\localizationlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-extractor\\build\\trtralocali\\localizationlib.csproj||{04B8AB82-A572-4FEF-95CE-5222444B6B64}|", "RelativeMoniker": "D:0:0:{45258CA8-B215-3C81-B480-6857ECC13998}|trtralocali\\localizationlib.csproj|solutionrelative:trtralocali\\localizationlib.csproj||{04B8AB82-A572-4FEF-95CE-5222444B6B64}|"}, {"AbsoluteMoniker": "D:0:0:{45258CA8-B215-3C81-B480-6857ECC13998}|trtralocali\\localizationlib.csproj|d:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\replacer\\csreplacer.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 7, "Children": [{"$type": "Document", "DocumentIndex": 28, "Title": "FileMerge.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\merge\\FileMerge.cs", "RelativeDocumentMoniker": "..\\..\\localization-lib\\src\\merge\\FileMerge.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\merge\\FileMerge.cs", "RelativeToolTip": "..\\..\\localization-lib\\src\\merge\\FileMerge.cs", "ViewState": "AgIAALwAAAAAAAAAAAAwwMoAAAAUAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-02-28T05:24:30.539Z", "IsPinned": true}, {"$type": "Bookmark", "Name": "ST:0:0:{e1b7d1f8-9b3c-49b1-8f4f-bfc63a88835d}"}, {"$type": "Document", "DocumentIndex": 12, "Title": "ProjectTextExtractorConfig.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\common\\ProjectTextExtractorConfig.cs", "RelativeDocumentMoniker": "..\\..\\localization-lib\\src\\common\\ProjectTextExtractorConfig.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\common\\ProjectTextExtractorConfig.cs", "RelativeToolTip": "..\\..\\localization-lib\\src\\common\\ProjectTextExtractorConfig.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAsAAAAcAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-01T09:55:02.156Z"}, {"$type": "Document", "DocumentIndex": 9, "Title": "ConsolidatedCppProcessor.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\processer\\cpp\\ConsolidatedCppProcessor.cs", "RelativeDocumentMoniker": "..\\..\\localization-lib\\src\\processer\\cpp\\ConsolidatedCppProcessor.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\processer\\cpp\\ConsolidatedCppProcessor.cs", "RelativeToolTip": "..\\..\\localization-lib\\src\\processer\\cpp\\ConsolidatedCppProcessor.cs", "ViewState": "AgIAAAsAAAAAAAAAAAAIwCgAAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-25T08:00:06.438Z"}, {"$type": "Document", "DocumentIndex": 5, "Title": "LlmCacheManager.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\processer\\cs\\llm\\LlmCacheManager.cs", "RelativeDocumentMoniker": "..\\..\\localization-lib\\src\\processer\\cs\\llm\\LlmCacheManager.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\processer\\cs\\llm\\LlmCacheManager.cs", "RelativeToolTip": "..\\..\\localization-lib\\src\\processer\\cs\\llm\\LlmCacheManager.cs", "ViewState": "AgIAABUAAAAAAAAAAADwvywAAAAMAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-26T16:08:15.301Z"}, {"$type": "Document", "DocumentIndex": 13, "Title": "XamlExtractor.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\processer\\xaml\\XamlExtractor.cs", "RelativeDocumentMoniker": "..\\..\\localization-lib\\src\\processer\\xaml\\XamlExtractor.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\processer\\xaml\\XamlExtractor.cs", "RelativeToolTip": "..\\..\\localization-lib\\src\\processer\\xaml\\XamlExtractor.cs", "ViewState": "AgIAABQAAAAAAAAAAAAkwCIAAABGAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-03-26T10:58:01.004Z"}, {"$type": "Document", "DocumentIndex": 2, "Title": "ToolOptions.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-extractor\\src\\ToolOptions.cs", "RelativeDocumentMoniker": "..\\src\\ToolOptions.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-extractor\\src\\ToolOptions.cs", "RelativeToolTip": "..\\src\\ToolOptions.cs", "ViewState": "AgIAACYAAAAAAAAAAIA3wDQAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-03-14T06:00:58.482Z"}, {"$type": "Document", "DocumentIndex": 0, "Title": "localization_extractor", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-extractor\\build\\localization_extractor.csproj", "RelativeDocumentMoniker": "localization_extractor.csproj", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-extractor\\build\\localization_extractor.csproj", "RelativeToolTip": "localization_extractor.csproj", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000758|", "WhenOpened": "2025-04-04T07:53:22.538Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "XamlProcessor.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\processer\\xaml\\XamlProcessor.cs", "RelativeDocumentMoniker": "..\\..\\localization-lib\\src\\processer\\xaml\\XamlProcessor.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\processer\\xaml\\XamlProcessor.cs", "RelativeToolTip": "..\\..\\localization-lib\\src\\processer\\xaml\\XamlProcessor.cs", "ViewState": "AgIAAOwAAAAAAAAAAADwv/YAAAAMAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-03-26T10:57:44.407Z"}, {"$type": "Document", "DocumentIndex": 7, "Title": "ConsolidatedCsProcessor.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\processer\\cs\\ConsolidatedCsProcessor.cs", "RelativeDocumentMoniker": "..\\..\\localization-lib\\src\\processer\\cs\\ConsolidatedCsProcessor.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\processer\\cs\\ConsolidatedCsProcessor.cs", "RelativeToolTip": "..\\..\\localization-lib\\src\\processer\\cs\\ConsolidatedCsProcessor.cs", "ViewState": "AgIAAGsBAAAAAAAAAADwv3EBAAAQAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-03-26T15:24:58.898Z"}, {"$type": "Document", "DocumentIndex": 8, "Title": "ILlmClient.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\processer\\cs\\llm\\ILlmClient.cs", "RelativeDocumentMoniker": "..\\..\\localization-lib\\src\\processer\\cs\\llm\\ILlmClient.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\processer\\cs\\llm\\ILlmClient.cs", "RelativeToolTip": "..\\..\\localization-lib\\src\\processer\\cs\\llm\\ILlmClient.cs", "ViewState": "AgIAAD4AAAAAAAAAAADwv1AAAAAiAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-03-26T13:23:32.417Z"}, {"$type": "Document", "DocumentIndex": 4, "Title": "LlmVerificationCache.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\processer\\cs\\llm\\LlmVerificationCache.cs", "RelativeDocumentMoniker": "..\\..\\localization-lib\\src\\processer\\cs\\llm\\LlmVerificationCache.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\processer\\cs\\llm\\LlmVerificationCache.cs", "RelativeToolTip": "..\\..\\localization-lib\\src\\processer\\cs\\llm\\LlmVerificationCache.cs", "ViewState": "AgIAADUAAAAAAAAAAIA2wFAAAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-26T16:08:47.835Z"}, {"$type": "Document", "DocumentIndex": 6, "Title": "Program.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-extractor\\src\\Program.cs", "RelativeDocumentMoniker": "..\\src\\Program.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-extractor\\src\\Program.cs", "RelativeToolTip": "..\\src\\Program.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAACUAAAAdAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-03-14T06:04:27.978Z"}, {"$type": "Document", "DocumentIndex": 17, "Title": "XamlTextToCodeBehindProcessor.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\code-restructure\\XamlTextToCodeBehindProcessor.cs", "RelativeDocumentMoniker": "..\\..\\localization-lib\\src\\code-restructure\\XamlTextToCodeBehindProcessor.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\code-restructure\\XamlTextToCodeBehindProcessor.cs", "RelativeToolTip": "..\\..\\localization-lib\\src\\code-restructure\\XamlTextToCodeBehindProcessor.cs", "ViewState": "AgIAAA4AAAAAAAAAAAAWwB4AAAAqAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-28T06:56:13.213Z"}, {"$type": "Document", "DocumentIndex": 10, "Title": "LocalLlmClient.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\processer\\cs\\llm\\LocalLlmClient.cs", "RelativeDocumentMoniker": "..\\..\\localization-lib\\src\\processer\\cs\\llm\\LocalLlmClient.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\processer\\cs\\llm\\LocalLlmClient.cs", "RelativeToolTip": "..\\..\\localization-lib\\src\\processer\\cs\\llm\\LocalLlmClient.cs", "ViewState": "AgIAALsAAAAAAAAAAAAUwHcAAAAEAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-03-26T13:23:45.077Z"}, {"$type": "Document", "DocumentIndex": 14, "Title": "LlmClientFactory.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\processer\\cs\\llm\\LlmClientFactory.cs", "RelativeDocumentMoniker": "..\\..\\localization-lib\\src\\processer\\cs\\llm\\LlmClientFactory.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\processer\\cs\\llm\\LlmClientFactory.cs", "RelativeToolTip": "..\\..\\localization-lib\\src\\processer\\cs\\llm\\LlmClientFactory.cs", "ViewState": "AgIAAGQAAAAAAAAAAAAQwG8AAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-23T11:43:42.629Z"}, {"$type": "Document", "DocumentIndex": 11, "Title": "LlmPrompts.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\processer\\cs\\llm\\LlmPrompts.cs", "RelativeDocumentMoniker": "..\\..\\localization-lib\\src\\processer\\cs\\llm\\LlmPrompts.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\processer\\cs\\llm\\LlmPrompts.cs", "RelativeToolTip": "..\\..\\localization-lib\\src\\processer\\cs\\llm\\LlmPrompts.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-27T06:42:09.501Z"}, {"$type": "Document", "DocumentIndex": 16, "Title": "ProjectTextExtractorConfigManager.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\common\\ProjectTextExtractorConfigManager.cs", "RelativeDocumentMoniker": "..\\..\\localization-lib\\src\\common\\ProjectTextExtractorConfigManager.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\common\\ProjectTextExtractorConfigManager.cs", "RelativeToolTip": "..\\..\\localization-lib\\src\\common\\ProjectTextExtractorConfigManager.cs", "ViewState": "AgIAADECAAAAAAAAAAAWwEQCAAATAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-01T07:58:09.904Z"}, {"$type": "Document", "DocumentIndex": 15, "Title": "LLMCsProcessor.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\processer\\cs\\llm\\LLMCsProcessor.cs", "RelativeDocumentMoniker": "..\\..\\localization-lib\\src\\processer\\cs\\llm\\LLMCsProcessor.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\processer\\cs\\llm\\LLMCsProcessor.cs", "RelativeToolTip": "..\\..\\localization-lib\\src\\processer\\cs\\llm\\LLMCsProcessor.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-03-26T13:23:40.556Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 20, "Title": "CsExtractor.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\processer\\cs\\CsExtractor.cs", "RelativeDocumentMoniker": "..\\..\\localization-lib\\src\\processer\\cs\\CsExtractor.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\processer\\cs\\CsExtractor.cs", "RelativeToolTip": "..\\..\\localization-lib\\src\\processer\\cs\\CsExtractor.cs", "ViewState": "AgIAAB8AAAAAAAAAAADgvyEAAAA2AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-03-26T10:42:54.521Z"}, {"$type": "Document", "DocumentIndex": 1, "Title": "ProjectLocalizationExtractor.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\ProjectLocalizationExtractor.cs", "RelativeDocumentMoniker": "..\\..\\localization-lib\\src\\ProjectLocalizationExtractor.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\ProjectLocalizationExtractor.cs", "RelativeToolTip": "..\\..\\localization-lib\\src\\ProjectLocalizationExtractor.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAwAAAAnAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-03-26T14:05:53.018Z"}, {"$type": "Document", "DocumentIndex": 18, "Title": "CppExtractor.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\processer\\cpp\\CppExtractor.cs", "RelativeDocumentMoniker": "..\\..\\localization-lib\\src\\processer\\cpp\\CppExtractor.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\processer\\cpp\\CppExtractor.cs", "RelativeToolTip": "..\\..\\localization-lib\\src\\processer\\cpp\\CppExtractor.cs", "ViewState": "AgIAAA8AAAAAAAAAAAAAAD8AAAAMAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-25T08:00:11.458Z"}, {"$type": "Document", "DocumentIndex": 19, "Title": "LLMConfigPathResolver.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\utils\\LLMConfigPathResolver.cs", "RelativeDocumentMoniker": "..\\..\\localization-lib\\src\\utils\\LLMConfigPathResolver.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\utils\\LLMConfigPathResolver.cs", "RelativeToolTip": "..\\..\\localization-lib\\src\\utils\\LLMConfigPathResolver.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAwAAAAnAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-16T04:51:26.381Z"}, {"$type": "Document", "DocumentIndex": 21, "Title": "CMakeLists.txt", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-extractor\\CMakeLists.txt", "RelativeDocumentMoniker": "..\\CMakeLists.txt", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-extractor\\CMakeLists.txt", "RelativeToolTip": "..\\CMakeLists.txt", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003109|", "WhenOpened": "2025-04-28T07:03:09.862Z"}, {"$type": "Document", "DocumentIndex": 22, "Title": "ProjectLocalizationReplacer.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\ProjectLocalizationReplacer.cs", "RelativeDocumentMoniker": "..\\..\\localization-lib\\src\\ProjectLocalizationReplacer.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\ProjectLocalizationReplacer.cs", "RelativeToolTip": "..\\..\\localization-lib\\src\\ProjectLocalizationReplacer.cs", "ViewState": "AgIAAAkAAAAAAAAAAAAowBYAAABcAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-03-31T11:43:21.672Z"}, {"$type": "Document", "DocumentIndex": 24, "Title": "XamlReplacer.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\replacer\\XamlReplacer.cs", "RelativeDocumentMoniker": "..\\..\\localization-lib\\src\\replacer\\XamlReplacer.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\replacer\\XamlReplacer.cs", "RelativeToolTip": "..\\..\\localization-lib\\src\\replacer\\XamlReplacer.cs", "ViewState": "AgIAACUAAAAAAAAAAAD4vykAAABvAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-21T08:25:30.581Z"}, {"$type": "Document", "DocumentIndex": 23, "Title": "StringInterpolationProcessor.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\code-restructure\\StringInterpolationProcessor.cs", "RelativeDocumentMoniker": "..\\..\\localization-lib\\src\\code-restructure\\StringInterpolationProcessor.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\code-restructure\\StringInterpolationProcessor.cs", "RelativeToolTip": "..\\..\\localization-lib\\src\\code-restructure\\StringInterpolationProcessor.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA8AAAAtAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-21T14:13:32.394Z"}, {"$type": "Document", "DocumentIndex": 25, "Title": "PathUtils.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\utils\\PathUtils.cs", "RelativeDocumentMoniker": "..\\..\\localization-lib\\src\\utils\\PathUtils.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\utils\\PathUtils.cs", "RelativeToolTip": "..\\..\\localization-lib\\src\\utils\\PathUtils.cs", "ViewState": "AgIAAAgAAAAAAAAAAAAqwAsAAAAMAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-04-08T07:47:18.351Z"}, {"$type": "Document", "DocumentIndex": 26, "Title": "RoslynCsProcessor.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\processer\\cs\\roslyn\\RoslynCsProcessor.cs", "RelativeDocumentMoniker": "..\\..\\localization-lib\\src\\processer\\cs\\roslyn\\RoslynCsProcessor.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\processer\\cs\\roslyn\\RoslynCsProcessor.cs", "RelativeToolTip": "..\\..\\localization-lib\\src\\processer\\cs\\roslyn\\RoslynCsProcessor.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA4AAAAeAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-03-31T11:06:43.205Z"}, {"$type": "Document", "DocumentIndex": 27, "Title": "LocalizationUtils.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\utils\\LocalizationUtils.cs", "RelativeDocumentMoniker": "..\\..\\localization-lib\\src\\utils\\LocalizationUtils.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\utils\\LocalizationUtils.cs", "RelativeToolTip": "..\\..\\localization-lib\\src\\utils\\LocalizationUtils.cs", "ViewState": "AgIAADgAAAAAAAAAAAAowEYAAAAZAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-03-24T08:41:38.272Z"}, {"$type": "Document", "DocumentIndex": 29, "Title": "RegexCsProcessor.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\processer\\cs\\regex\\RegexCsProcessor.cs", "RelativeDocumentMoniker": "..\\..\\localization-lib\\src\\processer\\cs\\regex\\RegexCsProcessor.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\processer\\cs\\regex\\RegexCsProcessor.cs", "RelativeToolTip": "..\\..\\localization-lib\\src\\processer\\cs\\regex\\RegexCsProcessor.cs", "ViewState": "AgIAABcAAAAAAAAAAAAiwCAAAAAtAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-03-26T10:43:07.004Z"}, {"$type": "Document", "DocumentIndex": 30, "Title": "RoslynStringLiteralFilter.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\processer\\cs\\roslyn\\RoslynStringLiteralFilter.cs", "RelativeDocumentMoniker": "..\\..\\localization-lib\\src\\processer\\cs\\roslyn\\RoslynStringLiteralFilter.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\processer\\cs\\roslyn\\RoslynStringLiteralFilter.cs", "RelativeToolTip": "..\\..\\localization-lib\\src\\processer\\cs\\roslyn\\RoslynStringLiteralFilter.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-03-31T11:06:42.518Z"}, {"$type": "Document", "DocumentIndex": 31, "Title": "CsProcessorHelpers.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\processer\\cs\\CsProcessorHelpers.cs", "RelativeDocumentMoniker": "..\\..\\localization-lib\\src\\processer\\cs\\CsProcessorHelpers.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\processer\\cs\\CsProcessorHelpers.cs", "RelativeToolTip": "..\\..\\localization-lib\\src\\processer\\cs\\CsProcessorHelpers.cs", "ViewState": "AgIAAAwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-03-26T14:05:37.978Z"}, {"$type": "Document", "DocumentIndex": 32, "Title": "localizationlib", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-extractor\\build\\trtralocali\\localizationlib.csproj", "RelativeDocumentMoniker": "trtralocali\\localizationlib.csproj", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-extractor\\build\\trtralocali\\localizationlib.csproj", "RelativeToolTip": "trtralocali\\localizationlib.csproj", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000758|", "WhenOpened": "2025-02-05T09:56:19.332Z"}, {"$type": "Document", "DocumentIndex": 33, "Title": "CsReplacer.cs", "DocumentMoniker": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\replacer\\CsReplacer.cs", "RelativeDocumentMoniker": "..\\..\\localization-lib\\src\\replacer\\CsReplacer.cs", "ToolTip": "D:\\kibisoft\\workarea\\git\\interop\\libs\\translate\\translate-tools\\localization-lib\\src\\replacer\\CsReplacer.cs", "RelativeToolTip": "..\\..\\localization-lib\\src\\replacer\\CsReplacer.cs", "ViewState": "AgIAAA4AAAAAAAAAAAAgwCIAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-03-19T04:15:42.555Z"}]}]}]}