/*
 * Copyright 2021-present Alcon. All rights reserved.
 * Alcon proprietary/confidential.
 * Use is subject to license terms.
 */

using NUnit.Framework;
using System;
using System.Collections.Generic;
using System.IO;

namespace LocalizationLib.Tests;

/// <summary>
/// Tests for the ReportGenerator class.
/// </summary>
[TestFixture]
public class ReportGeneratorTests
{
    [Test]
    public void GenerateReport_CreatesReportFileWithCorrectContent()
    {
        // Arrange: Create dummy file processing results.
        List<FileProcessingResult> fileResults = new List<FileProcessingResult>
            {
                new FileProcessingResult
                {
                    FilePath = "Sample1.xaml",
                    Changed = true,
                    Replacements = new List<ReplacementRecord>
                    {
                        new ReplacementRecord
                        {
                            Property = "Content",
                            OriginalValue = "Cancel",
                            NewKey = "en:Cancel",
                            LineNumber = 10,
                            Column = 15
                        }
                    },
                    Notes = new List<string> { "Added trConv namespace." }
                },
                new FileProcessingResult
                {
                    FilePath = "Sample2.xaml",
                    Changed = false,
                    Replacements = new List<ReplacementRecord>(),
                    Notes = new List<string>()
                }
            };

        string tempReportFile = Path.Combine(Path.GetTempPath(), Guid.NewGuid() + ".txt");
        ReportGenerator generator = new ReportGenerator();

        // Act: Generate the report.
        generator.GenerateReport(tempReportFile, fileResults, false);

        // Assert: Verify the report file was created and contains expected content.
        Assert.IsTrue(File.Exists(tempReportFile), "Report file should exist.");
        string reportContent = File.ReadAllText(tempReportFile);
        StringAssert.Contains("Sample1.xaml", reportContent);
        StringAssert.Contains("en:Cancel", reportContent);
        StringAssert.Contains("Line 10, Column 15", reportContent);
        StringAssert.Contains("Added trConv namespace.", reportContent);
        StringAssert.Contains("Total replacements", reportContent);

        // Clean up the temporary report file.
        File.Delete(tempReportFile);
    }
}