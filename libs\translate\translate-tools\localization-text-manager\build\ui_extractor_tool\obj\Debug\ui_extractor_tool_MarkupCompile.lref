﻿D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-text-manager\build\ui_extractor_tool\obj\Debug\GeneratedInternalTypeHelper.g.cs

FD:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-uitext-extractor\src\Extractor\UIExtractorWindow.xaml;src\Extractor\UIExtractorWindow.xaml;
FD:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-uitext-extractor\src\Style\UIExtractorStyles.xaml;src\Style\UIExtractorStyles.xaml;
FD:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-uitext-extractor\src\Views\BulkActions.xaml;src\Views\BulkActions.xaml;
FD:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-uitext-extractor\src\Views\FileSelectionPanel.xaml;src\Views\FileSelectionPanel.xaml;
FD:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-uitext-extractor\src\Views\FilterPanel.xaml;src\Views\FilterPanel.xaml;
FD:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-uitext-extractor\src\Views\SourceMappingWindow.xaml;src\Views\SourceMappingWindow.xaml;
FD:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-uitext-extractor\src\Views\TextGrid.xaml;src\Views\TextGrid.xaml;
FD:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-uitext-extractor\src\Views\UITextInfoWindow.xaml;src\Views\UITextInfoWindow.xaml;

