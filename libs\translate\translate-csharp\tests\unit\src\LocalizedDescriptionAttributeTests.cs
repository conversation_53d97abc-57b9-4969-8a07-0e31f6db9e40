using NUnit.Framework;
using System.Collections.Generic;
using System.IO;

namespace Alcon.Interop.Translate.Tests
{
    [TestFixture]
    public class LocalizedDescriptionAttributeTests
    {
        private Translator _translator;
        private string _spanishFilePath;
        private string _romanianFilePath;

        [SetUp]
        public void SetUp()
        {
            // Create a fresh Translator before each test
            _translator = Translator.Instance;

            // Point to the XML file in a "TestData" folder within your test project output
            var testDataDir = Path.Combine(TestContext.CurrentContext.TestDirectory, "..", "..", "..", "etc");
            testDataDir = Path.GetFullPath(testDataDir); // Normalize the path
            _spanishFilePath = Path.Combine(testDataDir, "PlanWidget_es.xml");
            _romanianFilePath = Path.Combine(testDataDir, "PlanWidget_ro.xml");
        }

        [Test]
        public void Description_ShouldReturnTranslatedText_WhenTranslationExists()
        {
            // Arrange
            _translator.Initialize(new List<string> { _spanishFilePath }, "Spanish");
            var attribute = new LocalizedDescriptionAttribute("-1286148484", "Temporal");

            // Act
            var result = attribute.Description;

            // Assert
            Assert.AreEqual("Temporal_es", result, "Translation mismatch for 'Temporal' in Spanish.");
        }

        [Test]
        public void Description_ShouldFallbackToEnglish_WhenTranslationNotAvailable()
        {
            // Arrange
            _translator.Initialize(new List<string> { _spanishFilePath }, "Spanish");
            var attribute = new LocalizedDescriptionAttribute("nonExistentId", "Fallback Text");

            // Act
            var result = attribute.Description;

            // Assert
            Assert.AreEqual("Fallback Text", result, "Should fallback to English text when translation not available.");
        }

        [Test]
        public void Description_ShouldCacheTranslatedValue()
        {
            // Arrange
            _translator.Initialize(new List<string> { _spanishFilePath }, "Spanish");
            var attribute = new LocalizedDescriptionAttribute("-1286148484", "Temporal");

            // Act - First call should translate
            var firstResult = attribute.Description;
            
            // Change language to force different translation
            _translator.CurrentUserLanguage = "Romanian";
            
            // Second call should return cached value
            var secondResult = attribute.Description;

            // Assert
            Assert.AreEqual("Temporal_es", firstResult, "First call should return Spanish translation.");
            Assert.AreEqual("Temporal_es", secondResult, "Second call should return cached Spanish translation.");
        }

        [Test]
        public void Description_ShouldUpdateTranslation_WhenLanguageChanges()
        {
            // Arrange
            _translator.Initialize(new List<string> { _spanishFilePath, _romanianFilePath });
            var attribute = new LocalizedDescriptionAttribute("-1286148484", "Temporal");

            // Act - First call in Spanish
            _translator.CurrentUserLanguage = "Spanish";
            var spanishResult = attribute.Description;

            // Change language to Romanian
            _translator.CurrentUserLanguage = "Romanian";
            var romanianResult = attribute.Description;

            // Assert
            Assert.AreEqual("Temporal_es", spanishResult, "Should return Spanish translation.");
            Assert.AreEqual("Temporal_es", romanianResult, "Should return Romanian translation (which happens to be the same as Spanish in this case).");
        }

        [Test]
        public void Description_ShouldHandleEmptyOrNullTranslations()
        {
            // Arrange
            _translator.Initialize(new List<string> { _spanishFilePath }, "Spanish");
            var attribute = new LocalizedDescriptionAttribute("emptyTranslationId", "Default Text");

            // Act
            var result = attribute.Description;

            // Assert
            Assert.AreEqual("Default Text", result, "Should fallback to English text when translation is empty or null.");
        }

        [Test]
        public void Description_ShouldHandleMultipleAttributesWithSameTextId()
        {
            // Arrange
            _translator.Initialize(new List<string> { _spanishFilePath }, "Spanish");
            var attribute1 = new LocalizedDescriptionAttribute("-1286148484", "Temporal");
            var attribute2 = new LocalizedDescriptionAttribute("-1286148484", "Temporal");

            // Act
            var result1 = attribute1.Description;
            var result2 = attribute2.Description;

            // Assert
            Assert.AreEqual("Temporal_es", result1, "First attribute should return correct translation.");
            Assert.AreEqual("Temporal_es", result2, "Second attribute should return correct translation.");
        }
    }
} 