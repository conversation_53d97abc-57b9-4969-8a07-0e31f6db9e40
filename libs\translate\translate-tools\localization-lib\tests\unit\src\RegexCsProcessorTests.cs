/*
 * Copyright 2021-present Alcon. All rights reserved.
 * Alcon proprietary/confidential.
 * Use is subject to license terms.
 */

using NUnit.Framework;
using System;
using System.IO;

namespace LocalizationLib.Tests;

[TestFixture]
public class RegexCsProcessorTests
{
    private RegexCsProcessor _processor;
    private string _tempFolder;

    [SetUp]
    public void Setup()
    {
        _processor = new RegexCsProcessor();
        // Create a unique temp folder for these tests
        _tempFolder = Path.Combine(Path.GetTempPath(), "RegexCsProcessorTests_" + Guid.NewGuid());
        Directory.CreateDirectory(_tempFolder);
    }

    [TearDown]
    public void TearDown()
    {
        if (Directory.Exists(_tempFolder))
        {
            Directory.Delete(_tempFolder, true);
        }
    }

    [Test]
    public void ProcessCsFile_ShouldReportButNotModifyFile()
    {
        // Arrange: write a test .cs with a single string literal
        string csPath = Path.Combine(_tempFolder, "TestFile.cs");
        string originalCode = @"using System;
namespace Demo
{
    public class TestFile
    {
        public void ShowMessage()
        {
            Console.WriteLine(""Hello World"");
        }
    }
}";
        File.WriteAllText(csPath, originalCode);

        // Act
        var result = _processor.ProcessCsFile(csPath, groupId: "Demo.TestFile");

        // Assert: 
        // 1) The file is unchanged
        string postCode = File.ReadAllText(csPath);
        Assert.AreEqual(originalCode, postCode,
            "File should not be modified.");

        // 2) A ReplacementRecord is found
        Assert.AreEqual(1, result.Replacements.Count,
            "Should find 1 string literal for 'Hello World'.");
        Assert.AreEqual("Hello World", result.Replacements[0].OriginalValue);

        // 3) The global AllReplacementRecords also has 1
        Assert.AreEqual(1, _processor.AllReplacementRecords.Count);
    }

    [Test]
    public void ProcessCsFile_ShouldSkipShortOrBracketOnlyStrings()
    {
        // Arrange
        string csPath = Path.Combine(_tempFolder, "TestFile.cs");
        string originalCode = @"namespace Demo
{
    public class TestFile
    {
        public void Something()
        {
            string brackets = ""[]"";
            string empty = """";
            string word = ""Hello"";
        }
    }
}";
        File.WriteAllText(csPath, originalCode);

        // Act
        var result = _processor.ProcessCsFile(csPath, groupId: "Demo.TestFile");

        // Assert
        // 1) Only "Hello" is found
        Assert.AreEqual(1, result.Replacements.Count,
            "Only 'Hello' should be found, skipping '[]' and ''.");
        Assert.AreEqual("Hello", result.Replacements[0].OriginalValue);

        // 2) The file is unchanged
        string updatedCode = File.ReadAllText(csPath);
        Assert.AreEqual(originalCode, updatedCode);
    }

    [Test]
    public void ProcessCsFile_NoStringLiterals_ShouldDoNothing()
    {
        // Arrange
        string csPath = Path.Combine(_tempFolder, "EmptyFile.cs");
        string originalCode = @"namespace Demo
{
    public class EmptyFile
    {
        public void NoStringsHere()
        {
            // no string literals
        }
    }
}";
        File.WriteAllText(csPath, originalCode);

        // Act
        var result = _processor.ProcessCsFile(csPath, groupId: "Demo.EmptyFile");

        // Assert
        Assert.IsFalse(result.Changed, "No string to replace => no changes.");
        Assert.AreEqual(0, result.Replacements.Count, "No replacements.");
        Assert.AreEqual(originalCode, File.ReadAllText(csPath),
            "File remains unchanged.");
    }

    [Test]
    public void ProcessCsFile_WithSkipLogic_ShouldSkipNotifyCalls()
    {
        // This test demonstrates naive snippet skipping for e.g. "Notify(" or "NotifyPropertyChanged(".
        string csPath = Path.Combine(_tempFolder, "NotifyFile.cs");
        string originalCode = @"
namespace Demo
{
    public class NotifyFile
    {
        public void SomeMethod()
        {
            Notify(""ShouldSkipThis"");
            NotifyPropertyChanged(""ShouldSkipThisToo"");
            Console.WriteLine(""ShouldTranslateThis"");
        }
    }
}";
        File.WriteAllText(csPath, originalCode);

        // Act
        var result = _processor.ProcessCsFile(csPath, groupId: "Demo.NotifyFile");

        // Assert
        // Expect only the "ShouldTranslateThis" to be found, skipping the two "Notify" calls
        Assert.IsFalse(result.Changed);
        Assert.AreEqual(1, result.Replacements.Count);
        Assert.AreEqual("ShouldTranslateThis", result.Replacements[0].OriginalValue);

        // Check file is unchanged
        string updatedCode = File.ReadAllText(csPath);
        Assert.AreEqual(originalCode, updatedCode);
    }
}
