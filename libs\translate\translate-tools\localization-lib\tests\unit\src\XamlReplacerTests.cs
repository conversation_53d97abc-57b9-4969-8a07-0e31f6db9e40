/*
 * Copyright 2021-present Alcon. All rights reserved.
 * Alcon proprietary/confidential.
 * Use is subject to license terms.
 */

using NUnit.Framework;
using System;
using System.IO;

namespace LocalizationLib.Tests;

[TestFixture]
public class XamlReplacerTest
{
    private string _tempFolder;
    private string _projectFolder;
    private string _xamlFileContent =
@"<UserControl x:Class=""Test.XamlReplacer.XamlTestFile""
xmlns=""http://schemas.microsoft.com/winfx/2006/xaml/presentation""
xmlns:x=""http://schemas.microsoft.com/winfx/2006/xaml""
Title=""Sample"">
    <Grid>
        <Button Content=""Cancel"" />
        <TextBlock Text=""Hello World"" />
        <Label ToolTip=""Info"" />
    </Grid>
</UserControl>";
    private string _configFileContent =
@"""LastExtraction"": ""2025-04-07T11:20:38.6076263Z"",
  ""ExtractedTexts"": {
    ""XamlTestFile.xaml:4:6:**********"": {
      ""FilePath"": ""XamlTestFile.xaml"",
      ""LineNumber"": 4,
      ""Column"": 6,
      ""OriginalText"": ""Sample"",
      ""Property"": ""Title"",
      ""GroupId"": ""Test.XamlReplacer"",
      ""TextHash"": ""**********"",
      ""Source"": 0,
      ""LastUpdated"": ""2025-04-07T11:20:38.6037644Z"",
      ""NeedsTranslation"": true,
      ""Status"": 1,
      ""LangTextID"": ""-1548743804""
    },
    ""XamlTestFile.xaml:6:25:*********"": {
      ""FilePath"": ""XamlTestFile.xaml"",
      ""LineNumber"": 6,
      ""Column"": 25,
      ""OriginalText"": ""Cancel"",
      ""Property"": ""Button"",
      ""GroupId"": ""Test.XamlReplacer"",
      ""TextHash"": ""*********"",
      ""Source"": 1,
      ""LastUpdated"": ""2025-04-07T11:20:38.6040413Z"",
      ""NeedsTranslation"": true,
      ""Status"": 1,
      ""LangTextID"": ""-2006793132""
    },
     ""XamlTestFile.xaml:7:25:2106357261"": {
      ""FilePath"": ""XamlTestFile.xaml"",
      ""LineNumber"": 7,
      ""Column"": 25,
      ""OriginalText"": ""Hello World"",
      ""Property"": ""TextBlock"",
      ""GroupId"": ""Test.XamlReplacer"",
      ""TextHash"": ""2106357261"",
      ""Source"": 1,
      ""LastUpdated"": ""2025-04-07T11:20:38.6040533Z"",
      ""NeedsTranslation"": true,
      ""Status"": 0,
      ""LangTextID"": ""-*********""
    },
    ""XamlTestFile.xaml:8:25:23524709"": {
      ""FilePath"": ""XamlTestFile.xaml"",
      ""LineNumber"": 8,
      ""Column"": 25,
      ""OriginalText"": ""Info"",
      ""Property"": ""Label"",
      ""GroupId"": ""Test.XamlReplacer"",
      ""TextHash"": ""23524709"",
      ""Source"": 1,
      ""LastUpdated"": ""2025-04-07T11:20:38.6040653Z"",
      ""NeedsTranslation"": true,
      ""Status"": 0,
      ""LangTextID"": ""-216422136""
    }
}";

    [SetUp]
    public void SetUp()
    {
        // Reset the config manager before each test
        ProjectTextExtractorConfigManager.Reset();

        // Create a unique temporary folder for the tests.
        _tempFolder = Path.Combine(Path.GetTempPath(), "XamlReplacerTests_" + Guid.NewGuid());
        Directory.CreateDirectory(_tempFolder);

        // Create subfolder for the "project"
        _projectFolder = Path.Combine(_tempFolder, "Project");
        Directory.CreateDirectory(_projectFolder);
    }

    [TearDown]
    public void TearDown()
    {
        // Clean up the temporary folder after tests.
        if (Directory.Exists(_tempFolder))
        {
            Directory.Delete(_tempFolder, true);
        }
    }

    [Test]
    public void Run_Replace_SuccessOnApprovedStatus()
    {
        string xamlFile = Path.Combine(_projectFolder, "XamlTestFile.xaml");
        File.WriteAllText(xamlFile, _xamlFileContent);

        string configFile = Path.Combine(_projectFolder, "ProjectTextExtractorConfig.json");
        string configFileContent = $"{{\"ProjectPath\": \"{_projectFolder.Replace("\\", "\\\\")}\"," + _configFileContent + "}";
        File.WriteAllText(configFile, configFileContent);

        var aggregator = new ProjectLocalizationReplacer();
        aggregator.Run(_projectFolder, "xaml");

        // Assert updated file content contains the expected binding syntax.
        string updatedContent = File.ReadAllText(xamlFile);
        StringAssert.Contains("Binding Converter={StaticResource TranslateConverter}", updatedContent,
            "XAML file should be updated with binding syntax.");
        StringAssert.Contains(@"xmlns:trConv=""clr-namespace:Alcon.Interop.Translate;assembly=translate_csharp""", updatedContent,
            "XAML file should contain translate_csharp assemply and Alcon.Interop.Translate namespace");

        // assert TranslateConverter binding has been added for Approved strings from the config file
        StringAssert.Contains("Title=\"{Binding Converter={StaticResource TranslateConverter}, ConverterParameter='id:-1548743804|en:Sample'}\"", updatedContent,
            "Binding converter has been added for \"Sample\" string");
        StringAssert.Contains("Button Content=\"{Binding Converter={StaticResource TranslateConverter}, ConverterParameter='id:-2006793132|en:Cancel'}\"", updatedContent,
            "Binding converter has been added for \"Cancel\" string");
        StringAssert.DoesNotContain("TextBlock Text=\"{Binding Converter={StaticResource TranslateConverter}, ConverterParameter='id:-*********|en:Hello World'}\"", updatedContent,
            "Binding converter hasn't been added for \"Hello World\" string because status is Open, not Approved");
        StringAssert.DoesNotContain("Label ToolTip=\"{Binding Converter={StaticResource TranslateConverter}, ConverterParameter='id:-216422136|en:Info'}\"", updatedContent,
            "Binding converter hasn't been added for \"Info\" string because status is Open not Approved");
        StringAssert.Contains("TextBlock Text=\"Hello World\"", updatedContent, "The original content for TextBlock isn't changed");
        StringAssert.Contains("Label ToolTip=\"Info\"", updatedContent, "The original content for Label isn't changed");

        var config = ProjectTextExtractorConfigManager.Instance;

        // assert status changed to resolved for approved one, for open status it remains unchanged
        Assert.IsTrue(config.GetExtractedTextStatus("XamlTestFile.xaml:4:6:**********") == TextStatus.Resolved);
        Assert.IsTrue(config.GetExtractedTextStatus("XamlTestFile.xaml:6:25:*********") == TextStatus.Resolved);
        Assert.IsTrue(config.GetExtractedTextStatus("XamlTestFile.xaml:7:25:2106357261") == TextStatus.Open);
        Assert.IsTrue(config.GetExtractedTextStatus("XamlTestFile.xaml:8:25:23524709") == TextStatus.Open);

        // assert updated lineNumber/column
        Assert.IsTrue(config.GetExtractedText("XamlTestFile.xaml:4:6:**********").UpdatedLineNumber == 0); // should remain unchanged
        Assert.IsTrue(config.GetExtractedText("XamlTestFile.xaml:4:6:**********").UpdatedColumn == 102);
        Assert.IsTrue(config.GetExtractedText("XamlTestFile.xaml:6:25:*********").UpdatedLineNumber == 10);
        Assert.IsTrue(config.GetExtractedText("XamlTestFile.xaml:6:25:*********").UpdatedColumn == 120);
        Assert.IsTrue(config.GetExtractedText("XamlTestFile.xaml:7:25:2106357261").UpdatedLineNumber == 0);
        Assert.IsTrue(config.GetExtractedText("XamlTestFile.xaml:7:25:2106357261").UpdatedColumn == 0);
        Assert.IsTrue(config.GetExtractedText("XamlTestFile.xaml:8:25:23524709").UpdatedLineNumber == 0);
        Assert.IsTrue(config.GetExtractedText("XamlTestFile.xaml:8:25:23524709").UpdatedColumn == 0);
    }

    [Test]
    public void Run_Replace_TranslateConverterAlreadyAdded()
    {
        string xamlFile = Path.Combine(_projectFolder, "XamlTestFile.xaml");
        string xamlFileContent = @"<UserControl x:Class=""Test.XamlReplacer.XamlTestFile""
xmlns=""http://schemas.microsoft.com/winfx/2006/xaml/presentation""
xmlns:x=""http://schemas.microsoft.com/winfx/2006/xaml""
xmlns:trConv=""clr-namespace:Alcon.Interop.Translate;assembly=translate_csharp""
Title=""Sample"">
    <UserControl.Resources>
        <trConv:TranslateConverter x:Key=""TranslateConverter""/>
    </UserControl.Resources>
    <Grid>
        <Button Content=""{Binding Converter={StaticResource TranslateConverter}, ConverterParameter='id:-2006793132|en:Cancel'}"" />
        <TextBlock Text=""Hello World"" />
        <Label ToolTip=""Info"" />
    </Grid>
</UserControl>";
        File.WriteAllText(xamlFile, xamlFileContent);


        string configFile = Path.Combine(_projectFolder, "ProjectTextExtractorConfig.json");
        string configFileContent = $"{{\"ProjectPath\": \"{_projectFolder.Replace("\\", "\\\\")}\"," +
@"""LastExtraction"": ""2025-04-07T11:20:38.6076263Z"",
  ""ExtractedTexts"": {
    ""XamlTestFile.xaml:5:6:**********"": {
      ""FilePath"": ""XamlTestFile.xaml"",
      ""LineNumber"": 5,
      ""Column"": 6,
      ""OriginalText"": ""Sample"",
      ""Property"": ""Title"",
      ""GroupId"": ""Test.XamlReplacer"",
      ""TextHash"": ""**********"",
      ""Source"": 0,
      ""LastUpdated"": ""2025-04-07T11:20:38.6037644Z"",
      ""NeedsTranslation"": true,
      ""Status"": 1,
      ""LangTextID"": ""-1548743804""
    },
    ""XamlTestFile.xaml:10:25:*********"": {
      ""FilePath"": ""XamlTestFile.xaml"",
      ""LineNumber"": 10,
      ""Column"": 25,
      ""OriginalText"": ""Cancel"",
      ""Property"": ""Button"",
      ""GroupId"": ""Test.XamlReplacer"",
      ""TextHash"": ""*********"",
      ""Source"": 1,
      ""LastUpdated"": ""2025-04-07T11:20:38.6040413Z"",
      ""NeedsTranslation"": true,
      ""Status"": 1,
      ""LangTextID"": ""-2006793132""
    },
}
}";
        File.WriteAllText(configFile, configFileContent);

        var aggregator = new ProjectLocalizationReplacer();
        aggregator.Run(_projectFolder, "xaml");

        string updatedContent = File.ReadAllText(xamlFile);

        // Assert updated file content contains the namespace and assembly
        StringAssert.Contains("xmlns:trConv=\"clr-namespace:Alcon.Interop.Translate;assembly=translate_csharp\"", updatedContent,
            "XAML file should contain Alcon.Interop.Translate and translate_csharp assembly");

        // Assert updated file content contains the resources updated.
        StringAssert.Contains("<trConv:TranslateConverter x:Key=\"TranslateConverter\"/>", updatedContent,
            "XAML file should be updated with resources block syntax.");

        // Assert TranslateConverter binding has been added once for Approved strings from the config file
        StringAssert.Contains("Title=\"{Binding Converter={StaticResource TranslateConverter}, ConverterParameter='id:-1548743804|en:Sample'}\"", updatedContent,
            "Binding converter has been added for \"Sample\" string");
        StringAssert.Contains("Button Content=\"{Binding Converter={StaticResource TranslateConverter}, ConverterParameter='id:-2006793132|en:Cancel'}\"", updatedContent,
            "Binding converter has been added for \"Cancel\" string");

        var config = ProjectTextExtractorConfigManager.Instance;

        // Assert status changed to resolved for approved one, for open status it remains unchanged
        Assert.IsTrue(config.GetExtractedTextStatus("XamlTestFile.xaml:5:6:**********") == TextStatus.Resolved);
        Assert.IsTrue(config.GetExtractedTextStatus("XamlTestFile.xaml:10:25:*********") == TextStatus.Resolved);

        // Assert updated lineNumber/column
        Assert.IsTrue(config.GetExtractedText("XamlTestFile.xaml:5:6:**********").UpdatedLineNumber == 0); // should remain unchanged
        Assert.IsTrue(config.GetExtractedText("XamlTestFile.xaml:5:6:**********").UpdatedColumn == 102);
        Assert.IsTrue(config.GetExtractedText("XamlTestFile.xaml:10:25:*********").UpdatedLineNumber == 0);
        Assert.IsTrue(config.GetExtractedText("XamlTestFile.xaml:10:25:*********").UpdatedColumn == 0);
    }

    [Test]
    public void Run_Replace_SpecialCases_Success()
    {
        string xamlFile = Path.Combine(_projectFolder, "XamlTestFile.xaml");
        string xamlFileContent = @"<UserControl x:Class=""Test.XamlReplacer.XamlTestFile""
xmlns=""http://schemas.microsoft.com/winfx/2006/xaml/presentation""
xmlns:x=""http://schemas.microsoft.com/winfx/2006/xaml""
Title=""Sample"">
    <Grid>
        <Button Content=""Surgeon's patients"" />
        <TextBlock Text=""Hello World\n Nice to have you here"" />
        <Label ToolTip=""Info"" />
    </Grid>
</UserControl>";
        File.WriteAllText(xamlFile, xamlFileContent);


        string configFile = Path.Combine(_projectFolder, "ProjectTextExtractorConfig.json");
        string configFileContent = $"{{\"ProjectPath\": \"{_projectFolder.Replace("\\", "\\\\")}\"," +
@"""LastExtraction"": ""2025-04-07T11:20:38.6076263Z"",
  ""ExtractedTexts"": {
    ""XamlTestFile.xaml:6:6:-**********"": {
      ""FilePath"": ""XamlTestFile.xaml"",
      ""LineNumber"": 6,
      ""Column"": 6,
      ""OriginalText"": ""Surgeon's patients"",
      ""Property"": ""Title"",
      ""GroupId"": ""Test.XamlReplacer"",
      ""TextHash"": ""-**********"",
      ""Source"": 0,
      ""LastUpdated"": ""2025-04-07T11:20:38.6037644Z"",
      ""NeedsTranslation"": true,
      ""Status"": 1,
      ""LangTextID"": ""**********""
    },
    ""XamlTestFile.xaml:7:25:-*********"": {
      ""FilePath"": ""XamlTestFile.xaml"",
      ""LineNumber"": 7,
      ""Column"": 25,
      ""OriginalText"": ""Hello World\n Nice to have you here"",
      ""Property"": ""TextBlock"",
      ""GroupId"": ""Test.XamlReplacer"",
      ""TextHash"": ""-*********"",
      ""Source"": 1,
      ""LastUpdated"": ""2025-04-07T11:20:38.6040413Z"",
      ""NeedsTranslation"": true,
      ""Status"": 1,
      ""LangTextID"": ""**********""
    },
}
}";
        File.WriteAllText(configFile, configFileContent);
        var aggregator = new ProjectLocalizationReplacer();
        aggregator.Run(_projectFolder, "xaml");

        string updatedContent = File.ReadAllText(xamlFile);

        // Assert updated file content contains the namespace and assembly
        StringAssert.Contains("xmlns:trConv=\"clr-namespace:Alcon.Interop.Translate;assembly=translate_csharp\"", updatedContent,
            "XAML file should contain Alcon.Interop.Translate and translate_csharp assembly");

        // Assert updated file content contains the resources updated.
        StringAssert.Contains("<trConv:TranslateConverter x:Key=\"TranslateConverter\"/>", updatedContent,
            "XAML file should be updated with resources block syntax.");

        // Assert TranslateConverter binding has been added for Approved strings from the config file
        StringAssert.Contains("Button Content=\"{Binding Converter={StaticResource TranslateConverter}, ConverterParameter='id:**********|en:Surgeon\\&apos;s patients'}\"", updatedContent,
            "Binding converter has been added for \"Surgeon's patients\" string");
        StringAssert.Contains("TextBlock Text=\"{Binding Converter={StaticResource TranslateConverter}, ConverterParameter='id:**********|en:Hello World\\n Nice to have you here'}\"", updatedContent,
            "Binding converter has been added for \"Hello World\\n Nice to have you here\" string");

        var config = ProjectTextExtractorConfigManager.Instance;

        // Assert status changed to resolved
        Assert.IsTrue(config.GetExtractedTextStatus("XamlTestFile.xaml:6:6:-**********") == TextStatus.Resolved);
        Assert.IsTrue(config.GetExtractedTextStatus("XamlTestFile.xaml:7:25:-*********") == TextStatus.Resolved);

        // Assert updated lineNumber/column
        Assert.IsTrue(config.GetExtractedText("XamlTestFile.xaml:6:6:-**********").UpdatedLineNumber == 10);
        Assert.IsTrue(config.GetExtractedText("XamlTestFile.xaml:6:6:-**********").UpdatedColumn == 119);
        Assert.IsTrue(config.GetExtractedText("XamlTestFile.xaml:7:25:-*********").UpdatedLineNumber == 11);
        Assert.IsTrue(config.GetExtractedText("XamlTestFile.xaml:7:25:-*********").UpdatedColumn == 119);
    }

    [Test]
    public void Run_Replace_LineColumnNotMatching()
    {
        string xamlFile = Path.Combine(_projectFolder, "XamlTestFile.xaml");
        string xamlFileContent = @"<UserControl x:Class=""Test.XamlReplacer.XamlTestFile""
xmlns=""http://schemas.microsoft.com/winfx/2006/xaml/presentation""
xmlns:x=""http://schemas.microsoft.com/winfx/2006/xaml""
Title=""Sample"">
    <Grid>
        <Button Content=""Cancel"" />
        <TextBlock Text=""Hello World"" />
        <Label ToolTip=""Info"" />
    </Grid>
</UserControl>";
        File.WriteAllText(xamlFile, xamlFileContent);

        string configFile = Path.Combine(_projectFolder, "ProjectTextExtractorConfig.json");
        string configFileContent = $"{{\"ProjectPath\": \"{_projectFolder.Replace("\\", "\\\\")}\"," +
@"""LastExtraction"": ""2025-04-07T11:20:38.6076263Z"",
  ""ExtractedTexts"": {
    ""XamlTestFile.xaml:5:9:**********"": {
      ""FilePath"": ""XamlTestFile.xaml"",
      ""LineNumber"": 5,
      ""Column"": 9,
      ""OriginalText"": ""Sample"",
      ""Property"": ""Title"",
      ""GroupId"": ""Test.XamlReplacer"",
      ""TextHash"": ""**********"",
      ""Source"": 0,
      ""LastUpdated"": ""2025-04-07T11:20:38.6037644Z"",
      ""NeedsTranslation"": true,
      ""Status"": 1,
      ""LangTextID"": ""-1548743804""
    },
    ""XamlTestFile.xaml:8:25:**********"": {
      ""FilePath"": ""XamlTestFile.xaml"",
      ""LineNumber"": 8,
      ""Column"": 25,
      ""OriginalText"": ""Cancel"",
      ""Property"": ""Button"",
      ""GroupId"": ""Test.XamlReplacer"",
      ""TextHash"": ""**********"",
      ""Source"": 1,
      ""LastUpdated"": ""2025-04-07T11:20:38.6040413Z"",
      ""NeedsTranslation"": true,
      ""Status"": 1,
      ""LangTextID"": ""-2006793132""
    },
}
}";
        File.WriteAllText(configFile, configFileContent);

        var aggregator = new ProjectLocalizationReplacer();
        aggregator.Run(_projectFolder, "xaml");

        string updatedContent = File.ReadAllText(xamlFile);

        // Assert updated file content doesn't contain the namespace and assembly
        StringAssert.DoesNotContain("xmlns:trConv=\"clr-namespace:Alcon.Interop.Translate;assembly=translate_csharp\"", updatedContent,
            "XAML file should contain Alcon.Interop.Translate and translate_csharp assembly");

        // Assert updated file content doesn't contain the resources updated.
        StringAssert.DoesNotContain("<trConv:TranslateConverter x:Key=\"TranslateConverter\"/>", updatedContent,
            "XAML file should be updated with resources block syntax.");

        // Assert TranslateConverter binding hasn't been added for Approved strings
        StringAssert.DoesNotContain("Title=\"{Binding Converter={StaticResource TranslateConverter}, ConverterParameter='id:-1548743804|en:Sample'}\"", updatedContent,
            "Binding converter has been added for \"Sample\" string");
        StringAssert.DoesNotContain("Button Content=\"{Binding Converter={StaticResource TranslateConverter}, ConverterParameter='id:-2006793132|en:Cancel'}\"", updatedContent,
            "Binding converter has been added for \"Cancel\" string");

        var config = ProjectTextExtractorConfigManager.Instance;

        // Assert status remains approved
        Assert.IsTrue(config.GetExtractedTextStatus("XamlTestFile.xaml:5:9:**********") == TextStatus.Approved);
        Assert.IsTrue(config.GetExtractedTextStatus("XamlTestFile.xaml:8:25:**********") == TextStatus.Approved);

        // Assert additional notes added
        Assert.IsTrue(config.GetExtractedText("XamlTestFile.xaml:5:9:**********").Notes.Contains("Replacer: String \"Sample\" wasn't found on line number \"5\"."));
        Assert.IsTrue(config.GetExtractedText("XamlTestFile.xaml:8:25:**********").Notes.Contains("Replacer: String \"Cancel\" wasn't found on line number \"8\"."));
    }

    [Test]
    public void Run_Replace_ResourcesBlockExisting()
    {
        string xamlFile = Path.Combine(_projectFolder, "XamlTestFile.xaml");
        string xamlFileContent = @"<UserControl x:Class=""Test.XamlReplacer.XamlTestFile""
xmlns=""http://schemas.microsoft.com/winfx/2006/xaml/presentation""
xmlns:x=""http://schemas.microsoft.com/winfx/2006/xaml""
Title=""Sample"">
    <UserControl.Resources>
        <sys:String x:Key=""CancelText"">Cancel</sys:String>
    </UserControl.Resources>
    <Grid>
        <Button Content=""{StaticContentResource CancelText}"" />
        <TextBlock Text=""Hello World"" />
    </Grid>
</UserControl>""";
        File.WriteAllText(xamlFile, xamlFileContent);

        string configFile = Path.Combine(_projectFolder, "ProjectTextExtractorConfig.json");
        string configFileContent = $"{{\"ProjectPath\": \"{_projectFolder.Replace("\\", "\\\\")}\"," +
@"""LastExtraction"": ""2025-04-07T11:20:38.6076263Z"",
  ""ExtractedTexts"": {
    ""XamlTestFile.xaml:4:6:**********"": {
      ""FilePath"": ""XamlTestFile.xaml"",
      ""LineNumber"": 4,
      ""Column"": 6,
      ""OriginalText"": ""Sample"",
      ""Property"": ""Title"",
      ""GroupId"": ""Test.XamlReplacer"",
      ""TextHash"": ""**********"",
      ""Source"": 0,
      ""LastUpdated"": ""2025-04-07T11:20:38.6037644Z"",
      ""NeedsTranslation"": true,
      ""Status"": 1,
      ""LangTextID"": ""-1548743804""
    },
     ""XamlTestFile.xaml:10:25:2106357261"": {
      ""FilePath"": ""XamlTestFile.xaml"",
      ""LineNumber"": 10,
      ""Column"": 25,
      ""OriginalText"": ""Hello World"",
      ""Property"": ""TextBlock"",
      ""GroupId"": ""Test.XamlReplacer"",
      ""TextHash"": ""2106357261"",
      ""Source"": 1,
      ""LastUpdated"": ""2025-04-07T11:20:38.6040533Z"",
      ""NeedsTranslation"": true,
      ""Status"": 1,
      ""LangTextID"": ""-*********""
    }
}
}";
        File.WriteAllText(configFile, configFileContent);

        var aggregator = new ProjectLocalizationReplacer();
        aggregator.Run(_projectFolder, "xaml");

        string updatedContent = File.ReadAllText(xamlFile);

        // Assert updated file content contains the namespace and assembly
        StringAssert.Contains("xmlns:trConv=\"clr-namespace:Alcon.Interop.Translate;assembly=translate_csharp\"", updatedContent,
            "XAML file should contain Alcon.Interop.Translate and translate_csharp assembly");

        // Assert updated file content contains the resources updated.
        StringAssert.Contains("<trConv:TranslateConverter x:Key=\"TranslateConverter\"/>", updatedContent,
            "XAML file should be updated with resources block syntax.");

        // Assert TranslateConverter binding has been added for Approved strings
        StringAssert.Contains("Title=\"{Binding Converter={StaticResource TranslateConverter}, ConverterParameter='id:-1548743804|en:Sample'}\"", updatedContent,
            "Binding converter has been added for \"Sample\" string");
        StringAssert.Contains("TextBlock Text=\"{Binding Converter={StaticResource TranslateConverter}, ConverterParameter='id:-*********|en:Hello World'}\"", updatedContent,
            "Binding converter has been added for \"Hello World\" string");

        var config = ProjectTextExtractorConfigManager.Instance;

        // Assert status changed to resolved
        Assert.IsTrue(config.GetExtractedTextStatus("XamlTestFile.xaml:4:6:**********") == TextStatus.Resolved);
        Assert.IsTrue(config.GetExtractedTextStatus("XamlTestFile.xaml:10:25:2106357261") == TextStatus.Resolved);

        // Assert updated lineNumber/column
        Assert.IsTrue(config.GetExtractedText("XamlTestFile.xaml:4:6:**********").UpdatedLineNumber == 0);
        Assert.IsTrue(config.GetExtractedText("XamlTestFile.xaml:4:6:**********").UpdatedColumn == 102);
        Assert.IsTrue(config.GetExtractedText("XamlTestFile.xaml:10:25:2106357261").UpdatedLineNumber == 12);
        Assert.IsTrue(config.GetExtractedText("XamlTestFile.xaml:10:25:2106357261").UpdatedColumn == 119);

    }

    [Test]
    public void Run_Replace_ResourceDictionary()
    {
        string xamlFile = Path.Combine(_projectFolder, "XamlTestFile.xaml");
        string xamlFileContent =
@"<ResourceDictionary xmlns=""http://schemas.microsoft.com/winfx/2006/xaml/presentation""
                      xmlns:x=""http://schemas.microsoft.com/winfx/2006/xaml"">
    <TextBlock x:Key=""InfoTextBlock""
               Text=""Hello World""
               Foreground=""DarkSlateGray""
               FontWeight=""Bold""
               FontSize=""14""
               TextWrapping=""Wrap"" />
</ResourceDictionary>""";
        File.WriteAllText(xamlFile, xamlFileContent);

        string configFile = Path.Combine(_projectFolder, "ProjectTextExtractorConfig.json");
        string configFileContent = $"{{\"ProjectPath\": \"{_projectFolder.Replace("\\", "\\\\")}\"," +
@"""LastExtraction"": ""2025-04-07T11:20:38.6076263Z"",
  ""ExtractedTexts"": {
     ""XamlTestFile.xaml:4:22:2106357261"": {
      ""FilePath"": ""XamlTestFile.xaml"",
      ""LineNumber"": 4,
      ""Column"": 22,
      ""OriginalText"": ""Hello World"",
      ""Property"": ""Text"",
      ""GroupId"": ""Test.XamlReplacer"",
      ""TextHash"": ""2106357261"",
      ""Source"": 1,
      ""LastUpdated"": ""2025-04-07T11:20:38.6040533Z"",
      ""NeedsTranslation"": true,
      ""Status"": 1,
      ""LangTextID"": ""-*********""
    }
}
}";
        File.WriteAllText(configFile, configFileContent);

        var aggregator = new ProjectLocalizationReplacer();
        aggregator.Run(_projectFolder, "xaml");

        string updatedContent = File.ReadAllText(xamlFile);

        // Assert updated file content contains the namespace and assembly
        StringAssert.Contains("xmlns:trConv=\"clr-namespace:Alcon.Interop.Translate;assembly=translate_csharp\"", updatedContent,
            "XAML file should contain Alcon.Interop.Translate and translate_csharp assembly");

        // Assert updated file content contains the resources updated.
        StringAssert.Contains("<trConv:TranslateConverter x:Key=\"TranslateConverter\"/>", updatedContent,
            "XAML file should be updated with resources block syntax.");

        // Assert resources block hasn't been added
        StringAssert.DoesNotContain("<ResourceDictionary.Resources>", updatedContent, "Resources not added for ResourceDictionary");

        // Assert TranslateConverter binding has been added for Approved strings
        StringAssert.Contains("Text=\"{Binding Converter={StaticResource TranslateConverter}, ConverterParameter='id:-*********|en:Hello World'}\"", updatedContent,
            "Binding converter has been added for \"Hello World\" string");

        var config = ProjectTextExtractorConfigManager.Instance;

        // Assert status changed to ressolved
        Assert.IsTrue(config.GetExtractedTextStatus("XamlTestFile.xaml:4:22:2106357261") == TextStatus.Resolved);

        // Assert updated lineNumber/column
        Assert.IsTrue(config.GetExtractedText("XamlTestFile.xaml:4:22:2106357261").UpdatedLineNumber == 6);
        Assert.IsTrue(config.GetExtractedText("XamlTestFile.xaml:4:22:2106357261").UpdatedColumn == 115);
    }

    [Test]
    public void Run_Replace_TranslateConverterAlreadyAddedWithoutID()
    {
        string xamlFile = Path.Combine(_projectFolder, "XamlTestFile.xaml");
        string xamlFileContent = @"<UserControl x:Class=""Test.XamlReplacer.XamlTestFile""
xmlns=""http://schemas.microsoft.com/winfx/2006/xaml/presentation""
xmlns:x=""http://schemas.microsoft.com/winfx/2006/xaml""
xmlns:trConv=""clr-namespace:Alcon.Interop.Translate;assembly=translate_csharp""
Title=""Sample"">
    <UserControl.Resources>
        <trConv:TranslateConverter x:Key=""TranslateConverter""/>
    </UserControl.Resources>
    <Grid>
        <Button Content=""{Binding Converter={StaticResource TranslateConverter}, ConverterParameter='en:Cancel'}"" />
        <TextBlock Text=""Hello World"" />
        <Label ToolTip=""Info"" />
    </Grid>
</UserControl>";
        File.WriteAllText(xamlFile, xamlFileContent);


        string configFile = Path.Combine(_projectFolder, "ProjectTextExtractorConfig.json");
        string configFileContent = $"{{\"ProjectPath\": \"{_projectFolder.Replace("\\", "\\\\")}\"," +
@"""LastExtraction"": ""2025-04-07T11:20:38.6076263Z"",
  ""ExtractedTexts"": {
    ""XamlTestFile.xaml:5:6:**********"": {
      ""FilePath"": ""XamlTestFile.xaml"",
      ""LineNumber"": 5,
      ""Column"": 6,
      ""OriginalText"": ""Sample"",
      ""Property"": ""Title"",
      ""GroupId"": ""Test.XamlReplacer"",
      ""TextHash"": ""**********"",
      ""Source"": 0,
      ""LastUpdated"": ""2025-04-07T11:20:38.6037644Z"",
      ""NeedsTranslation"": true,
      ""Status"": 1,
      ""LangTextID"": ""-1548743804""
    },
    ""XamlTestFile.xaml:10:25:*********"": {
      ""FilePath"": ""XamlTestFile.xaml"",
      ""LineNumber"": 10,
      ""Column"": 25,
      ""OriginalText"": ""Cancel"",
      ""Property"": ""Button"",
      ""GroupId"": ""Test.XamlReplacer"",
      ""TextHash"": ""*********"",
      ""Source"": 1,
      ""LastUpdated"": ""2025-04-07T11:20:38.6040413Z"",
      ""NeedsTranslation"": true,
      ""Status"": 1,
      ""LangTextID"": ""-2006793132""
    },
}
}";
        File.WriteAllText(configFile, configFileContent);

        var aggregator = new ProjectLocalizationReplacer();
        aggregator.Run(_projectFolder, "xaml");

        string updatedContent = File.ReadAllText(xamlFile);

        // Assert updated file content contains the namespace and assembly
        StringAssert.Contains("xmlns:trConv=\"clr-namespace:Alcon.Interop.Translate;assembly=translate_csharp\"", updatedContent,
            "XAML file should contain Alcon.Interop.Translate and translate_csharp assembly");

        // Assert updated file content contains the resources updated.
        StringAssert.Contains("<trConv:TranslateConverter x:Key=\"TranslateConverter\"/>", updatedContent,
            "XAML file should be updated with resources block syntax.");

        // Assert TranslateConverter binding has been added once for Approved strings from the config file
        StringAssert.Contains("Title=\"{Binding Converter={StaticResource TranslateConverter}, ConverterParameter='id:-1548743804|en:Sample'}\"", updatedContent,
            "Binding converter has been added for \"Sample\" string");
        StringAssert.Contains("Button Content=\"{Binding Converter={StaticResource TranslateConverter}, ConverterParameter='en:Cancel'}\"", updatedContent,
            "Binding converter has been added for \"Cancel\" string");

        var config = ProjectTextExtractorConfigManager.Instance;

        // Assert status changed to resolved for approved one, for open status it remains unchanged
        Assert.IsTrue(config.GetExtractedTextStatus("XamlTestFile.xaml:5:6:**********") == TextStatus.Resolved);
        Assert.IsTrue(config.GetExtractedTextStatus("XamlTestFile.xaml:10:25:*********") == TextStatus.Resolved);

        // Assert updated lineNumber/column
        Assert.IsTrue(config.GetExtractedText("XamlTestFile.xaml:5:6:**********").UpdatedLineNumber == 0); // should remain unchanged
        Assert.IsTrue(config.GetExtractedText("XamlTestFile.xaml:5:6:**********").UpdatedColumn == 102);
        Assert.IsTrue(config.GetExtractedText("XamlTestFile.xaml:10:25:*********").UpdatedLineNumber == 0);
        Assert.IsTrue(config.GetExtractedText("XamlTestFile.xaml:10:25:*********").UpdatedColumn == 0);
    }

    [Test]
    public void Run_Replace_RunTextProperty()
    {
        string xamlFile = Path.Combine(_projectFolder, "XamlTestFile.xaml");
        string xamlFileContent = @"<UserControl x:Class=""Test.XamlReplacer.XamlTestFile""
xmlns=""http://schemas.microsoft.com/winfx/2006/xaml/presentation""
xmlns:x=""http://schemas.microsoft.com/winfx/2006/xaml""
Title=""Sample"">
    <Grid>
        <Button Content=""Cancel"" />
         <TextBlock>
            <Run Text=""Hello""/>
            <Run Text=""World"" FontWeight=""Bold"" />
        </TextBlock>
        <Label ToolTip=""Info"" />
    </Grid>
</UserControl>";
        File.WriteAllText(xamlFile, xamlFileContent);


        string configFile = Path.Combine(_projectFolder, "ProjectTextExtractorConfig.json");
        string configFileContent = $"{{\"ProjectPath\": \"{_projectFolder.Replace("\\", "\\\\")}\"," +
@"""LastExtraction"": ""2025-04-07T11:20:38.6076263Z"",
  ""ExtractedTexts"": {
    ""XamlTestFile.xaml:4:8:**********"": {
      ""FilePath"": ""XamlTestFile.xaml"",
      ""LineNumber"": 4,
      ""Column"": 8,
      ""OriginalText"": ""Sample"",
      ""Property"": ""Title"",
      ""GroupId"": ""Test.XamlReplacer"",
      ""TextHash"": ""**********"",
      ""Source"": 1,
      ""LastUpdated"": ""2025-04-07T11:20:38.6037644Z"",
      ""NeedsTranslation"": true,
      ""Status"": 1,
      ""LangTextID"": ""-1548743804""
    },
    ""XamlTestFile.xaml:6:26:*********"": {
      ""FilePath"": ""XamlTestFile.xaml"",
      ""LineNumber"": 6,
      ""Column"": 26,
      ""OriginalText"": ""Cancel"",
      ""Property"": ""Button"",
      ""GroupId"": ""Test.XamlReplacer"",
      ""TextHash"": ""*********"",
      ""Source"": 1,
      ""LastUpdated"": ""2025-04-07T11:20:38.6040413Z"",
      ""NeedsTranslation"": true,
      ""Status"": 1,
      ""LangTextID"": ""-2006793132""
    },
    ""XamlTestFile.xaml:8:24:728080123"": {
      ""FilePath"": ""XamlTestFile.xaml"",
      ""LineNumber"": 8,
      ""Column"": 24,
      ""OriginalText"": ""Hello"",
      ""Property"": ""Text"",
      ""GroupId"": ""Test.XamlReplacer"",
      ""TextHash"": ""728080123"",
      ""Source"": 1,
      ""LastUpdated"": ""2025-04-07T11:20:38.6040413Z"",
      ""NeedsTranslation"": true,
      ""Status"": 1,
      ""LangTextID"": ""1879662520""
    },
    ""XamlTestFile.xaml:9:24:742236603"": {
      ""FilePath"": ""XamlTestFile.xaml"",
      ""LineNumber"": 9,
      ""Column"": 24,
      ""OriginalText"": ""World"",
      ""Property"": ""Text"",
      ""GroupId"": ""Test.XamlReplacer"",
      ""TextHash"": ""742236603"",
      ""Source"": 1,
      ""LastUpdated"": ""2025-04-07T11:20:38.6040413Z"",
      ""NeedsTranslation"": true,
      ""Status"": 1,
      ""LangTextID"": ""1893819000""
    }
}
}";
        File.WriteAllText(configFile, configFileContent);

        var aggregator = new ProjectLocalizationReplacer();
        aggregator.Run(_projectFolder, "xaml");

        string updatedContent = File.ReadAllText(xamlFile);

        // Assert updated file content contains the namespace and assembly
        StringAssert.Contains("xmlns:trConv=\"clr-namespace:Alcon.Interop.Translate;assembly=translate_csharp\"", updatedContent,
            "XAML file should contain Alcon.Interop.Translate and translate_csharp assembly");

        // Assert updated file content contains the resources updated.
        StringAssert.Contains("<trConv:TranslateConverter x:Key=\"TranslateConverter\"/>", updatedContent,
            "XAML file should be updated with resources block syntax.");

        // Assert TranslateConverter binding has been added once for Approved strings from the config file
        StringAssert.Contains("Title=\"{Binding Converter={StaticResource TranslateConverter}, ConverterParameter='id:-1548743804|en:Sample'}\"", updatedContent,
            "Binding converter has been added for \"Sample\" string");
        StringAssert.Contains("Button Content=\"{Binding Converter={StaticResource TranslateConverter}, ConverterParameter='id:-2006793132|en:Cancel'}\"", updatedContent,
            "Binding converter has been added for \"Cancel\" string");
        StringAssert.Contains("Run Text=\"{Binding Converter={StaticResource TranslateConverter}, ConverterParameter='id:1879662520|en:Hello', Mode=OneWay}\"", updatedContent,
            "Binding converter including OneWay mode has been added for \"Hello\" string");
        StringAssert.Contains("Run Text=\"{Binding Converter={StaticResource TranslateConverter}, ConverterParameter='id:1893819000|en:World', Mode=OneWay}\"", updatedContent,
            "Binding converter including OneWay mode has been added for \"World\" string");

        var config = ProjectTextExtractorConfigManager.Instance;

        // Assert status changed to resolved for approved one, for open status it remains unchanged
        Assert.IsTrue(config.GetExtractedTextStatus("XamlTestFile.xaml:4:8:**********") == TextStatus.Resolved);
        Assert.IsTrue(config.GetExtractedTextStatus("XamlTestFile.xaml:6:26:*********") == TextStatus.Resolved);
        Assert.IsTrue(config.GetExtractedTextStatus("XamlTestFile.xaml:8:24:728080123") == TextStatus.Resolved);
        Assert.IsTrue(config.GetExtractedTextStatus("XamlTestFile.xaml:9:24:742236603") == TextStatus.Resolved);

        // Assert updated lineNumber/column
        Assert.IsTrue(config.GetExtractedText("XamlTestFile.xaml:4:8:**********").UpdatedLineNumber == 0); // should remain unchanged
        Assert.IsTrue(config.GetExtractedText("XamlTestFile.xaml:4:8:**********").UpdatedColumn == 102);
        Assert.IsTrue(config.GetExtractedText("XamlTestFile.xaml:6:26:*********").UpdatedLineNumber == 10);
        Assert.IsTrue(config.GetExtractedText("XamlTestFile.xaml:6:26:*********").UpdatedColumn == 120);
        Assert.IsTrue(config.GetExtractedText("XamlTestFile.xaml:8:24:728080123").UpdatedLineNumber == 12);
        Assert.IsTrue(config.GetExtractedText("XamlTestFile.xaml:8:24:728080123").UpdatedColumn == 117);
        Assert.IsTrue(config.GetExtractedText("XamlTestFile.xaml:9:24:742236603").UpdatedLineNumber == 13);
        Assert.IsTrue(config.GetExtractedText("XamlTestFile.xaml:9:24:742236603").UpdatedColumn == 117);
    }
}