D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-replacer\build\Debug\localization_replacer.exe
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-replacer\build\Debug\localization_replacer.deps.json
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-replacer\build\Debug\localization_replacer.runtimeconfig.json
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-replacer\build\Debug\localization_replacer.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-replacer\build\Debug\localization_replacer.pdb
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-replacer\build\Debug\CommandLine.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-replacer\build\Debug\CommunityToolkit.Mvvm.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-replacer\build\Debug\Microsoft.CodeAnalysis.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-replacer\build\Debug\Microsoft.CodeAnalysis.CSharp.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-replacer\build\Debug\Microsoft.CodeAnalysis.CSharp.Scripting.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-replacer\build\Debug\Microsoft.CodeAnalysis.Scripting.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-replacer\build\Debug\Newtonsoft.Json.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-replacer\build\Debug\cs\Microsoft.CodeAnalysis.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-replacer\build\Debug\de\Microsoft.CodeAnalysis.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-replacer\build\Debug\es\Microsoft.CodeAnalysis.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-replacer\build\Debug\fr\Microsoft.CodeAnalysis.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-replacer\build\Debug\it\Microsoft.CodeAnalysis.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-replacer\build\Debug\ja\Microsoft.CodeAnalysis.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-replacer\build\Debug\ko\Microsoft.CodeAnalysis.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-replacer\build\Debug\pl\Microsoft.CodeAnalysis.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-replacer\build\Debug\pt-BR\Microsoft.CodeAnalysis.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-replacer\build\Debug\ru\Microsoft.CodeAnalysis.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-replacer\build\Debug\tr\Microsoft.CodeAnalysis.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-replacer\build\Debug\zh-Hans\Microsoft.CodeAnalysis.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-replacer\build\Debug\zh-Hant\Microsoft.CodeAnalysis.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-replacer\build\Debug\cs\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-replacer\build\Debug\de\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-replacer\build\Debug\es\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-replacer\build\Debug\fr\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-replacer\build\Debug\it\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-replacer\build\Debug\ja\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-replacer\build\Debug\ko\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-replacer\build\Debug\pl\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-replacer\build\Debug\pt-BR\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-replacer\build\Debug\ru\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-replacer\build\Debug\tr\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-replacer\build\Debug\zh-Hans\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-replacer\build\Debug\zh-Hant\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-replacer\build\Debug\cs\Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-replacer\build\Debug\de\Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-replacer\build\Debug\es\Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-replacer\build\Debug\fr\Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-replacer\build\Debug\it\Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-replacer\build\Debug\ja\Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-replacer\build\Debug\ko\Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-replacer\build\Debug\pl\Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-replacer\build\Debug\pt-BR\Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-replacer\build\Debug\ru\Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-replacer\build\Debug\tr\Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-replacer\build\Debug\zh-Hans\Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-replacer\build\Debug\zh-Hant\Microsoft.CodeAnalysis.CSharp.Scripting.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-replacer\build\Debug\cs\Microsoft.CodeAnalysis.Scripting.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-replacer\build\Debug\de\Microsoft.CodeAnalysis.Scripting.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-replacer\build\Debug\es\Microsoft.CodeAnalysis.Scripting.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-replacer\build\Debug\fr\Microsoft.CodeAnalysis.Scripting.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-replacer\build\Debug\it\Microsoft.CodeAnalysis.Scripting.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-replacer\build\Debug\ja\Microsoft.CodeAnalysis.Scripting.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-replacer\build\Debug\ko\Microsoft.CodeAnalysis.Scripting.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-replacer\build\Debug\pl\Microsoft.CodeAnalysis.Scripting.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-replacer\build\Debug\pt-BR\Microsoft.CodeAnalysis.Scripting.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-replacer\build\Debug\ru\Microsoft.CodeAnalysis.Scripting.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-replacer\build\Debug\tr\Microsoft.CodeAnalysis.Scripting.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-replacer\build\Debug\zh-Hans\Microsoft.CodeAnalysis.Scripting.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-replacer\build\Debug\zh-Hant\Microsoft.CodeAnalysis.Scripting.resources.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-replacer\build\Debug\localizationlib.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-replacer\build\Debug\localizationlib.pdb
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-replacer\build\obj\Debug\localization_replacer.csproj.AssemblyReference.cache
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-replacer\build\obj\Debug\localization_replacer.GeneratedMSBuildEditorConfig.editorconfig
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-replacer\build\obj\Debug\localization_replacer.AssemblyInfoInputs.cache
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-replacer\build\obj\Debug\localization_replacer.AssemblyInfo.cs
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-replacer\build\obj\Debug\localization_replacer.csproj.CoreCompileInputs.cache
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-replacer\build\obj\Debug\localization_replacer.sourcelink.json
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-replacer\build\obj\Debug\localiza.1E83C21B.Up2Date
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-replacer\build\obj\Debug\localization_replacer.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-replacer\build\obj\Debug\refint\localization_replacer.dll
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-replacer\build\obj\Debug\localization_replacer.pdb
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-replacer\build\obj\Debug\localization_replacer.genruntimeconfig.cache
D:\kibisoft\workarea\git\interop\libs\translate\translate-tools\localization-replacer\build\obj\Debug\ref\localization_replacer.dll
