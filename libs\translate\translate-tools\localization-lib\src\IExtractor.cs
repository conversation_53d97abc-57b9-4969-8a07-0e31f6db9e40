/*
 * Copyright 2021-present Alcon. All rights reserved.
 * Alcon proprietary/confidential.
 * Use is subject to license terms.
 */

using System.Collections.Generic;

namespace LocalizationLib;

public interface IExtractor
{
    /// <summary>
    /// Extract strings from the given project path. 
    /// Returns (fileResults, replacementRecords).
    /// </summary>
    (List<FileProcessingResult> fileResults, List<ReplacementRecord> replacements) 
        Extract(string projectPath);
}
