{"llmProvider": "BedrockProvider", "promptTemplate": "You are analyzing C# code from a WPF application (file: {{fileName}}). Your task is to identify user-facing text that needs translation. Consider these guidelines:\n\n1. Look for strings that are meaningful to end users, such as:\n   - UI labels, buttons, and messages\n   - Error messages and notifications\n   - Help text and tooltips\n   - Menu items and dialog text\n\n2. Skip strings that should NOT be translated:\n   - Already wrapped with Translator.Instance.TranslateM(...)\n   - Technical identifiers or internal strings\n   - Log messages or debug text\n   - File paths or system paths\n   - Regular expressions or patterns\n   - Configuration keys or property names\n   - Empty strings or whitespace-only strings\n   - Strings containing only punctuation or symbols\n\n3. For each translatable string found, provide:\n   - lineNumber: The line index in the file\n   - column: The exact column where the string starts\n   - originalValue: The string literal to be translated\n   - reason: A brief explanation of why it's user-facing text\n   - lineText: The complete line containing the string\n\n4. Special cases to handle:\n   - If multiple translatable strings appear on the same line, include each separately\n   - For string interpolation ($\"...\"), analyze the interpolated parts\n   - For verbatim strings (@\"...\"), treat them like regular strings\n   - For raw strings ($$\"...\"), analyze the content", "bedrockProviderConfig": {"region": "us-east-1", "modelId": "anthropic.claude-3-sonnet-20240229-v1:0", "temperature": 0.0, "maxRetries": 8, "timeoutSeconds": 300, "useChunking": true, "chunkSize": 2000, "maxTokens": 4096, "enableDebugLogging": true, "enableMetrics": true, "accessKey": "YOUR_AWS_ACCESS_KEY", "secretKey": "YOUR_AWS_SECRET_KEY"}}