/*
 * Copyright 2021-present Alcon. All rights reserved.
 * Alcon proprietary/confidential.
 * Use is subject to license terms.
 */

using System;
using System.IO;
using System.Text.Json;

namespace LocalizationLib;
public static class LLMConfigLoader
{
    public static LLMConfig LoadLlmConfig(string path)
    {
        if (!File.Exists(path))
        {
            Console.Error.WriteLine($"LLM config file not found: {path}");
            return null;
        }
        string json = File.ReadAllText(path);
        return JsonSerializer.Deserialize<LLMConfig>(json);
    }
}

public class LLMConfig
{
    // Which provider type to use, e.g. "LocalLlmProvider", "BedrockProvider"
    public string llmProvider { get; set; }

    // Partial user prompt to embed
    public string promptTemplate { get; set; }

    // Settings for the local LLM
    public LocalLlmProviderConfig localLlmProviderConfig { get; set; }

    // Settings for Amazon Bedrock
    public BedrockProviderConfig bedrockProviderConfig { get; set; }
}

public class LocalLlmProviderConfig
{
    public string serviceUrl { get; set; }
    public string modelId { get; set; }
    public double temperature { get; set; }
    public int maxRetries { get; set; }
    public int timeoutSeconds { get; set; }
    public int chunkSize { get; set; } = 300;
    public bool useChunking { get; set; }
}

public class BedrockProviderConfig
{
    public string region { get; set; }
    public string modelId { get; set; }
    public double temperature { get; set; }
    public int maxRetries { get; set; }
    public int timeoutSeconds { get; set; }
    public int chunkSize { get; set; } = 300;
    public bool useChunking { get; set; }
    public string accessKey { get; set; }
    public string secretKey { get; set; }
    public string sessionToken { get; set; }
    public string profileName { get; set; }
    public int maxTokens { get; set; } = 4096;
    public bool enableDebugLogging { get; set; } = false;
    public bool enableMetrics { get; set; } = true;
}